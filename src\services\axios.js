import store from '@/store'
import usuariosService from "@/services/usuariosService"
const { API_URL } = require('@/config.json')
import axios from 'axios'
import { jwtDecode } from 'jwt-decode';
// import { history } from '../index.js'
// import { userService } from './user'
// import { jwtService } from './jwt'

// DEBUG ONLY:
// localStorage.removeItem('token');
// localStorage.removeItem('contracts');
// localStorage.removeItem('selectedContract');
// localStorage.removeItem('name');
// localStorage.removeItem('firstName');
axios.defaults.timeout = 400000;
axios.defaults.withCredentials = true;
axios.defaults.baseURL = API_URL;
axios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded';

// Flag para controlar se há um refresh em andamento
let isRefreshing = false;
// Fila de requisições que falharam e estão aguardando o refresh
let failedQueue = [];

// Configuração de retry para erros 500
const RETRY_CONFIG = {
    maxRetries: 3,
    retryDelay: 1000, // 1 segundo
    retryableErrors: ['Secret is not set', 'Internal Server Error', 'Server Error']
};

// Função para processar a fila de requisições após o refresh
const processQueue = (error, token = null) => {
    failedQueue.forEach(prom => {
        if (error) {
            prom.reject(error);
        } else {
            prom.resolve(token);
        }
    });

    failedQueue = [];
};

// Função para fazer retry com delay exponencial
const retryRequest = async (config, retryCount = 0) => {
    if (retryCount >= RETRY_CONFIG.maxRetries) {
        return Promise.reject(new Error('Max retries exceeded'));
    }

    // Delay exponencial: 1s, 2s, 4s
    const delay = RETRY_CONFIG.retryDelay * Math.pow(2, retryCount);

    console.log(`Retry ${retryCount + 1}/${RETRY_CONFIG.maxRetries} após ${delay}ms para ${config.url}`);

    await new Promise(resolve => setTimeout(resolve, delay));

    return axios(config);
};

axios.refreshToken = (token = null) => {
    if (token) {
        // Salvar token no localStorage
        localStorage.setItem('token', token);

        // Decodificar e salvar no Vuex
        try {
            const decodedToken = jwtDecode(token);
            store.commit('setToken', decodedToken);
        } catch (error) {
            console.error('Error decoding token:', error);
        }

        // Atualizar header do axios IMEDIATAMENTE
        axios.defaults.headers.common['Authorization'] = 'Bearer ' + token;
    } else {
        // If no token is provided, check if there's one in localStorage
        const storedToken = localStorage.getItem('token');
        if (storedToken) {
            try {
                const decodedToken = jwtDecode(storedToken);
                store.commit('setToken', decodedToken);
                axios.defaults.headers.common['Authorization'] = 'Bearer ' + storedToken;
            } catch (error) {
                console.error('Error decoding token from localStorage:', error);
            }
        } else {
            // Se não há token, limpar o header
            delete axios.defaults.headers.common['Authorization'];
        }
    }
}

if (!axios.defaults.headers.common['Authorization'])
    axios.refreshToken()

// axios.generateTempToken = async () => {
// 	const now = parseInt(new Date().getTime() / 1000);

// 	const tempToken = localStorage.getItem('tempToken')

// 	let tempTokenData

// 	if (tempToken)
// 		tempTokenData = jwtService.decode(tempToken)

// 	if (!tempToken || now > tempTokenData.payload.exp) {
// 		const response = await axios.get('/user/temptoken')

// 		if (!response)
// 			return false;

// 		localStorage.setItem('tempToken', response.data.token);
// 	}

// 	axios.refreshToken()
// }

// axios.refreshToken()

axios.interceptors.request.use(request => {
    // console.log(request);
    // Edit request config
    return request;
}, error => {
    console.log(error);
    return Promise.reject(error);
});

axios.interceptors.response.use(response => {
    // Verificar se é uma rota de notificações e se a resposta pode ter problemas de JSON
    if (response.config.url && response.config.url.includes('/notifications')) {
        try {
            // Se a resposta for uma string, tentar fazer parse
            if (typeof response.data === 'string') {
                // Verificar se parece ser JSON malformado (múltiplos objetos concatenados)
                if (response.data.includes('}{')) {
                    console.warn('Resposta JSON malformada detectada para notificações:', response.data)
                    // Tentar extrair o primeiro objeto JSON válido
                    const firstJsonMatch = response.data.match(/^{[^}]*}/)
                    if (firstJsonMatch) {
                        response.data = JSON.parse(firstJsonMatch[0])
                        console.warn('JSON corrigido automaticamente:', response.data)
                    } else {
                        // Se não conseguir extrair, retornar erro padrão
                        response.data = { status: 'error', message: 'Server Error' }
                    }
                } else {
                    // Tentar fazer parse normal
                    response.data = JSON.parse(response.data)
                }
            }
        } catch (parseError) {
            console.warn('Erro ao processar resposta de notificações:', parseError)
            // Em caso de erro de parse, retornar estrutura de erro padrão
            response.data = { status: 'error', message: 'Server Error' }
        }
    }

    return response;
}, error => {
    const originalRequest = error.config;

    if (!error.response) {
        // Handle API Offline Error
        return Promise.reject(error);
    }

    // ========== TRATAMENTO DE ERRO 500 (Secret is not set, etc) ==========
    // ⚠️ IMPORTANTE: Apenas retentar requisições GET (idempotentes)
    // Requisições de escrita (POST, PUT, DELETE, PATCH) NUNCA devem ser retentadas
    // para evitar duplicação de dados ou inconsistências no banco de dados
    if (error.response.status === 500) {
        const errorMessage = error.response.data?.message || '';
        const isRetryableError = RETRY_CONFIG.retryableErrors.some(msg => errorMessage.includes(msg));
        const isGetRequest = originalRequest.method && originalRequest.method.toUpperCase() === 'GET';

        if (isRetryableError && isGetRequest && !originalRequest._retryCount) {
            originalRequest._retryCount = 0;
        }

        if (isRetryableError && isGetRequest && originalRequest._retryCount < RETRY_CONFIG.maxRetries) {
            originalRequest._retryCount++;

            console.warn(`Erro 500 detectado em GET: "${errorMessage}". Tentando novamente (${originalRequest._retryCount}/${RETRY_CONFIG.maxRetries})...`);

            return retryRequest(originalRequest, originalRequest._retryCount - 1);
        }
    }

    // ========== TRATAMENTO DE ERRO 401 (Token expirado, blacklisted, etc) ==========
    if (error.response.status === 401) {
        const errorMessage = error.response.data?.message || '';

        // Se o token expirou, fazer logout direto
        if (errorMessage.includes('Token has expired') || errorMessage.includes('token_expired')) {
            console.warn('Token expirado detectado. Fazendo logout...');
            usuariosService.logout();
            return Promise.reject(error);
        }

        // Se o token foi blacklisted e não é uma requisição de refresh
        if (errorMessage.includes('blacklisted') && !originalRequest._retry && originalRequest.url !== '/auth/refresh') {
            originalRequest._retry = true;

            if (isRefreshing) {
                // Se já está fazendo refresh, adicionar à fila
                return new Promise((resolve, reject) => {
                    failedQueue.push({ resolve, reject });
                }).then(token => {
                    originalRequest.headers['Authorization'] = 'Bearer ' + token;
                    return axios(originalRequest);
                }).catch(err => {
                    return Promise.reject(err);
                });
            }

            isRefreshing = true;

            // Tentar fazer refresh do token
            return new Promise((resolve, reject) => {
                axios.post('/auth/refresh')
                    .then(response => {
                        if (response && response.data && response.data.access_token) {
                            const newToken = response.data.access_token;
                            axios.refreshToken(newToken);
                            originalRequest.headers['Authorization'] = 'Bearer ' + newToken;
                            processQueue(null, newToken);
                            resolve(axios(originalRequest));
                        } else {
                            processQueue(new Error('Refresh failed'), null);
                            usuariosService.logout();
                            reject(error);
                        }
                    })
                    .catch(err => {
                        processQueue(err, null);
                        usuariosService.logout();
                        reject(err);
                    })
                    .finally(() => {
                        isRefreshing = false;
                    });
            });
        }

        // Para outros erros 401, fazer logout
        usuariosService.logout();
    }

    return Promise.reject(error);
})

export default axios
