<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Adicionar 'pendente' ao ENUM de status
        DB::statement("ALTER TABLE `orcamentos` MODIFY COLUMN `status` ENUM('rascunho', 'pendente', 'enviado', 'aprovado', 'rejeitado', 'expirado', 'convertido') NOT NULL DEFAULT 'rascunho'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remover 'pendente' do ENUM de status
        DB::statement("ALTER TABLE `orcamentos` MODIFY COLUMN `status` ENUM('rascunho', 'enviado', 'aprovado', 'rejeitado', 'expirado', 'convertido') NOT NULL DEFAULT 'rascunho'");
    }
};
