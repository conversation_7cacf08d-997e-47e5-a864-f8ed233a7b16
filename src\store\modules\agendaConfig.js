// Store module para gerenciar configurações da agenda por consultório
const state = {
  // Configurações por consultório (chave: consultorioId, valor: config)
  configsByConsultorio: {},

  // Consultório atualmente selecionado
  selectedConsultorioId: null,

  // Lista de consultórios disponíveis
  consultorios: [],

  // Estados de carregamento
  isLoaded: false,
  isLoadingConsultorios: false,

  // Sistema de Cache Local
  cache: {
    configs: {}, // { consultorioId: { data, timestamp, ttl } }
    lastFullUpdate: null, // Timestamp da última atualização completa
    updateInterval: 60000, // 1 minuto (60 segundos)
    ttl: 300000, // 5 minutos (TTL do cache)
    isUpdating: false, // Flag para evitar múltiplas atualizações simultâneas
    updateTimer: null // Referência do timer de atualização automática
  }
};

const mutations = {
  SET_CONSULTORIOS(state, consultorios) {
    state.consultorios = consultorios;
    state.isLoadingConsultorios = false;
  },

  SET_LOADING_CONSULTORIOS(state, loading) {
    state.isLoadingConsultorios = loading;
  },

  SET_SELECTED_CONSULTORIO(state, consultorioId) {
    state.selectedConsultorioId = consultorioId;
  },

  SET_AGENDA_CONFIG_FOR_CONSULTORIO(state, { consultorioId, config }) {
    if (!state.configsByConsultorio[consultorioId]) {
      state.configsByConsultorio[consultorioId] = {};
    }
    state.configsByConsultorio[consultorioId] = { ...state.configsByConsultorio[consultorioId], ...config };
    state.isLoaded = true;
  },

  UPDATE_AGENDA_CONFIG(state, updates) {
    state.config = { ...state.config, ...updates };
  },

  // Mutation para compatibilidade com código antigo
  SET_AGENDA_CONFIG(state, config) {
    state.config = config;
    state.isLoaded = true;
  },

  // Mutation para compatibilidade com código antigo
  SET_TIME_SLOTS(state, timeSlots) {
    if (state.config) {
      state.config.time_slots = timeSlots;
    }
  },

  SET_TIME_SLOTS_FOR_CONSULTORIO(state, { consultorioId, timeSlots }) {
    if (!state.configsByConsultorio[consultorioId]) {
      state.configsByConsultorio[consultorioId] = {};
    }
    state.configsByConsultorio[consultorioId].time_slots = timeSlots;
  },

  RESET_AGENDA_CONFIG_FOR_CONSULTORIO(state, consultorioId) {
    const defaultConfig = {
      horario_inicio: '08:00',
      horario_fim: '18:00',
      dias_semana: ['segunda', 'terca', 'quarta', 'quinta', 'sexta'],
      duracao_padrao_consulta: 30,
      permitir_duracao_personalizada: true,
      intervalo_entre_consultas: 0,
      tem_horario_almoco: false,
      horario_almoco_inicio: null,
      horario_almoco_fim: null,
      permitir_agendamento_passado: false,
      permitir_agendamento_feriados: false,
      antecedencia_minima_agendamento: 0,
      antecedencia_maxima_agendamento: 720,
      time_slots: []
    };
    state.configsByConsultorio[consultorioId] = defaultConfig;
  },

  CLEAR_ALL_CONFIGS(state) {
    state.configsByConsultorio = {};
    state.selectedConsultorioId = null;
    state.isLoaded = false;
  },

  // Mutations para Cache Local
  SET_CACHE_CONFIG(state, { consultorioId, data, timestamp = Date.now(), ttl = state.cache.ttl }) {
    state.cache.configs[consultorioId] = {
      data: { ...data },
      timestamp,
      ttl
    };
    console.log(`💾 Cache atualizado para consultório ${consultorioId}`);
  },

  CLEAR_CACHE_CONFIG(state, consultorioId) {
    if (state.cache.configs[consultorioId]) {
      delete state.cache.configs[consultorioId];
      console.log(`🗑️ Cache removido para consultório ${consultorioId}`);
    }
  },

  CLEAR_ALL_CACHE(state) {
    state.cache.configs = {};
    state.cache.lastFullUpdate = null;
    console.log('🗑️ Todo cache foi limpo');
  },

  SET_CACHE_LAST_UPDATE(state, timestamp = Date.now()) {
    state.cache.lastFullUpdate = timestamp;
  },

  SET_CACHE_UPDATING(state, isUpdating) {
    state.cache.isUpdating = isUpdating;
  },

  SET_CACHE_TIMER(state, timerId) {
    state.cache.updateTimer = timerId;
  },

  CLEAR_CACHE_TIMER(state) {
    if (state.cache.updateTimer) {
      clearInterval(state.cache.updateTimer);
      state.cache.updateTimer = null;
      console.log('⏰ Timer de atualização automática parado');
    }
  }
};

const actions = {
  async loadConsultorios({ commit, state }) {
    try {
      commit('SET_LOADING_CONSULTORIOS', true);

      const consultorioService = (await import('@/services/consultorioService.js')).default;
      const consultorios = await consultorioService.getConsultorios();

      console.log('📋 Consultórios carregados da API:', consultorios);
      commit('SET_CONSULTORIOS', consultorios || []);

      // IMPORTANTE: Não sobrescrever se já houver um consultório selecionado
      // Isso permite que o localStorage funcione corretamente
      if (consultorios && consultorios.length > 0 && !state.selectedConsultorioId) {
        console.log('🔍 Nenhum consultório selecionado, tentando restaurar do localStorage...');

        // Tentar restaurar do localStorage primeiro
        try {
          const savedConsultorioId = localStorage.getItem('agenda_ultimo_consultorio');
          console.log('📂 Valor do localStorage:', savedConsultorioId);

          if (savedConsultorioId) {
            const consultorioId = parseInt(savedConsultorioId, 10);
            const consultorioExiste = consultorios.some(c => c.id === consultorioId);

            console.log('🔍 Consultório existe?', consultorioExiste, 'ID:', consultorioId);

            if (consultorioExiste) {
              console.log('✅ Restaurando consultório do localStorage:', consultorioId);
              commit('SET_SELECTED_CONSULTORIO', consultorioId);
              return consultorios;
            } else {
              console.log('⚠️ Consultório salvo não existe mais na lista');
            }
          } else {
            console.log('⚠️ Nenhum consultório salvo no localStorage');
          }
        } catch (error) {
          console.warn('⚠️ Erro ao restaurar consultório do localStorage:', error);
        }

        // Se não conseguiu restaurar, selecionar o primeiro
        console.log('🔄 Selecionando primeiro consultório disponível:', consultorios[0].id);
        commit('SET_SELECTED_CONSULTORIO', consultorios[0].id);
      } else if (state.selectedConsultorioId) {
        console.log('✅ Consultório já estava selecionado:', state.selectedConsultorioId);
      }

      return consultorios;
    } catch (error) {
      console.error('❌ Erro ao carregar consultórios:', error);
      commit('SET_LOADING_CONSULTORIOS', false);
      return [];
    }
  },

  async loadAgendaConfigForConsultorio({ commit }, { consultorioId, config }) {
    try {
      console.log('🔄 loadAgendaConfigForConsultorio chamado com:', { consultorioId, config });

      if (config) {
        console.log('📋 Usando configuração passada como parâmetro:', config);
        commit('SET_AGENDA_CONFIG_FOR_CONSULTORIO', { consultorioId, config });
        return true;
      }

      // Se não foi passada configuração, tentar carregar da API
      console.log('📡 Carregando configuração da API para consultório:', consultorioId);
      const agendaConfigService = (await import('@/services/agendaConfigService.js')).default;
      const loadedConfig = await agendaConfigService.getAgendaConfig(consultorioId);

      console.log('📋 Configuração carregada da API:', loadedConfig);

      if (loadedConfig) {
        console.log('✅ Salvando configuração no store para consultório:', consultorioId);
        commit('SET_AGENDA_CONFIG_FOR_CONSULTORIO', { consultorioId, config: loadedConfig });
        return true;
      }

      console.log('❌ Nenhuma configuração foi carregada da API');
      return false;
    } catch (error) {
      console.error('❌ Erro ao carregar configurações da agenda:', error);
      return false;
    }
  },

  // Manter compatibilidade com código existente
  async loadAgendaConfig({ commit, dispatch, state }, config) {
    try {
      // Se não há configuração, não fazer nada (usuário novo sem config ainda)
      if (!config) {
        console.log('Nenhuma configuração de agenda fornecida (usuário novo)');
        return true;
      }

      // Salvar config global para compatibilidade
      commit('SET_AGENDA_CONFIG', config);

      // Se há consultório selecionado, carregar para ele também
      if (state.selectedConsultorioId) {
        return await dispatch('loadAgendaConfigForConsultorio', {
          consultorioId: state.selectedConsultorioId,
          config
        });
      }

      return true;
    } catch (error) {
      console.error('Erro ao carregar configuração da agenda:', error);
      return false;
    }
  },
  
  async updateAgendaConfig({ commit }, updates) {
    try {
      const axios = (await import('@/services/axios.js')).default;
      const response = await axios.patch('/agenda-config', updates);
      
      if (response.data && response.data.status === 'success') {
        commit('UPDATE_AGENDA_CONFIG', response.data.data);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Erro ao atualizar configurações da agenda:', error);
      throw error;
    }
  },
  
  async resetAgendaConfig({ commit }) {
    try {
      const axios = (await import('@/services/axios.js')).default;
      const response = await axios.post('/agenda-config/reset');
      
      if (response.data && response.data.status === 'success') {
        commit('SET_AGENDA_CONFIG', response.data.data);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Erro ao resetar configurações da agenda:', error);
      throw error;
    }
  },
  
  generateTimeSlots({ state, commit }) {
    try {
      // Usar o consultório selecionado ou o primeiro disponível
      const selectedConsultorioId = state.selectedConsultorioId;
      if (!selectedConsultorioId) {
        console.warn('Nenhum consultório selecionado para gerar horários');
        return;
      }

      const config = state.configsByConsultorio[selectedConsultorioId];
      if (!config) {
        console.warn('Configuração não encontrada para o consultório:', selectedConsultorioId);
        return;
      }

      const slots = [];

      // Verificar se há configurações por dia ou usar formato antigo
      let horarioConfig = null;

      if (config.configuracoes_por_dia && typeof config.configuracoes_por_dia === 'object') {
        const dias = Object.keys(config.configuracoes_por_dia);
        if (dias.length > 0) {
          horarioConfig = config.configuracoes_por_dia[dias[0]];
        }
      } else if (config.horario_inicio && config.horario_fim) {
        // Fallback para formato antigo
        horarioConfig = {
          horario_inicio: config.horario_inicio,
          horario_fim: config.horario_fim,
          tem_horario_almoco: config.tem_horario_almoco,
          horario_almoco_inicio: config.horario_almoco_inicio,
          horario_almoco_fim: config.horario_almoco_fim
        };
      }

      if (!horarioConfig || !horarioConfig.horario_inicio || !horarioConfig.horario_fim) {
        commit('SET_TIME_SLOTS_FOR_CONSULTORIO', { consultorioId: selectedConsultorioId, timeSlots: [] });
        return [];
      }

      // Converter horários para objetos Date
      const [horaInicio, minutoInicio] = horarioConfig.horario_inicio.split(':').map(Number);
      const [horaFim, minutoFim] = horarioConfig.horario_fim.split(':').map(Number);

      const inicio = new Date();
      inicio.setHours(horaInicio, minutoInicio, 0, 0);

      const fim = new Date();
      fim.setHours(horaFim, minutoFim, 0, 0);

      const current = new Date(inicio);

      while (current < fim) {
        // Verificar se não está no horário de almoço
        if (horarioConfig.tem_horario_almoco && horarioConfig.horario_almoco_inicio && horarioConfig.horario_almoco_fim) {
          const [horaAlmocoInicio, minutoAlmocoInicio] = horarioConfig.horario_almoco_inicio.split(':').map(Number);
          const [horaAlmocoFim, minutoAlmocoFim] = horarioConfig.horario_almoco_fim.split(':').map(Number);

          const almocoInicio = new Date();
          almocoInicio.setHours(horaAlmocoInicio, minutoAlmocoInicio, 0, 0);

          const almocoFim = new Date();
          almocoFim.setHours(horaAlmocoFim, minutoAlmocoFim, 0, 0);

          if (current >= almocoInicio && current < almocoFim) {
            const duracao = config.duracao_padrao_consulta || 30;
            const intervalo = config.intervalo_entre_consultas || 0;
            current.setTime(current.getTime() + (duracao + intervalo) * 60000);
            continue;
          }
        }

        const timeString = current.toTimeString().substring(0, 5);
        slots.push(timeString);

        // Adicionar duração da consulta + intervalo
        const duracao = config.duracao_padrao_consulta || 30;
        const intervalo = config.intervalo_entre_consultas || 0;
        current.setTime(current.getTime() + (duracao + intervalo) * 60000);
      }
      
      commit('SET_TIME_SLOTS_FOR_CONSULTORIO', { consultorioId: selectedConsultorioId, timeSlots: slots });
      return slots;
    } catch (error) {
      console.error('Erro ao gerar horários:', error);
      commit('SET_TIME_SLOTS', []);
      return [];
    }
  },

  // Novos métodos para consultórios
  generateTimeSlotsForConsultorio({ commit, state }, consultorioId) {
    try {
      const config = state.configsByConsultorio[consultorioId];

      if (!config) {
        commit('SET_TIME_SLOTS_FOR_CONSULTORIO', { consultorioId, timeSlots: [] });
        return [];
      }

      console.log('Configuração do consultório:', consultorioId, config);

      const slots = [];

      // Verificar se há configurações específicas por dia
      if (config.configuracoes_por_dia && typeof config.configuracoes_por_dia === 'object') {
        // Usar configurações específicas por dia
        const diasAtivos = config.dias_semana || [];
        console.log('Dias ativos:', diasAtivos);

        // Para cada dia ativo, gerar horários baseados na configuração específica
        diasAtivos.forEach(dia => {
          const dayConfig = config.configuracoes_por_dia[dia];
          if (dayConfig && dayConfig.horario_inicio && dayConfig.horario_fim) {
            console.log(`Configuração para ${dia}:`, dayConfig);

            const [startHour, startMinute] = dayConfig.horario_inicio.split(':').map(Number);
            const [endHour, endMinute] = dayConfig.horario_fim.split(':').map(Number);

            const start = new Date();
            start.setHours(startHour, startMinute, 0, 0);

            const end = new Date();
            end.setHours(endHour, endMinute, 0, 0);

            const current = new Date(start);

            while (current < end) {
              const timeString = current.toTimeString().substring(0, 5);

              // Verificar se não está no horário de almoço para este dia
              let isLunchTime = false;
              if (dayConfig.tem_horario_almoco && dayConfig.horario_almoco_inicio && dayConfig.horario_almoco_fim) {
                const [lunchStartHour, lunchStartMinute] = dayConfig.horario_almoco_inicio.split(':').map(Number);
                const [lunchEndHour, lunchEndMinute] = dayConfig.horario_almoco_fim.split(':').map(Number);

                const currentMinutes = current.getHours() * 60 + current.getMinutes();
                const lunchStartMinutes = lunchStartHour * 60 + lunchStartMinute;
                const lunchEndMinutes = lunchEndHour * 60 + lunchEndMinute;

                isLunchTime = currentMinutes >= lunchStartMinutes && currentMinutes < lunchEndMinutes;
              }

              if (!isLunchTime && !slots.includes(timeString)) {
                slots.push(timeString);
              }

              // Incrementar pela duração da consulta (padrão 30 minutos)
              const duration = config.duracao_padrao_consulta || 30;
              current.setMinutes(current.getMinutes() + duration);
            }
          }
        });
      } else {
        // Fallback para configuração geral (formato antigo)
        if (!config.horario_inicio || !config.horario_fim) {
          commit('SET_TIME_SLOTS_FOR_CONSULTORIO', { consultorioId, timeSlots: [] });
          return [];
        }

        const [startHour, startMinute] = config.horario_inicio.split(':').map(Number);
        const [endHour, endMinute] = config.horario_fim.split(':').map(Number);

        const start = new Date();
        start.setHours(startHour, startMinute, 0, 0);

        const end = new Date();
        end.setHours(endHour, endMinute, 0, 0);

        const current = new Date(start);

        while (current < end) {
          const timeString = current.toTimeString().substring(0, 5);

          // Verificar se não está no horário de almoço
          let isLunchTime = false;
          if (config.tem_horario_almoco && config.horario_almoco_inicio && config.horario_almoco_fim) {
            const [lunchStartHour, lunchStartMinute] = config.horario_almoco_inicio.split(':').map(Number);
            const [lunchEndHour, lunchEndMinute] = config.horario_almoco_fim.split(':').map(Number);

            const currentMinutes = current.getHours() * 60 + current.getMinutes();
            const lunchStartMinutes = lunchStartHour * 60 + lunchStartMinute;
            const lunchEndMinutes = lunchEndHour * 60 + lunchEndMinute;

            isLunchTime = currentMinutes >= lunchStartMinutes && currentMinutes < lunchEndMinutes;
          }

          if (!isLunchTime) {
            slots.push(timeString);
          }

          // Adicionar duração da consulta + intervalo
          const intervalMinutes = (config.duracao_padrao_consulta || 30) + (config.intervalo_entre_consultas || 0);
          current.setTime(current.getTime() + intervalMinutes * 60000);
        }
      }

      // Ordenar e remover duplicatas
      const uniqueSlots = [...new Set(slots)].sort();

      console.log('Horários gerados para consultório', consultorioId, ':', uniqueSlots);
      commit('SET_TIME_SLOTS_FOR_CONSULTORIO', { consultorioId, timeSlots: uniqueSlots });
      return uniqueSlots;
    } catch (error) {
      console.error('Erro ao gerar horários para consultório:', error);
      commit('SET_TIME_SLOTS_FOR_CONSULTORIO', { consultorioId, timeSlots: [] });
      return [];
    }
  },

  selectConsultorio({ commit, dispatch }, consultorioId) {
    commit('SET_SELECTED_CONSULTORIO', consultorioId);
    dispatch('generateTimeSlotsForConsultorio', consultorioId);
  },

  // ============ SISTEMA DE CACHE LOCAL ============

  /**
   * Carrega configuração da agenda com cache inteligente
   * Estratégia: Cache-First com atualização em background
   */
  async loadAgendaConfigForConsultorioCached({ state, commit, dispatch }, { consultorioId, forceRefresh = false }) {
    try {
      console.log(`🔄 loadAgendaConfigForConsultorioCached chamado para consultório ${consultorioId}, forceRefresh: ${forceRefresh}`);

      const now = Date.now();
      const cachedConfig = state.cache.configs[consultorioId];

      // Verificar se temos dados em cache válidos
      if (!forceRefresh && cachedConfig && (now - cachedConfig.timestamp) < cachedConfig.ttl) {
        console.log(`✅ Usando dados do cache para consultório ${consultorioId} (válido por ${(cachedConfig.ttl - (now - cachedConfig.timestamp)) / 1000}s)`);

        // Usar dados do cache
        commit('SET_AGENDA_CONFIG_FOR_CONSULTORIO', { consultorioId, config: cachedConfig.data });
        dispatch('generateTimeSlotsForConsultorio', consultorioId);

        // Atualizar em background se necessário (cache stale)
        const timeSinceUpdate = now - cachedConfig.timestamp;
        if (timeSinceUpdate > state.cache.updateInterval) {
          console.log(`🔄 Cache stale, atualizando em background para consultório ${consultorioId}`);
          dispatch('updateCacheForConsultorio', consultorioId).catch(error => {
            console.warn(`⚠️ Falha ao atualizar cache em background para consultório ${consultorioId}:`, error);
          });
        }

        return true;
      }

      // Cache miss ou expirado - buscar da API
      console.log(`📡 Cache miss/expirado para consultório ${consultorioId}, buscando da API`);
      const success = await dispatch('loadAgendaConfigForConsultorio', { consultorioId });

      if (success) {
        // Salvar no cache se a busca foi bem-sucedida
        const config = state.configsByConsultorio[consultorioId];
        if (config) {
          commit('SET_CACHE_CONFIG', { consultorioId, data: config, timestamp: now });
        }
      }

      return success;
    } catch (error) {
      console.error(`❌ Erro em loadAgendaConfigForConsultorioCached para consultório ${consultorioId}:`, error);
      return false;
    }
  },

  /**
   * Atualiza cache para um consultório específico
   */
  async updateCacheForConsultorio({ commit, dispatch }, consultorioId) {
    try {
      console.log(`🔄 Atualizando cache para consultório ${consultorioId}`);

      const success = await dispatch('loadAgendaConfigForConsultorio', { consultorioId });

      if (success) {
        const config = state.configsByConsultorio[consultorioId];
        if (config) {
          commit('SET_CACHE_CONFIG', { consultorioId, data: config });
          console.log(`✅ Cache atualizado com sucesso para consultório ${consultorioId}`);
        }
      }

      return success;
    } catch (error) {
      console.error(`❌ Erro ao atualizar cache para consultório ${consultorioId}:`, error);
      return false;
    }
  },

  /**
   * Carrega todas as configurações de agenda de uma vez (bulk load)
   * Requer que os consultórios já estejam carregados no store
   */
  async loadAllAgendaConfigsBulk({ state, commit, dispatch }) {
    try {
      console.log('🔄 Carregando todas as configurações de agenda em bulk');

      // Verificar se há consultórios carregados
      if (!state.consultorios || state.consultorios.length === 0) {
        console.warn('⚠️ Nenhum consultório disponível. Carregando consultórios primeiro...');
        await dispatch('loadConsultorios');
      }

      if (!state.consultorios || state.consultorios.length === 0) {
        console.warn('⚠️ Ainda não há consultórios disponíveis após tentativa de carregamento');
        return false;
      }

      // Extrair IDs dos consultórios
      const consultorioIds = state.consultorios.map(c => c.id);
      console.log('📋 Consultórios a carregar:', consultorioIds);

      const agendaConfigService = (await import('@/services/agendaConfigService.js')).default;
      const allConfigs = await agendaConfigService.getAllAgendaConfigs(consultorioIds);

      if (!allConfigs || typeof allConfigs !== 'object') {
        console.warn('⚠️ Nenhuma configuração retornada da API bulk');
        return false;
      }

      console.log(`📦 Recebidas configurações para ${Object.keys(allConfigs).length} consultórios`);

      // Salvar todas as configurações no store e cache
      const now = Date.now();
      let successCount = 0;

      Object.entries(allConfigs).forEach(([consultorioId, config]) => {
        try {
          // Salvar no store
          commit('SET_AGENDA_CONFIG_FOR_CONSULTORIO', { consultorioId, config });

          // Salvar no cache
          commit('SET_CACHE_CONFIG', { consultorioId, data: config, timestamp: now });

          // Gerar time slots
          dispatch('generateTimeSlotsForConsultorio', consultorioId);

          successCount++;
          console.log(`✅ Configuração carregada para consultório ${consultorioId}`);
        } catch (error) {
          console.warn(`⚠️ Erro ao processar configuração para consultório ${consultorioId}:`, error);
        }
      });

      commit('SET_CACHE_LAST_UPDATE');
      console.log(`✅ Bulk load finalizado: ${successCount} configurações processadas`);

      return successCount > 0;
    } catch (error) {
      console.error('❌ Erro no bulk load de configurações:', error);
      return false;
    }
  },

  /**
   * Atualiza cache para todos os consultórios
   */
  async updateAllCache({ state, dispatch, commit }) {
    if (state.cache.isUpdating) {
      console.log('⏳ Atualização de cache já em andamento, pulando...');
      return false;
    }

    try {
      commit('SET_CACHE_UPDATING', true);
      console.log('🔄 Iniciando atualização completa do cache');

      // Tentar bulk load primeiro
      const bulkSuccess = await dispatch('loadAllAgendaConfigsBulk');
      if (bulkSuccess) {
        console.log('✅ Cache atualizado via bulk load');
        return true;
      }

      // Fallback para carregamento individual
      console.log('🔄 Fallback: atualizando cache individualmente');
      const consultorios = state.consultorios || [];
      let successCount = 0;

      for (const consultorio of consultorios) {
        try {
          const success = await dispatch('updateCacheForConsultorio', consultorio.id);
          if (success) successCount++;
        } catch (error) {
          console.warn(`⚠️ Falha ao atualizar cache para consultório ${consultorio.id}:`, error);
        }
      }

      commit('SET_CACHE_LAST_UPDATE');
      console.log(`✅ Atualização completa do cache finalizada: ${successCount}/${consultorios.length} consultórios atualizados`);

      return successCount > 0;
    } catch (error) {
      console.error('❌ Erro na atualização completa do cache:', error);
      return false;
    } finally {
      commit('SET_CACHE_UPDATING', false);
    }
  },

  /**
   * Inicia o sistema de atualização automática do cache
   */
  startCacheAutoUpdate({ state, dispatch, commit }) {
    if (state.cache.updateTimer) {
      console.log('⏰ Sistema de atualização automática já está ativo');
      return;
    }

    console.log(`⏰ Iniciando sistema de atualização automática do cache (intervalo: ${state.cache.updateInterval / 1000}s)`);

    const timerId = setInterval(() => {
      dispatch('updateAllCache').catch(error => {
        console.error('❌ Erro na atualização automática do cache:', error);
      });
    }, state.cache.updateInterval);

    commit('SET_CACHE_TIMER', timerId);
  },

  /**
   * Para o sistema de atualização automática do cache
   */
  stopCacheAutoUpdate({ commit }) {
    commit('CLEAR_CACHE_TIMER');
  },

  /**
   * Limpa cache para um consultório específico
   */
  clearCacheForConsultorio({ commit }, consultorioId) {
    commit('CLEAR_CACHE_CONFIG', consultorioId);
  },

  /**
   * Limpa todo o cache
   */
  clearAllCache({ commit }) {
    commit('CLEAR_ALL_CACHE');
  },

  /**
   * Força atualização completa do cache
   */
  async forceCacheRefresh({ dispatch }) {
    console.log('🔄 Forçando atualização completa do cache');
    return await dispatch('updateAllCache');
  },

  /**
   * Obtém estatísticas do cache
   */
  getCacheStats(state) {
    const now = Date.now();
    const stats = {
      totalConfigs: Object.keys(state.cache.configs).length,
      lastFullUpdate: state.cache.lastFullUpdate,
      isUpdating: state.cache.isUpdating,
      configs: {}
    };

    Object.keys(state.cache.configs).forEach(consultorioId => {
      const cached = state.cache.configs[consultorioId];
      const age = now - cached.timestamp;
      const isExpired = age > cached.ttl;

      stats.configs[consultorioId] = {
        age: Math.round(age / 1000), // em segundos
        ttl: Math.round(cached.ttl / 1000), // em segundos
        isExpired,
        timeToExpiry: isExpired ? 0 : Math.round((cached.ttl - age) / 1000)
      };
    });

    return stats;
  }
};

const getters = {
  // Getters para consultórios
  consultorios: state => state.consultorios,
  selectedConsultorioId: state => state.selectedConsultorioId,
  selectedConsultorio: state => {
    return state.consultorios.find(c => c.id === state.selectedConsultorioId) || null;
  },
  isLoadingConsultorios: state => state.isLoadingConsultorios,

  // Getters para configurações por consultório
  agendaConfigForConsultorio: state => consultorioId => {
    return state.configsByConsultorio[consultorioId] || null;
  },

  currentAgendaConfig: state => {
    if (!state.selectedConsultorioId) return null;
    return state.configsByConsultorio[state.selectedConsultorioId] || null;
  },

  timeSlotsForConsultorio: state => consultorioId => {
    const config = state.configsByConsultorio[consultorioId];
    return config ? config.time_slots || [] : [];
  },

  currentTimeSlots: state => {
    if (!state.selectedConsultorioId) return [];
    const config = state.configsByConsultorio[state.selectedConsultorioId];
    return config ? config.time_slots || [] : [];
  },

  // Getters de compatibilidade (mantém funcionamento do código existente)
  agendaConfig: state => {
    // Tentar buscar do consultório selecionado primeiro
    if (state.selectedConsultorioId) {
      return state.configsByConsultorio[state.selectedConsultorioId] || state.config;
    }
    // Fallback para config global
    return state.config;
  },

  isAgendaConfigLoaded: state => {
    // Verificar se há configuração carregada para o consultório selecionado
    if (!state.selectedConsultorioId) return false;
    const config = state.configsByConsultorio[state.selectedConsultorioId];
    return config && Object.keys(config).length > 0;
  },

  timeSlots: state => {
    // Tentar buscar do consultório selecionado primeiro
    if (state.selectedConsultorioId) {
      const config = state.configsByConsultorio[state.selectedConsultorioId];
      if (config && config.time_slots) {
        return config.time_slots;
      }
    }
    // Fallback para config global
    if (state.config && state.config.time_slots) {
      return state.config.time_slots;
    }
    return [];
  },

  // Getter para verificar se um dia da semana está ativo
  isDayActive: state => dayOfWeek => {
    // Tentar buscar do consultório selecionado primeiro
    if (state.selectedConsultorioId) {
      const config = state.configsByConsultorio[state.selectedConsultorioId];
      if (config && config.dias_semana) {
        return config.dias_semana.includes(dayOfWeek);
      }
    }
    // Fallback para config global
    if (state.config && state.config.dias_semana) {
      return state.config.dias_semana.includes(dayOfWeek);
    }
    return false;
  },

  // Getter para obter configurações formatadas para o LumiCalendar
  calendarConfig: state => {
    // Tentar buscar do consultório selecionado primeiro
    let config = null;
    if (state.selectedConsultorioId) {
      config = state.configsByConsultorio[state.selectedConsultorioId];
    }
    // Fallback para config global
    if (!config) {
      config = state.config;
    }

    if (!config) return null;

    return {
      horario_inicio: config.horario_inicio,
      horario_fim: config.horario_fim,
      dias_semana: config.dias_semana,
      duracao_padrao_consulta: config.duracao_padrao_consulta,
      time_slots: config.time_slots || []
    };
  },
  
  // Getter para verificar se está no horário de funcionamento
  isWorkingTime: state => (dayOfWeek, time) => {
    // Tentar buscar do consultório selecionado primeiro
    let config = null;
    if (state.selectedConsultorioId) {
      config = state.configsByConsultorio[state.selectedConsultorioId];
    }
    // Fallback para config global
    if (!config) {
      config = state.config;
    }

    // Se não há configuração, retornar false
    if (!config || !config.dias_semana || !config.horario_inicio || !config.horario_fim) {
      return false;
    }

    // Verificar se está dentro do horário de funcionamento
    const [hora, minuto] = time.split(':').map(Number);
    const [horaInicio, minutoInicio] = state.config.horario_inicio.split(':').map(Number);
    const [horaFim, minutoFim] = state.config.horario_fim.split(':').map(Number);

    const timeMinutes = hora * 60 + minuto;
    const inicioMinutes = horaInicio * 60 + minutoInicio;
    const fimMinutes = horaFim * 60 + minutoFim;

    if (timeMinutes < inicioMinutes || timeMinutes >= fimMinutes) {
      return false;
    }

    // Verificar se não está no horário de almoço
    if (state.config.tem_horario_almoco && state.config.horario_almoco_inicio && state.config.horario_almoco_fim) {
      const [horaAlmocoInicio, minutoAlmocoInicio] = state.config.horario_almoco_inicio.split(':').map(Number);
      const [horaAlmocoFim, minutoAlmocoFim] = state.config.horario_almoco_fim.split(':').map(Number);

      const almocoInicioMinutes = horaAlmocoInicio * 60 + minutoAlmocoInicio;
      const almocoFimMinutes = horaAlmocoFim * 60 + minutoAlmocoFim;

      if (timeMinutes >= almocoInicioMinutes && timeMinutes < almocoFimMinutes) {
        return false;
      }
    }

    return true;
  },

  // ============ GETTERS PARA CACHE LOCAL ============

  // Estatísticas do cache
  cacheStats: (state, getters) => getters.getCacheStats(state),

  // Verificar se um consultório tem cache válido
  isConsultorioCached: state => consultorioId => {
    const cached = state.cache.configs[consultorioId];
    if (!cached) return false;

    const now = Date.now();
    return (now - cached.timestamp) < cached.ttl;
  },

  // Obter idade do cache para um consultório
  cacheAgeForConsultorio: state => consultorioId => {
    const cached = state.cache.configs[consultorioId];
    if (!cached) return null;

    return Date.now() - cached.timestamp;
  },

  // Verificar se o cache está sendo atualizado
  isCacheUpdating: state => state.cache.isUpdating,

  // Verificar se o sistema de atualização automática está ativo
  isCacheAutoUpdateActive: state => !!state.cache.updateTimer
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
};
