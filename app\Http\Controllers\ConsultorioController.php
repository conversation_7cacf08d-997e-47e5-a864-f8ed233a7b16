<?php

namespace App\Http\Controllers;

use App\Models\Consultorio;
use App\Models\AgendaConfig;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class ConsultorioController extends Controller
{
    /**
     * Get all consultorios for the authenticated user's clinica
     */
    public function index(): JsonResponse
    {
        try {
            $user = Auth::user();
            $clinicaId = $user->clinica_id;

            $consultorios = Consultorio::where('clinica_id', $clinicaId)
                ->ativo()
                ->ordenado()
                ->get()
                ->map(function ($consultorio) {
                    return $consultorio->toFrontendFormat();
                });

            return response()->json([
                'status' => 'success',
                'data' => $consultorios
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Erro ao carregar consultórios',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a new consultorio
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $clinicaId = $user->clinica_id;

            $validated = $request->validate([
                'nome' => 'required|string|max:255',
                'descricao' => 'nullable|string|max:500',
                'cor' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'icone' => 'required|string|max:50',
                'ordem' => 'nullable|integer|min:0',
            ]);

            // Se não foi especificada ordem, usar a próxima disponível
            if (!isset($validated['ordem'])) {
                $maxOrdem = Consultorio::where('clinica_id', $clinicaId)->max('ordem') ?? 0;
                $validated['ordem'] = $maxOrdem + 1;
            }

            $validated['clinica_id'] = $clinicaId;
            $validated['ativo'] = true;

            $consultorio = Consultorio::create($validated);

            // Criar configuração padrão para o consultório
            AgendaConfig::createDefaultForConsultorio($consultorio->id);

            return response()->json([
                'status' => 'success',
                'message' => 'Consultório criado com sucesso',
                'data' => $consultorio->toFrontendFormat()
            ], 201);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Dados inválidos',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Erro ao criar consultório',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show a specific consultorio
     */
    public function show(Consultorio $consultorio): JsonResponse
    {
        try {
            return response()->json([
                'status' => 'success',
                'data' => $consultorio->toFrontendFormat()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Erro ao carregar consultório',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update a consultorio
     */
    public function update(Request $request, Consultorio $consultorio): JsonResponse
    {
        try {
            $validated = $request->validate([
                'nome' => 'required|string|max:255',
                'descricao' => 'nullable|string|max:500',
                'cor' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'icone' => 'required|string|max:50',
                'ordem' => 'nullable|integer|min:0',
                'ativo' => 'boolean',
            ]);

            $consultorio->update($validated);

            return response()->json([
                'status' => 'success',
                'message' => 'Consultório atualizado com sucesso',
                'data' => $consultorio->fresh()->toFrontendFormat()
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Dados inválidos',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Erro ao atualizar consultório',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a consultorio
     */
    public function destroy(Consultorio $consultorio): JsonResponse
    {
        try {
            // Verificar se não é o último consultório ativo da clínica
            $consultoriosAtivos = Consultorio::where('clinica_id', $consultorio->clinica_id)
                ->ativo()
                ->count();

            if ($consultoriosAtivos <= 1) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Não é possível remover o último consultório ativo da clínica'
                ], 422);
            }

            // Verificar se há consultas vinculadas
            $consultasCount = $consultorio->consultas()->count();
            if ($consultasCount > 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => "Não é possível remover o consultório pois há {$consultasCount} consulta(s) vinculada(s)"
                ], 422);
            }

            $consultorio->delete();

            return response()->json([
                'status' => 'success',
                'message' => 'Consultório removido com sucesso'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Erro ao remover consultório',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reorder consultorios
     */
    public function reorder(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $clinicaId = $user->clinica_id;

            $validated = $request->validate([
                'consultorios' => 'required|array',
                'consultorios.*.id' => 'required|integer|exists:consultorios,id',
                'consultorios.*.ordem' => 'required|integer|min:0',
            ]);

            foreach ($validated['consultorios'] as $item) {
                Consultorio::where('id', $item['id'])
                    ->where('clinica_id', $clinicaId)
                    ->update(['ordem' => $item['ordem']]);
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Ordem dos consultórios atualizada com sucesso'
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Dados inválidos',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Erro ao reordenar consultórios',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
