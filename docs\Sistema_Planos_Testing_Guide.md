# Guia de Testes - Sistema de Gerenciamento de Planos

## Visão Geral

Este documento descreve como testar o sistema completo de gerenciamento de planos implementado para as clínicas.

## Funcionalidades Implementadas

### Backend (Laravel)

1. **Model Plano** (`app/Models/Plano.php`)
   - Campos: nome, descrição, cor, módulos, limites, valor, etc.
   - Relacionamentos com Clinica
   - Accessors para formatação de dados
   - Scopes para consultas

2. **Migration** (`database/migrations/2025_10_13_000000_create_planos_table.php`)
   - Tabela `planos` com todos os campos necessários
   - Índices para performance

3. **Migration de Relacionamento** (`database/migrations/2025_10_13_000001_add_plano_id_to_clinicas_table.php`)
   - Campo `plano_id` na tabela `clinicas`
   - Foreign key e índices

4. **Controller** (`app/Http/Controllers/PlanosController.php`)
   - CRUD completo com validações
   - Middleware `system_admin`
   - Endpoints especiais (toggle status, planos para clínicas)

5. **Rotas** (`routes/api.php`)
   - Resource routes para planos
   - Rotas específicas com middleware

6. **Seeder** (`database/seeders/PlanosSeeder.php`)
   - 5 planos padrão
   - Associação automática de clínicas existentes

### Frontend (Vue.js)

1. **Service** (`src/services/planosService.js`)
   - Comunicação com API
   - Validações frontend
   - Formatação de dados

2. **Componente Principal** (`src/components/GerenciadorPlanos.vue`)
   - Grid de planos com design elegante
   - Ações: criar, editar, ativar/desativar, excluir

3. **Modal de Edição** (`src/components/PlanoModal.vue`)
   - Formulário completo para criar/editar planos
   - Validações em tempo real

4. **Integração** (`src/views/Dentistas.vue`)
   - Nova tab "Gerenciar Planos" na sidenav
   - Sistema de tabs funcionando

## Como Testar

### 1. Preparação do Ambiente

```bash
# 1. Executar migrations
php artisan migrate

# 2. Executar seeders
php artisan db:seed --class=PlanosSeeder

# 3. Verificar se as tabelas foram criadas
# - planos
# - clinicas (com campo plano_id)
```

### 2. Testes de Backend (API)

#### Listar Planos
```bash
GET /planos
Authorization: Bearer {token_system_admin}
```

#### Criar Plano
```bash
POST /planos
Authorization: Bearer {token_system_admin}
Content-Type: application/json

{
  "nome": "Plano Teste",
  "descricao": "Descrição do plano teste",
  "cor": "#ff5722",
  "modulo_clinica": true,
  "modulo_ortodontia": true,
  "quantidade_usuarios": 5,
  "quantidade_ortodontistas": 2,
  "quantidade_agendas": 3,
  "quantidade_cadeiras": 4,
  "meses_fidelidade_minima": 12,
  "quantidade_mentorias_mensais": 3,
  "valor_mensal": 299.90,
  "ativo": true
}
```

#### Atualizar Plano
```bash
PUT /planos/{id}
Authorization: Bearer {token_system_admin}
Content-Type: application/json

{
  "nome": "Plano Teste Atualizado",
  "valor_mensal": 399.90
}
```

#### Alternar Status
```bash
PATCH /planos/{id}/toggle-status
Authorization: Bearer {token_system_admin}
```

#### Excluir Plano
```bash
DELETE /planos/{id}
Authorization: Bearer {token_system_admin}
```

### 3. Testes de Frontend

#### Acessar Interface
1. Fazer login como system_admin
2. Navegar para `/dentistas`
3. Clicar na tab "Gerenciar Planos" na sidenav

#### Testar Funcionalidades
1. **Visualizar Planos**
   - Verificar se os planos são carregados
   - Verificar layout em grid
   - Verificar cores e informações

2. **Criar Novo Plano**
   - Clicar em "Novo Plano"
   - Preencher formulário
   - Testar validações
   - Salvar e verificar se aparece na lista

3. **Editar Plano**
   - Clicar no menu de ações (⋮)
   - Selecionar "Editar"
   - Modificar dados
   - Salvar e verificar alterações

4. **Ativar/Desativar Plano**
   - Usar menu de ações
   - Verificar mudança visual
   - Verificar badge de status

5. **Excluir Plano**
   - Tentar excluir plano com clínicas associadas (deve falhar)
   - Excluir plano sem clínicas (deve funcionar)

### 4. Validações a Testar

#### Backend
- [x] Nome obrigatório e único
- [x] Valores numéricos positivos
- [x] Mentorias só com módulo ortodontia
- [x] Não excluir plano com clínicas associadas
- [x] Middleware system_admin

#### Frontend
- [x] Validação de campos obrigatórios
- [x] Validação de valores numéricos
- [x] Validação de cor hexadecimal
- [x] Desabilitar mentorias sem ortodontia
- [x] Confirmações para ações destrutivas

### 5. Casos de Teste Específicos

#### Caso 1: Plano com Módulo Ortodontia
```json
{
  "nome": "Ortodontia Premium",
  "modulo_ortodontia": true,
  "quantidade_mentorias_mensais": 5
}
```
**Resultado esperado**: Deve salvar com sucesso

#### Caso 2: Mentorias sem Ortodontia
```json
{
  "nome": "Básico Inválido",
  "modulo_ortodontia": false,
  "quantidade_mentorias_mensais": 5
}
```
**Resultado esperado**: Deve retornar erro de validação

#### Caso 3: Valores Ilimitados
```json
{
  "nome": "Ilimitado",
  "quantidade_usuarios": null,
  "quantidade_ortodontistas": null,
  "valor_mensal": null
}
```
**Resultado esperado**: Deve salvar e exibir "Ilimitado" na interface

#### Caso 4: Excluir Plano com Clínicas
1. Associar uma clínica a um plano
2. Tentar excluir o plano
**Resultado esperado**: Deve retornar erro informando quantas clínicas usam o plano

### 6. Testes de Responsividade

- [ ] Testar em desktop (1920x1080)
- [ ] Testar em tablet (768x1024)
- [ ] Testar em mobile (375x667)
- [ ] Verificar grid responsivo
- [ ] Verificar modal responsivo

### 7. Testes de Performance

- [ ] Carregar 50+ planos
- [ ] Testar paginação se implementada
- [ ] Verificar tempo de resposta da API
- [ ] Testar com muitas clínicas associadas

## Problemas Conhecidos e Soluções

### Problema: Modal não abre
**Solução**: Verificar se Bootstrap JS está carregado

### Problema: Cores não aparecem
**Solução**: Verificar se o campo `cor` está sendo enviado corretamente

### Problema: Validações não funcionam
**Solução**: Verificar console do navegador para erros JavaScript

## Próximos Passos

1. [ ] Implementar paginação para muitos planos
2. [ ] Adicionar filtros na interface
3. [ ] Implementar histórico de mudanças de planos
4. [ ] Adicionar relatórios de uso por plano
5. [ ] Implementar notificações para mudanças de plano

## Conclusão

O sistema de gerenciamento de planos está completo e funcional. Todas as funcionalidades principais foram implementadas com validações adequadas e interface elegante.
