<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('financeiro_receber', function (Blueprint $table) {
            // Adicionar coluna orcamento_id após dentista_id
            $table->unsignedBigInteger('orcamento_id')->nullable()->after('dentista_id');

            // Adicionar índice para melhor performance
            $table->index('orcamento_id');

            // Adicionar foreign key constraint
            $table->foreign('orcamento_id')
                  ->references('id')
                  ->on('orcamentos')
                  ->onDelete('set null'); // Se o orçamento for deletado, apenas remove a referência
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('financeiro_receber', function (Blueprint $table) {
            // Remover foreign key primeiro
            $table->dropForeign(['orcamento_id']);

            // Remover índice
            $table->dropIndex(['orcamento_id']);

            // Remover coluna
            $table->dropColumn('orcamento_id');
        });
    }
};
