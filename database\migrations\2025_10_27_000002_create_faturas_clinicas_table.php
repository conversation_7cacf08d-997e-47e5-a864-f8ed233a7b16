<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('faturas_clinicas', function (Blueprint $table) {
            $table->id();
            
            // Relacionamentos
            $table->foreignId('assinatura_id')->constrained('assinaturas')->onDelete('cascade');
            $table->foreignId('clinica_id')->constrained('clinicas')->onDelete('cascade');
            
            // Identificação da fatura
            $table->string('numero_fatura')->unique(); // Auto-generated: FC-YYYY-MM-NNNN
            $table->string('descricao');
            
            // Valores financeiros
            $table->decimal('valor_nominal', 10, 2);
            $table->decimal('valor_desconto', 10, 2)->default(0);
            $table->decimal('valor_acrescimo', 10, 2)->default(0);
            $table->decimal('valor_final', 10, 2);
            
            // Datas
            $table->date('data_vencimento');
            $table->date('data_pagamento')->nullable();
            
            // Status e pagamento
            $table->enum('status', ['pendente', 'pago', 'vencido', 'cancelado'])->default('pendente');
            $table->enum('forma_pagamento', [
                'dinheiro', 
                'cartao_credito', 
                'cartao_debito', 
                'pix', 
                'transferencia', 
                'boleto'
            ])->nullable();
            
            // Observações
            $table->text('observacoes')->nullable();
            
            $table->timestamps();
            
            // Índices para performance
            $table->index(['clinica_id', 'status']);
            $table->index(['assinatura_id', 'status']);
            $table->index(['data_vencimento', 'status']);
            $table->index('status');
            $table->index('numero_fatura');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('faturas_clinicas');
    }
};
