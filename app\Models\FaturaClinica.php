<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Notifications\Notifiable;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Carbon\Carbon;

class FaturaClinica extends Model
{
    use HasFactory, Notifiable, LogsActivity;

    protected $table = 'faturas_clinicas';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'assinatura_id',
        'clinica_id',
        'numero_fatura',
        'descricao',
        'valor_nominal',
        'valor_desconto',
        'valor_acrescimo',
        'valor_final',
        'data_vencimento',
        'data_pagamento',
        'status',
        'forma_pagamento',
        'observacoes',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'valor_nominal' => 'decimal:2',
            'valor_desconto' => 'decimal:2',
            'valor_acrescimo' => 'decimal:2',
            'valor_final' => 'decimal:2',
            'data_vencimento' => 'date',
            'data_pagamento' => 'date',
        ];
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['*']);
    }

    /**
     * Relacionamentos
     */
    public function assinatura(): BelongsTo
    {
        return $this->belongsTo(Assinatura::class);
    }

    public function clinica(): BelongsTo
    {
        return $this->belongsTo(Clinica::class);
    }

    /**
     * Scopes
     */
    public function scopePendentes($query)
    {
        return $query->where('status', 'pendente');
    }

    public function scopePagas($query)
    {
        return $query->where('status', 'pago');
    }

    public function scopeVencidas($query)
    {
        return $query->where('status', 'vencido')
                    ->orWhere(function($q) {
                        $q->where('status', 'pendente')
                          ->where('data_vencimento', '<', Carbon::now()->toDateString());
                    });
    }

    public function scopeDoMes($query, $mes = null, $ano = null)
    {
        $mes = $mes ?? Carbon::now()->month;
        $ano = $ano ?? Carbon::now()->year;
        
        return $query->whereMonth('data_vencimento', $mes)
                    ->whereYear('data_vencimento', $ano);
    }

    /**
     * Métodos auxiliares
     */
    public function isPaga(): bool
    {
        return $this->status === 'pago';
    }

    public function isPendente(): bool
    {
        return $this->status === 'pendente';
    }

    public function isVencida(): bool
    {
        if ($this->status === 'vencido') {
            return true;
        }
        
        return $this->status === 'pendente' && 
               $this->data_vencimento < Carbon::now()->toDateString();
    }

    public function diasAteVencimento(): int
    {
        if ($this->isPaga()) {
            return 0;
        }
        
        return Carbon::now()->diffInDays($this->data_vencimento, false);
    }

    public function diasEmAtraso(): int
    {
        if (!$this->isVencida()) {
            return 0;
        }
        
        return Carbon::now()->diffInDays($this->data_vencimento);
    }

    public function marcarComoPaga($formaPagamento = null, $dataPagamento = null): bool
    {
        $this->status = 'pago';
        $this->forma_pagamento = $formaPagamento;
        $this->data_pagamento = $dataPagamento ?? Carbon::now()->toDateString();
        
        return $this->save();
    }

    public function cancelar($motivo = null): bool
    {
        $this->status = 'cancelado';
        if ($motivo) {
            $this->observacoes = ($this->observacoes ? $this->observacoes . "\n\n" : '') . 
                               "Cancelada em " . Carbon::now()->format('d/m/Y H:i') . ": " . $motivo;
        }
        
        return $this->save();
    }

    /**
     * Gerar número da fatura automaticamente
     */
    public static function gerarNumeroFatura(): string
    {
        $ano = Carbon::now()->year;
        $mes = Carbon::now()->format('m');
        
        // Buscar o último número do mês
        $ultimaFatura = self::where('numero_fatura', 'like', "FC-{$ano}-{$mes}-%")
                           ->orderBy('numero_fatura', 'desc')
                           ->first();
        
        if ($ultimaFatura) {
            $ultimoNumero = (int) substr($ultimaFatura->numero_fatura, -4);
            $proximoNumero = $ultimoNumero + 1;
        } else {
            $proximoNumero = 1;
        }
        
        return sprintf('FC-%d-%s-%04d', $ano, $mes, $proximoNumero);
    }

    /**
     * Boot method para auto-gerar número da fatura
     */
    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($fatura) {
            if (empty($fatura->numero_fatura)) {
                $fatura->numero_fatura = self::gerarNumeroFatura();
            }
        });
    }
}
