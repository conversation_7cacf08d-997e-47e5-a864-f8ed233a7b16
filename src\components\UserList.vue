<template>
  <div class="user-list-container">
    <!-- <PERSON><PERSON> de bus<PERSON> e ações -->
    <div class="d-flex justify-content-between align-items-center mb-4">
      <div class="search-box flex-grow-1 me-3">
        <input
          v-model="searchQuery"
          type="text"
          class="form-control"
          placeholder="Buscar usuário por nome, username ou email..."
          @input="handleSearch"
        />
      </div>
      <button
        v-if="isClinicaAdmin"
        class="btn btn-primary"
        @click="openNewUserForm"
        data-bs-toggle="modal"
        data-bs-target="#userFormModal"
      >
        <i class="fas fa-plus me-2"></i> Novo Usuário
      </button>
    </div>

    <!-- Tabela de usuários -->
    <div class="table-responsive">
      <table class="table table-hover">
        <thead class="table-light">
          <tr>
            <th>Nome</th>
            <th>Username</th>
            <th>Email</th>
            <th>Per<PERSON><PERSON><PERSON></th>
            <th>Status</th>
            <th>Criado em</th>
            <th>Ações</th>
          </tr>
        </thead>
        <tbody>
          <tr v-if="filteredUsuarios.length === 0">
            <td colspan="7" class="text-center text-muted py-4">
              Nenhum usuário encontrado
            </td>
          </tr>
          <tr v-for="usuario in filteredUsuarios" :key="usuario.id">
            <td>
              <strong>{{ usuario.name }}</strong>
            </td>
            <td>{{ usuario.username }}</td>
            <td>{{ usuario.email || '-' }}</td>
            <td>
              <span v-if="usuario.clinica_admin" class="badge bg-success">
                Admin
              </span>
              <span v-else class="badge bg-secondary">
                Usuário
              </span>
            </td>
            <td>
              <span v-if="!usuario.deleted_at" class="badge bg-success">
                Ativo
              </span>
              <span v-else class="badge bg-danger">
                Inativo
              </span>
            </td>
            <td>{{ $filters.dateDmy(usuario.created_at) }}</td>
            <td>
              <div class="btn-group btn-group-sm" role="group">
                <button
                  v-if="isClinicaAdmin && usuario.id !== currentUserId"
                  class="btn btn-outline-primary"
                  @click="editUsuario(usuario)"
                  title="Editar"
                >
                  <i class="fas fa-edit"></i>
                </button>
                <button
                  v-if="isClinicaAdmin && usuario.id !== currentUserId"
                  class="btn btn-outline-danger"
                  @click="deleteUsuario(usuario)"
                  title="Desativar"
                >
                  <i class="fas fa-trash"></i>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Loading -->
    <div v-if="isLoading" class="text-center py-4">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Carregando...</span>
      </div>
    </div>
  </div>
</template>

<script>
import usuariosService from '@/services/usuariosService';
import cSwal from '@/utils/cSwal';

export default {
  name: 'UserList',
  props: {
    clinicaId: {
      type: Number,
      required: true
    }
  },
  emits: ['edit-usuario', 'usuario-deleted', 'usuario-created'],
  data() {
    return {
      usuarios: [],
      searchQuery: '',
      isLoading: false,
      isClinicaAdmin: false,
      currentUserId: null
    };
  },
  computed: {
    filteredUsuarios() {
      if (!this.searchQuery) return this.usuarios;
      
      const query = this.searchQuery.toLowerCase();
      return this.usuarios.filter(u =>
        u.name.toLowerCase().includes(query) ||
        u.username.toLowerCase().includes(query) ||
        (u.email && u.email.toLowerCase().includes(query))
      );
    }
  },
  async mounted() {
    this.isClinicaAdmin = usuariosService.isClinicaAdmin();
    const decoded = usuariosService.decodedToken();
    this.currentUserId = decoded?.sub;
    await this.loadUsuarios();
  },
  methods: {
    async loadUsuarios() {
      this.isLoading = true;
      try {
        const response = await usuariosService.getClinicaUsuarios(this.clinicaId);
        this.usuarios = response.data || response;
      } catch (error) {
        console.error('Erro ao carregar usuários:', error);
        cSwal.cError('Erro ao carregar usuários');
      } finally {
        this.isLoading = false;
      }
    },
    handleSearch() {
      // Debounce search
      clearTimeout(this.searchTimeout);
      this.searchTimeout = setTimeout(() => {
        // Filtro é feito no computed
      }, 300);
    },
    openNewUserForm() {
      this.$emit('edit-usuario', null);
    },
    editUsuario(usuario) {
      this.$emit('edit-usuario', usuario);
    },
    async deleteUsuario(usuario) {
      const result = await cSwal.cConfirm(
        `Desativar usuário "${usuario.name}"?`,
        'Esta ação não pode ser desfeita.'
      );

      if (!result.isConfirmed) return;

      try {
        await usuariosService.deleteClinicaUsuario(this.clinicaId, usuario.id);
        cSwal.cSuccess('Usuário desativado com sucesso');
        await this.loadUsuarios();
        this.$emit('usuario-deleted', usuario);
      } catch (error) {
        console.error('Erro ao desativar usuário:', error);
        cSwal.cError('Erro ao desativar usuário');
      }
    }
  }
};
</script>

<style scoped>
.user-list-container {
  padding: 20px;
}

.search-box {
  max-width: 400px;
}

.table {
  margin-bottom: 0;
}

.btn-group-sm .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}
</style>

