<template>
  <div class="widget-calendar-wrapper relative w-full flex bg-lumi">
    <!-- left menu-->
    <!-- <LeftMenu :date="externalRequestDate" @calendar:datepicker="dateSelected = $event" ref="leftMenu" style="margin: 0 auto;"> -->
    <!-- <template #closeButton>
          <template v-if="slots.closeButton">
            <span class="inline-flex">
              <component
                :is="slots.closeButton"
                @click.prevent.stop="closeCalendar()"
              />
            </span>
          </template>
loseButton v-else @tap="closeCalendar()" />
template> -->
    <!-- / -->
    <!-- <template #loader>
          <template v-if="slots.loader">
            <span class="inline-flex">
              <component :is="slots.loader" :calendarGotLoading="isLoading" />
            </span>
          </template>
          <Loader v-else-if="isLoading" />
        </template> -->
    <!---->
    <!-- <template #sideEvent>
          <div
            class="side-event-box overflow-y-auto custom-scrll p-1"
            :class="{
              'h-50p': !configs?.nativeDatepicker,
              'below-native-datepicker': configs?.nativeDatepicker,
            }"
          >
            <template v-if="slots.sideEvent">
              <component
                :is="slots.sideEvent"
                :dateSelected="dateSelected"
                :calendarEvents="calendarEvents"
              />
            </template>
            <template v-else>
              <SideEvent :eventDate="dateSelected" />
              <SideEvent
                v-if="
                  nextDate(dateSelected).toLocaleDateString('en-CA') !=
                  dateSelected.toLocaleDateString('en-CA')
                "
                :eventDate="nextDate(dateSelected)"
              />
            </template>
          </div>
        </template> -->
    <!-- </LeftMenu> -->
    <!-- calendar base-->
    <div class="calendar-base w-full grow border border-white bg-lumi pb-0" style="flex: 1">
      <!-- calendar base header -->
      <HeaderComp>
        <!--Arrows-->
        <!-- <Arrows @calendar-arrow:today="leftMenu.datepicked = new Date()"
            @calendar-arrow:left="leftMenu.datepicked = prevDate(dateSelected)"
            @calendar-arrow:right="leftMenu.datepicked = nextDate(dateSelected)" :label="/calendar/i.test(dateLabel(dateSelected))
            ? $t(`${dateLabel(dateSelected)}`)
            : dateLabel(dateSelected)
          " :slots="slots" /> -->
        <!-- Painel de Controle Redesenhado -->
        <div class="calendar-control-panel">
          <!-- Container dividido em duas seções -->
          <div class="control-split-container">
            <!-- Seção Esquerda: Card com Data -->
            <div class="left-section">
              <div v-if="configs?.nativeDatepicker" class="date-card">
                <!-- Card Body com Datepicker -->
                <div class="date-card-body">
                  <NativeDatePicker
                    ref="calendar_date_picker"
                    :value="dateSelected"
                    @changed="dateSelected = $event"
                    class="compact-datepicker"
                  />
                </div>

                <!-- Card Footer com howMuchTime -->
                <div class="date-card-footer" :class="{
                  'is-past': isDateBeforeToday,
                  'is-today': isToday
                }">
                  <Transition name="date-text-fade" mode="out-in">
                    <span
                      :key="dateKey"
                      class="relative-text"
                      :class="{
                        'is-past': isDateBeforeToday,
                        'is-today': isToday
                      }"
                    >
                      {{ getRelativeDateText().toUpperCase() }}
                    </span>
                  </Transition>
                </div>
              </div>
            </div>

            <!-- Seção Direita: Navegação + Toggle centralizados -->
            <div class="right-section">
              <div class="navigation-toggle-group">
                <!-- Botão Anterior -->
                <button
                  @click="navigatePrevious"
                  class="nav-btn nav-prev"
                  :title="getNavigationTitle('prev')"
                >
                  <font-awesome-icon icon="fa-solid fa-chevron-left" />
                </button>

                <!-- Toggle de Visualização -->
                <Toggle
                  ref="viewToggle"
                  @calendar:view-changed="defineView = $event"
                  :view="defineView"
                  :date="dateSelected"
                  class="compact-toggle"
                />

                <!-- Botão Próximo -->
                <button
                  @click="navigateNext"
                  class="nav-btn nav-next"
                  :title="getNavigationTitle('next')"
                >
                  <font-awesome-icon icon="fa-solid fa-chevron-right" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </HeaderComp>
      <!--calendar-->
      <div data-widget-item="widget-calendar-comp" class="calendar-wrapper w-full mt-2 overflow-y-auto custom-scrll">
        <!--calendar week-view-->
        <Transition name="calendar-fade" mode="out-in">
          <template v-if="defineView === 'week'">
            <div class="calendar-view-container px-2" :key="'week-' + dateKey">
              <WeekView
                :weekDays="weekDays"
                :dateSelected="dateSelected"
                :dayTimes="dayTimes"
                :slots="slots"
                :isDayActive="isDayActive"
                :isTimeActive="isTimeActive"
              />
            </div>
          </template>
        </Transition>
        <!--calendar day-view-->
        <Transition name="calendar-fade" mode="out-in">
          <template v-if="defineView === 'day'">
            <div class="calendar-view-container px-2" :key="'day-' + dateKey">
              <DayView
                :dateSelected="dateSelected"
                :dayTimes="dayTimes"
                :slots="slots"
                :isDayActive="isDayActive"
                :isTimeActive="isTimeActive"
              />
            </div>
          </template>
        </Transition>
        <!--calendar month-view-->
        <Transition name="calendar-fade" mode="out-in">
          <template v-if="defineView === 'month'">
            <div class="calendar-view-container" :key="'month-' + dateKey">
              <MonthView
                :weekDays="weekDays"
                :monthDays="monthDays"
                :dateSelected="dateSelected"
                :slots="slots"
                :isDayActive="isDayActive"
                :isTimeActive="isTimeActive"
              />
            </div>
          </template>
        </Transition>
      </div>
    </div>
    <!---->
  </div>
</template>

<script setup lang="ts">
export interface Props {
  date?: string | Date;
  view?: T_View;
  events?: Appointment[];
  loading?: boolean;
  config?: Configs;
  consultorioId?: number | string;
}

// import v-calendar style
import "v-calendar/dist/style.css";

import "../../assets/tailwind.scss";

import {
  onMounted,
  onBeforeMount,
  ref,
  computed,
  watch,
  toRef,
  useSlots,
  nextTick,
  type ComponentPublicInstance
} from "vue";
import filters from "../../../../../helpers/filters";
import type { Ref } from "vue";
import LeftMenu from "./left-menu.vue";
import HeaderComp from "./calendar-base-header.vue";
import Arrows from "./calendar-arrows.vue";
import Search from "./calendar-search.vue";
import Toggle from "./view-toggle.vue";
import Loader from "./assets/loader-widget.vue";
import CloseButton from "./close-button.vue";
import NativeDatePicker from "./calendar-native-datepicker.vue";
import { useEventsStore, DEFAULT_CONFIGS } from "../../stores/events";
import type { Appointment, Configs, T_View } from "../../stores/events";

import MonthView from "./calendar-month-view.vue";
import DayView from "./calendar-day-view.vue";
import WeekView from "./calendar-week-view.vue";
import SideEvent from "./calendar-side-event.vue";

import {
  dateLabel,
  twoDigit,
  incrementTime,
  fixDateTime,
  randomId,
  dayName,
  copyDate,
  isoStringToDate,
  dateToIsoString,
  getWeekInterval,
  weekGenerator,
  monthGenerator,
  prevDate,
  nextDate,
} from "./common";

type T_Toggle = typeof Toggle;
type T_LeftMenu = typeof LeftMenu;

const props = withDefaults(defineProps<Props>(), {
  date: undefined,
  view: "week",
  events: () => [],
  loading: false,
  config: () => ({ ...DEFAULT_CONFIGS }),
});

const emit = defineEmits(["calendarClosed", "fetchEvents", "dateSelected"]);

const store = useEventsStore();

const leftMenu: Ref<ComponentPublicInstance<T_LeftMenu>> = ref<
  ComponentPublicInstance<T_LeftMenu>
>() as Ref<ComponentPublicInstance<T_LeftMenu>>;
const viewToggle: Ref<ComponentPublicInstance<T_Toggle>> = ref<
  ComponentPublicInstance<T_Toggle>
>() as Ref<ComponentPublicInstance<T_Toggle>>;
const dateSelected: Ref<Date> = ref(new Date());
const weekDays: Ref<Date[]> = ref([]);
const dayTimes: Ref<string[]> = ref([]);
const defineView: Ref<T_View> = ref(props.view);
const externalRequestDate: Ref<Date | undefined> = ref(undefined);
const monthDays: Ref<Date[]> = ref([]);
const monthDates: Ref<{ start: Date | string; end: Date | string }> = ref({
  start: "",
  end: "",
});
const calendarEvents = computed<Appointment[]>(() => store.getEvents);
const configs = computed<Configs>(() => store.getConfigs);
const isLoading: Ref<boolean> = ref(props.loading);
const slots = useSlots();

// Verificar se a data selecionada é hoje
const isToday = computed(() => {
  // Criar datas para comparação no fuso horário local
  const today = new Date();
  const selectedDate = new Date(dateSelected.value);

  // Resetar horas para comparar apenas as datas
  today.setHours(0, 0, 0, 0);
  selectedDate.setHours(0, 0, 0, 0);

  // Comparar os timestamps
  return selectedDate.getTime() === today.getTime();
});

// Mapeamento de dias da semana
const dayOfWeekMap = {
  0: 'domingo',
  1: 'segunda',
  2: 'terca',
  3: 'quarta',
  4: 'quinta',
  5: 'sexta',
  6: 'sabado'
};

// Verificar se um dia está ativo baseado nas configurações
const isDayActive = computed(() => (date: Date) => {
  try {
    const { default: store } = require('@/store');
    const agendaConfig = store.getters['agendaConfig/agendaConfig'];

    if (agendaConfig && agendaConfig.dias_semana && agendaConfig.dias_semana.length > 0) {
      const dayOfWeek = dayOfWeekMap[date.getDay()];
      const isActive = agendaConfig.dias_semana.includes(dayOfWeek);
      // Log apenas uma vez por dia (evitar spam)
      if (date.getDate() === new Date().getDate()) {
        console.log('📅 isDayActive - Config encontrada:', {
          dia: dayOfWeek,
          diasAtivos: agendaConfig.dias_semana,
          isActive
        });
      }
      return isActive;
    }

    // Fallback: segunda a sexta
    console.warn('⚠️ isDayActive - Usando fallback (sem config)');
    const dayOfWeek = date.getDay();
    return dayOfWeek >= 1 && dayOfWeek <= 5;
  } catch (error) {
    console.error('❌ Erro ao verificar se dia está ativo:', error);
    // Fallback: segunda a sexta
    const dayOfWeek = date.getDay();
    return dayOfWeek >= 1 && dayOfWeek <= 5;
  }
});

// Verificar se um horário está ativo para um dia específico
const isTimeActive = computed(() => (date: Date, time: string) => {
  try {
    const { default: store } = require('@/store');
    const agendaConfig = store.getters['agendaConfig/agendaConfig'];

    if (!agendaConfig) return true; // Se não há config, permitir todos os horários

    // Verificar se o dia está ativo
    if (!isDayActive.value(date)) return false;

    // Obter configuração específica para este dia da semana
    const dayOfWeek = dayOfWeekMap[date.getDay()];
    let dayConfig = null;

    // Verificar se há configurações específicas por dia
    if (agendaConfig.configuracoes_por_dia && typeof agendaConfig.configuracoes_por_dia === 'object') {
      dayConfig = agendaConfig.configuracoes_por_dia[dayOfWeek];
    }

    // Se não há configuração específica para o dia, usar configuração geral
    if (!dayConfig) {
      dayConfig = {
        horario_inicio: agendaConfig.horario_inicio || '08:00',
        horario_fim: agendaConfig.horario_fim || '18:00',
        tem_horario_almoco: agendaConfig.tem_horario_almoco || false,
        horario_almoco_inicio: agendaConfig.horario_almoco_inicio,
        horario_almoco_fim: agendaConfig.horario_almoco_fim
      };
    }

    // Verificar se o horário está dentro do expediente do dia
    const [timeHour, timeMinute] = time.split(':').map(Number);
    const timeInMinutes = timeHour * 60 + timeMinute;

    // Horário de início e fim do dia
    const [startHour, startMinute] = (dayConfig.horario_inicio || '08:00').split(':').map(Number);
    const startInMinutes = startHour * 60 + startMinute;

    const [endHour, endMinute] = (dayConfig.horario_fim || '18:00').split(':').map(Number);
    const endInMinutes = endHour * 60 + endMinute;

    if (timeInMinutes < startInMinutes || timeInMinutes >= endInMinutes) {
      return false;
    }

    // Verificar horário de almoço se configurado para este dia
    if (dayConfig.tem_horario_almoco && dayConfig.horario_almoco_inicio && dayConfig.horario_almoco_fim) {
      const [lunchStartHour, lunchStartMinute] = dayConfig.horario_almoco_inicio.split(':').map(Number);
      const lunchStartInMinutes = lunchStartHour * 60 + lunchStartMinute;

      const [lunchEndHour, lunchEndMinute] = dayConfig.horario_almoco_fim.split(':').map(Number);
      const lunchEndInMinutes = lunchEndHour * 60 + lunchEndMinute;

      if (timeInMinutes >= lunchStartInMinutes && timeInMinutes < lunchEndInMinutes) {
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error('Erro ao verificar se horário está ativo:', error);
    return true; // Em caso de erro, permitir o horário
  }
});

// Chave única para forçar a recriação do componente quando a data mudar
const dateKey = computed(() => {
  const date = new Date(dateSelected.value);
  return `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}`;
});

/**
 * closeCalendar
 */
const closeCalendar = (): void => {
  emit("calendarClosed");
};

/**
 * runSearch
 * search by event
 * @param value {string}
 */
const runSearch = async (value: string): Promise<void> => {
  const _s = new RegExp(value, "i");
  let _search = [];
  //
  if (!value.replace(/\s/g, "").length) {
    store.setEvents(props.events);
    return void 0;
  }
  //
  isLoading.value = true;
  _search = calendarEvents.value.filter((rdv: any) => {
    try {
      return _s.test(`${rdv.name}`) || _s.test(`${rdv.keywords}`);
    } catch (e) {
      return false;
    }
  });
  isLoading.value = false;
  if (_search.length !== 0) store.setEvents(_search);
};

/**
 * fetch Appointments
 */
const fetchAppointments = (): void => {
  // fetch appointments from server
  emit("fetchEvents", {
    start: dateToIsoString(
      fixDateTime(monthDates.value.start as Date, "00:00")
    ),
    end: dateToIsoString(fixDateTime(monthDates.value.end as Date, "23:59")),
  });
};

/**
 * verifyFirst Props
 */
const verifyFirstBind = (): void => {
  // date
  if (props.date) {
    if (typeof props.date === 'string') {
      // Se for string, converter para Date
      const b = isoStringToDate(props.date);
      if (b.getTime()) {
        dateSelected.value = b;
        externalRequestDate.value = dateSelected.value;
      }
    } else if (props.date instanceof Date) {
      // Se já for um objeto Date, usar diretamente
      dateSelected.value = new Date(props.date);
      externalRequestDate.value = dateSelected.value;
    }
  }

  // events
  store.setEvents(props.events);
  // config
  store.setConfigs(props.config);
};

/**
 * generateDayTimes - Gera horários baseado nas configurações da agenda
 * Considera configurações específicas por dia da semana se disponível
 */
const generateDayTimes = (): void => {
  try {
    // Tentar obter configurações do store global
    const { default: store } = require('@/store');
    const agendaConfig = store.getters['agendaConfig/agendaConfig'];

    if (agendaConfig) {
      // Se há time_slots pré-gerados, usar eles
      if (agendaConfig.time_slots && agendaConfig.time_slots.length > 0) {
        dayTimes.value = agendaConfig.time_slots;
        console.log('Horários carregados das configurações (time_slots):', dayTimes.value);
        return;
      }

      // Se há configurações por dia da semana, gerar horários unificados
      if (agendaConfig.configuracoes_por_dia && typeof agendaConfig.configuracoes_por_dia === 'object') {
        const unifiedSlots = generateUnifiedTimeSlotsFromDayConfigs(agendaConfig);
        dayTimes.value = unifiedSlots;
        console.log('Horários gerados das configurações por dia:', dayTimes.value);
        return;
      }

      // Se não há configurações por dia, gerar baseado nas configurações gerais
      if (agendaConfig.horario_inicio && agendaConfig.horario_fim) {
        const generatedSlots = generateTimeSlotsFromConfig(agendaConfig);
        dayTimes.value = generatedSlots;
        console.log('Horários gerados das configurações gerais:', dayTimes.value);
        return;
      }
    }

    // Fallback para horários padrão se não houver configurações
    console.log('Usando horários padrão (configurações não encontradas)');
    generateDefaultDayTimes();
  } catch (error) {
    console.error('Erro ao carregar configurações da agenda:', error);
    // Fallback para horários padrão em caso de erro
    generateDefaultDayTimes();
  }
};

/**
 * generateUnifiedTimeSlotsFromDayConfigs - Gera horários unificados baseado nas configurações por dia
 */
const generateUnifiedTimeSlotsFromDayConfigs = (config: any): string[] => {
  const allSlots = new Set<string>();

  try {
    const dayConfigs = config.configuracoes_por_dia;
    const activeDays = config.dias_semana || [];

    // Mapear nomes dos dias para as chaves das configurações
    const dayKeyMap = {
      'segunda': 'segunda',
      'terca': 'terca',
      'quarta': 'quarta',
      'quinta': 'quinta',
      'sexta': 'sexta',
      'sabado': 'sabado',
      'domingo': 'domingo'
    };

    // Para cada dia ativo, gerar horários e adicionar ao conjunto
    activeDays.forEach(dayName => {
      const dayKey = dayKeyMap[dayName];
      const dayConfig = dayConfigs[dayKey];

      if (dayConfig && dayConfig.horario_inicio && dayConfig.horario_fim) {
        // Criar configuração temporária para este dia
        const tempConfig = {
          horario_inicio: dayConfig.horario_inicio,
          horario_fim: dayConfig.horario_fim,
          tem_horario_almoco: dayConfig.tem_horario_almoco || false,
          horario_almoco_inicio: dayConfig.horario_almoco_inicio,
          horario_almoco_fim: dayConfig.horario_almoco_fim,
          duracao_padrao_consulta: config.duracao_padrao_consulta || 30,
          intervalo_entre_consultas: config.intervalo_entre_consultas || 0
        };

        const daySlots = generateTimeSlotsFromConfig(tempConfig);
        daySlots.forEach(slot => allSlots.add(slot));
      }
    });

    // Converter Set para Array e ordenar
    const sortedSlots = Array.from(allSlots).sort((a, b) => {
      const [aHour, aMinute] = a.split(':').map(Number);
      const [bHour, bMinute] = b.split(':').map(Number);
      const aTime = aHour * 60 + aMinute;
      const bTime = bHour * 60 + bMinute;
      return aTime - bTime;
    });

    return sortedSlots;
  } catch (error) {
    console.error('Erro ao gerar horários unificados:', error);
    return [];
  }
};

/**
 * generateTimeSlotsFromConfig - Gera horários baseado nas configurações
 */
const generateTimeSlotsFromConfig = (config: any): string[] => {
  const slots: string[] = [];

  try {
    // Converter horários para objetos Date para facilitar cálculos
    const [startHour, startMinute] = config.horario_inicio.split(':').map(Number);
    const [endHour, endMinute] = config.horario_fim.split(':').map(Number);

    const current = new Date();
    current.setHours(startHour, startMinute, 0, 0);

    const end = new Date();
    end.setHours(endHour, endMinute, 0, 0);

    // Configurações de almoço se existirem
    let lunchStart: Date | null = null;
    let lunchEnd: Date | null = null;

    if (config.tem_horario_almoco && config.horario_almoco_inicio && config.horario_almoco_fim) {
      const [lunchStartHour, lunchStartMinute] = config.horario_almoco_inicio.split(':').map(Number);
      const [lunchEndHour, lunchEndMinute] = config.horario_almoco_fim.split(':').map(Number);

      lunchStart = new Date();
      lunchStart.setHours(lunchStartHour, lunchStartMinute, 0, 0);

      lunchEnd = new Date();
      lunchEnd.setHours(lunchEndHour, lunchEndMinute, 0, 0);
    }

    // Duração da consulta (padrão 30 minutos)
    const consultaDuracao = config.duracao_padrao_consulta || 30;
    const intervaloConsultas = config.intervalo_entre_consultas || 0;
    const totalInterval = consultaDuracao + intervaloConsultas;

    // Gerar slots
    while (current < end) {
      const timeString = `${twoDigit(current.getHours())}:${twoDigit(current.getMinutes())}`;

      // Verificar se não está no horário de almoço
      let isLunchTime = false;
      if (lunchStart && lunchEnd) {
        isLunchTime = current >= lunchStart && current < lunchEnd;
      }

      if (!isLunchTime) {
        slots.push(timeString);
      }

      // Avançar para o próximo slot
      current.setTime(current.getTime() + totalInterval * 60000);
    }

  } catch (error) {
    console.error('Erro ao gerar horários das configurações:', error);
    return [];
  }

  return slots;
};

/**
 * generateDefaultDayTimes - Horários padrão como fallback
 */
const generateDefaultDayTimes = (): void => {
  // dayTimes generation from 08h00 to 18h00 (padrão)
  const _p1 = Array.from(
    { length: 18 - 8 + 1 },
    (_, i) => `${twoDigit(i + 8)}:${twoDigit(0)}`
  );
  dayTimes.value = _p1;
};

/**
 * watch dateSelected to change everything
 */
watch(dateSelected, () => {
  //refresh week days'date
  weekDays.value = weekGenerator(
    getWeekInterval(dateSelected.value, configs.value.firstDayOfWeek)
  );
  //refresh month days'date
  const __m = monthGenerator(dateSelected.value, configs.value.firstDayOfWeek);
  monthDays.value = __m._days;
  //month date start & end
  monthDates.value = {
    start: __m.firstDay,
    end: __m.lastDay,
  };
  // fetch appointments
  fetchAppointments();

  // A função isDateBeforeToday é um computed e será reavaliada automaticamente

  emit("dateSelected", dateSelected.value)
});

/**
 * Função para estender dayTimes para incluir horários de eventos fora do range
 */
const extendDayTimesForEvents = (events: Appointment[]) => {
  if (!events || events.length === 0) return;

  const currentTimes = new Set(dayTimes.value);
  let hasNewTimes = false;

  // Extrair todos os horários dos eventos
  events.forEach(event => {
    try {
      // Validar se o evento tem uma data válida
      if (!event.date) {
        console.warn('⚠️ Evento sem data:', event);
        return;
      }

      const eventDate = isoStringToDate(event.date);

      // Validar se a data é válida
      if (isNaN(eventDate.getTime())) {
        console.warn('⚠️ Data inválida no evento:', event.date, event);
        return;
      }

      const hours = eventDate.getHours();
      const minutes = eventDate.getMinutes();

      // Validar se hours e minutes são números válidos
      if (isNaN(hours) || isNaN(minutes)) {
        console.warn('⚠️ Horário inválido extraído do evento:', event.date, event);
        return;
      }

      const time = `${twoDigit(hours)}:${twoDigit(minutes)}`;

      // Validar formato do horário
      if (!/^\d{2}:\d{2}$/.test(time)) {
        console.warn('⚠️ Formato de horário inválido:', time, event);
        return;
      }

      if (!currentTimes.has(time)) {
        hasNewTimes = true;
        currentTimes.add(time);
        console.log(`⚠️ Evento encontrado fora do range configurado: ${time}`);
      }
    } catch (error) {
      console.error('❌ Erro ao processar evento:', event, error);
    }
  });

  // Se houver novos horários, atualizar dayTimes
  if (hasNewTimes) {
    // Converter para array e ordenar
    const allTimes = Array.from(currentTimes).sort((a, b) => {
      const [aHour, aMin] = a.split(':').map(Number);
      const [bHour, bMin] = b.split(':').map(Number);
      return (aHour * 60 + aMin) - (bHour * 60 + bMin);
    });

    dayTimes.value = allTimes;
    console.log(`✅ Range de horários estendido para incluir todos os eventos. Total de slots: ${allTimes.length}`);
  }
};

/**
 * watch props and set needed in store
 */
watch(props, () => {
  store.setEvents(props.events);
  store.setConfigs(props.config);

  isLoading.value = props.loading;

  // Estender dayTimes se houver eventos fora do range
  extendDayTimesForEvents(props.events);
});

onBeforeMount(async () => {
  generateDayTimes();
});

/**
 * watch agenda config changes to regenerate time slots
 */
watch(
  () => {
    try {
      const { default: store } = require('@/store');
      return store.getters['agendaConfig/agendaConfig'];
    } catch {
      return null;
    }
  },
  (newConfig, oldConfig) => {
    if (newConfig) {
      // Verificar se houve mudanças relevantes nas configurações
      const hasRelevantChanges = !oldConfig ||
        newConfig.horario_inicio !== oldConfig.horario_inicio ||
        newConfig.horario_fim !== oldConfig.horario_fim ||
        newConfig.duracao_padrao_consulta !== oldConfig.duracao_padrao_consulta ||
        newConfig.intervalo_entre_consultas !== oldConfig.intervalo_entre_consultas ||
        newConfig.tem_horario_almoco !== oldConfig.tem_horario_almoco ||
        newConfig.horario_almoco_inicio !== oldConfig.horario_almoco_inicio ||
        newConfig.horario_almoco_fim !== oldConfig.horario_almoco_fim ||
        JSON.stringify(newConfig.dias_semana) !== JSON.stringify(oldConfig?.dias_semana) ||
        JSON.stringify(newConfig.time_slots) !== JSON.stringify(oldConfig?.time_slots);

      if (hasRelevantChanges) {
        console.log('Configurações da agenda atualizadas, regenerando horários');
        generateDayTimes();

        // Regenerar dados das semanas e meses para refletir mudanças nos dias ativos
        weekDays.value = weekGenerator(
          getWeekInterval(dateSelected.value, configs.value.firstDayOfWeek)
        );

        const __m = monthGenerator(dateSelected.value, configs.value.firstDayOfWeek);
        monthDays.value = __m._days;
        monthDates.value = {
          start: __m.firstDay,
          end: __m.lastDay,
        };
      }
    }
  },
  { deep: true, immediate: false }
);

// Variáveis para controlar a mudança de consultório
// IMPORTANTE: Declarar ANTES dos watchers que as utilizam
const isWaitingForConsultorioChange = ref(false);
const pendingConsultorioId = ref<string | number | null>(null);

/**
 * Função auxiliar para atualizar o calendário quando o consultório muda
 */
const updateCalendarForNewConsultorio = () => {
  // Regenerar horários baseado no novo consultório
  generateDayTimes();

  // Regenerar dados das semanas e meses para refletir mudanças nos dias ativos
  weekDays.value = weekGenerator(
    getWeekInterval(dateSelected.value, configs.value.firstDayOfWeek)
  );

  const __m = monthGenerator(dateSelected.value, configs.value.firstDayOfWeek);
  monthDays.value = __m._days;
  monthDates.value = {
    start: __m.firstDay,
    end: __m.lastDay,
  };

  // Emitir evento para buscar novos appointments (similar ao fetchAppointments)
  emit("fetchEvents", {
    start: dateToIsoString(
      fixDateTime(monthDates.value.start as Date, "00:00")
    ),
    end: dateToIsoString(fixDateTime(monthDates.value.end as Date, "23:59")),
  });

  console.log('✅ LumiCalendar atualizado para novo consultório');
};

/**
 * Tenta atualizar o calendário para o novo consultório
 */
const tryUpdateCalendarForConsultorio = (consultorioId: string | number) => {
  const { default: store } = require('@/store');
  const configForConsultorio = store.getters['agendaConfig/agendaConfigForConsultorio'](consultorioId);

  if (configForConsultorio) {
    console.log('✅ Configuração já disponível, atualizando calendário imediatamente');
    updateCalendarForNewConsultorio();
    isWaitingForConsultorioChange.value = false;
    pendingConsultorioId.value = null;
  } else {
    console.log('⏳ Configuração não disponível ainda, aguardando carregamento...');
    // O watcher acima cuidará de atualizar quando a configuração estiver disponível
  }
};

/**
 * watch consultorioId changes to update calendar data (similar to view changes)
 */
watch(
  () => props.consultorioId,
  (newConsultorioId, oldConsultorioId) => {
    if (newConsultorioId !== undefined) {
      console.log('🔄 Consultório alterado no LumiCalendar:', newConsultorioId, '(anterior:', oldConsultorioId, ')');

      // Marcar que estamos aguardando uma mudança de consultório
      isWaitingForConsultorioChange.value = true;
      pendingConsultorioId.value = newConsultorioId;

      // Tentar atualizar imediatamente
      tryUpdateCalendarForConsultorio(newConsultorioId);
    }
  },
  { immediate: true }
);

/**
 * Watcher para monitorar quando a configuração do consultório é carregada
 */
watch(
  () => {
    const { default: store } = require('@/store');
    const consultorioId = pendingConsultorioId.value;
    if (!consultorioId) return null;
    return store.getters['agendaConfig/agendaConfigForConsultorio'](consultorioId);
  },
  (newConfig) => {
    if (isWaitingForConsultorioChange.value && newConfig && pendingConsultorioId.value) {
      console.log('✅ Configuração carregada para consultório:', pendingConsultorioId.value);
      updateCalendarForNewConsultorio();
      isWaitingForConsultorioChange.value = false;
      pendingConsultorioId.value = null;
    }
  },
  { immediate: false }
);

/**
 * Navegar para o dia/semana/mês anterior
 */
const navigatePrevious = (): void => {
  const currentView = defineView.value;

  if (currentView === 'day') {
    // Ir para o dia anterior
    const prevDay = new Date(dateSelected.value);
    prevDay.setDate(prevDay.getDate() - 1);
    dateSelected.value = prevDay;
  } else if (currentView === 'week') {
    // Ir para a semana anterior
    const prevWeek = new Date(dateSelected.value);
    prevWeek.setDate(prevWeek.getDate() - 7);
    dateSelected.value = prevWeek;
  } else if (currentView === 'month') {
    // Ir para o mês anterior
    const prevMonth = new Date(dateSelected.value);
    prevMonth.setMonth(prevMonth.getMonth() - 1);
    dateSelected.value = prevMonth;
  }
};

/**
 * Navegar para o próximo dia/semana/mês
 */
const navigateNext = (): void => {
  const currentView = defineView.value;

  if (currentView === 'day') {
    // Ir para o próximo dia
    const nextDay = new Date(dateSelected.value);
    nextDay.setDate(nextDay.getDate() + 1);
    dateSelected.value = nextDay;
  } else if (currentView === 'week') {
    // Ir para a próxima semana
    const nextWeek = new Date(dateSelected.value);
    nextWeek.setDate(nextWeek.getDate() + 7);
    dateSelected.value = nextWeek;
  } else if (currentView === 'month') {
    // Ir para o próximo mês
    const nextMonth = new Date(dateSelected.value);
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    dateSelected.value = nextMonth;
  }
};

/**
 * Voltar para hoje
 */
const goToToday = (): void => {
  dateSelected.value = new Date();
};

/**
 * Obter o título para os botões de navegação
 */
const getNavigationTitle = (direction: 'prev' | 'next'): string => {
  const action = direction === 'prev' ? 'Anterior' : 'Próximo';

  if (defineView.value === 'day') {
    return `${action} Dia`;
  } else if (defineView.value === 'week') {
    return `${action} Semana`;
  } else {
    return `${action} Mês`;
  }
};

/**
 * Obter o texto relativo à data selecionada
 */
const getRelativeDateText = (): string => {
  // Criar datas para comparação no fuso horário local
  const today = new Date();
  const selectedDate = new Date(dateSelected.value);

  // Resetar horas para comparar apenas as datas
  today.setHours(0, 0, 0, 0);
  selectedDate.setHours(0, 0, 0, 0);

  // Verificar se é hoje
  if (selectedDate.getTime() === today.getTime()) {
    return 'hoje';
  }

  // Verificar se é amanhã
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);
  if (selectedDate.getTime() === tomorrow.getTime()) {
    return 'amanhã';
  }

  // Verificar se é ontem
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  if (selectedDate.getTime() === yesterday.getTime()) {
    return 'ontem';
  }

  // Para outras datas, usar o filtro howMuchTime
  return filters.howMuchTime(selectedDate, {
    type: 'date',
    compareTo: today,
    prefix: true
  });
};

/**
 * Verificar se a data selecionada é anterior a hoje
 */
const isDateBeforeToday = computed(() => {
  // Criar datas para comparação no fuso horário local
  const today = new Date();
  const selectedDate = new Date(dateSelected.value);

  // Resetar horas para comparar apenas as datas
  today.setHours(0, 0, 0, 0);
  selectedDate.setHours(0, 0, 0, 0);

  // Verificar se a data selecionada é anterior a hoje
  return selectedDate.getTime() < today.getTime();
});



onMounted(async () => {
  // verify first bind props: date, events
  verifyFirstBind();
});
</script>

<style lang="scss" scoped>
.calendar-wrapper {
  height: calc(83vh - 66px);
}

.side-event-box {
  &.below-native-datepicker {
    height: calc(100% - 92px);
  }
}

/* Painel de Controle Redesenhado */
.calendar-control-panel {
  width: 100%;
  // padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-bottom: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.control-split-container {
  display: grid;
  grid-template-columns: 45% 55%;
  align-items: center;
  width: 100%;
  gap: 0;
}

/* Seção Esquerda */
.left-section {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #F7F7F7;
  margin: -0.75rem 0 -0.75rem -1.5rem;
  border-right: 1px solid rgba(226, 232, 240, 0.5);
  padding: 1rem;
}

/* Card de Data */
.date-card {
  width: 100%;
  max-width: 380px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(226, 232, 240, 0.6);
  overflow: hidden;
  transition: all 0.3s ease;
}

.date-card:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
  transform: translateY(-1px);
}

/* Card Body - Datepicker */
.date-card-body {
  padding: 0;
  background: #ffffff;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
}

.compact-datepicker {
  width: 100%;
}

/* Remover border-radius superior do input */
.date-card-body .compact-datepicker :deep(input) {
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom: none;
}

/* Card Footer - howMuchTime */
.date-card-footer {
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
  padding: 0.1rem;
  text-align: center;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
}

.date-card-footer .relative-text {
  font-size: 0.75rem;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  letter-spacing: 0.5px;
  transition: all 0.2s ease;
}

/* Estilos especiais para diferentes estados de data */
.date-card-footer.is-past {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
}

.date-card-footer.is-past .relative-text {
  color: #92400e;
  font-weight: 700;
}

.date-card-footer.is-today {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
}

.date-card-footer.is-today .relative-text {
  color: #166534;
  font-weight: 700;
}

.relative-text.is-past {
  color: #f97316;
  font-weight: 600;
}

.relative-text.is-today {
  color: #22c55e;
  font-weight: 600;
}

/* Seção Direita */
.right-section {
  background: #FAFAFA;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  padding: 0.5rem 0;
}

.navigation-toggle-group {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  width: 90%;
  padding: 0.25rem;
}

/* Botões de Navegação Flat */
.nav-btn {
  width: 36px;
  height: 36px;
  min-width: 36px;
  min-height: 36px;
  border-radius: 6px;
  background: transparent;
  color: #64748b;
  border: 1px solid #e2e8f0;
  transition: all 0.15s ease;
  font-size: 13px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  cursor: pointer;
  outline: none;
}

.nav-btn:hover {
  background: #f8fafc;
  color: #475569;
  border-color: #cbd5e1;
}

.nav-btn:active {
  background: #f1f5f9;
  transform: scale(0.98);
}

.nav-btn:focus {
  border-color: #0ea5e9;
  box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.1);
}

.today-button {
  height: calc(100% - 2px);
  padding: 0 12px;
  margin: 1px;
  border-radius: 0 6px 6px 0;
  background: linear-gradient(to right, rgba(255,255,255,0), #f0f9ff);
  color: #0ea5e9;
  font-weight: 600;
  font-size: 13px;
  transition: all 0.2s ease;
}

.today-button:hover {
  background: linear-gradient(to right, rgba(255,255,255,0), #e0f2fe);
  color: #0284c7;
}

.today-text {
  padding-right: 4px;
}

/* Responsividade */
@media (max-width: 768px) {
  .calendar-control-panel {
    padding: 0.75rem 1rem;
  }

  .control-split-container {
    grid-template-columns: 1fr;
  }

  .left-section {
    order: 1;
    margin: -0.75rem 0 -0.75rem -1rem;
    padding: 1rem 0.75rem 1rem 1rem;
    border-right: none;
    border-bottom: 1px solid rgba(226, 232, 240, 0.5);
  }

  .right-section {
    padding-bottom: 0px;
    order: 2;
  }

  .navigation-toggle-group {
    gap: 0.25rem;
    width: 95%; /* Usar mais largura em mobile */
  }

  .date-card {
    max-width: 100%;
  }

  .date-card-footer {
    padding: 0.4rem 0.6rem;
  }

  .date-card-footer .relative-text {
    font-size: 0.7rem;
  }

  .nav-btn {
    width: 28px;
    height: 28px;
    min-width: 28px;
    min-height: 28px;
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .calendar-control-panel {
    padding: 0.5rem 0.25rem;
  }

  .left-section {
    padding: 0.75rem 0.5rem;
  }

  .right-section {
    padding: 0;
  }

  .navigation-toggle-group {
    gap: 0.25rem;
    width: 98%; /* Usar quase toda a largura em mobile */
  }

  .date-card {
    border-radius: 10px;
  }

  .date-card-footer {
    padding: 0.4rem 0.5rem;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
  }

  .date-card-footer .relative-text {
    font-size: 0.65rem;
  }

  .date-card-body .compact-datepicker :deep(input) {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
  }

  .nav-btn {
    width: 24px;
    height: 32px;
    font-size: 10px;
  }
}

/* Para telas muito pequenas */
@media (max-width: 360px) {
  .navigation-toggle-group {
    width: 100%; /* Usar toda a largura em telas muito pequenas */
    gap: 0.15rem; /* Reduzir gap ainda mais */
  }
}

/* Animações para transição de data */
.calendar-fade-enter-active,
.calendar-fade-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.calendar-fade-enter-from {
  opacity: 0;
  transform: translateY(10px);
}

.calendar-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Animações para o texto relativo à data */
.date-text-fade-enter-active,
.date-text-fade-leave-active {
  transition: opacity 0.25s ease, transform 0.25s ease;
}

.date-text-fade-enter-from {
  opacity: 0;
  transform: translateY(5px);
}

.date-text-fade-leave-to {
  opacity: 0;
  transform: translateY(-5px);
}
</style>
