<template>
  <div class="tabela-precos">
    <!-- Header com título e botões de ação -->
    <div class="card shadow-sm">
      <div class="card-header bg-gradient-light">
        <div class="row align-items-center">
          <div class="col">
            <h4 class="mb-0 text-dark">
              <i class="fas fa-list-alt me-2"></i>
              Tabela de Preços
            </h4>
            <small class="text-muted">Gerencie os procedimentos, serviços e produtos da clínica</small>
          </div>
          <div class="col-auto">
            <div class="btn-group btn-group-sm" role="group">
              <button
                class="btn btn-outline-success"
                @click="abrirModalImportacao"
                title="Importar Planilha"
              >
                <i class="fas fa-file-import me-1"></i>
                Importar
              </button>
              <button
                class="btn btn-outline-info"
                @click="exportarDados"
                :disabled="exportando"
                title="Exportar para Excel"
              >
                <span v-if="exportando">
                  <span class="spinner-border spinner-border-sm me-1"></span>
                  Exportando...
                </span>
                <span v-else>
                  <i class="fas fa-file-export me-1"></i>
                  Exportar
                </span>
              </button>
              <button
                class="btn btn-primary"
                @click="openProcedimentoModal()"
                title="Adicionar Item"
              >
                <i class="fas fa-plus me-1"></i>
                Novo Item
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="card-body p-0">
        <!-- Filtros -->
        <div class="filtros-container p-3 border-bottom bg-white">
          <div class="row g-3">
            <div class="col-md-5">
              <div class="input-group input-group-sm">
                <span class="input-group-text filtro-icon">
                  <i class="fas fa-search"></i>
                </span>
                <input
                  type="text"
                  class="form-control form-control-sm filtro-input"
                  placeholder="Buscar itens..."
                  v-model="filtros.busca"
                  @input="buscarProcedimentos"
                >
              </div>
            </div>
            <div class="col-md-3">
              <select
                class="form-select form-select-sm filtro-select"
                v-model="filtros.tipo"
                @change="buscarProcedimentos"
              >
                <option value="">Todos os tipos</option>
                <option value="procedimento">Procedimentos</option>
                <option value="produto">Produtos</option>
              </select>
            </div>
            <div class="col-md-3">
              <select
                class="form-select form-select-sm filtro-select"
                v-model="filtros.ativo"
                @change="buscarProcedimentos"
              >
                <option value="">Todos</option>
                <option value="1">Ativos</option>
                <option value="0">Inativos</option>
              </select>
            </div>
            <div class="col-md-1">
              <button
                class="btn btn-outline-secondary btn-sm w-100 filtro-btn"
                @click="limparFiltros"
                title="Limpar filtros"
              >
                <i class="fas fa-broom"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Loading -->
        <div v-if="loading" class="text-center p-4">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Carregando...</span>
          </div>
        </div>

        <!-- Tabela de procedimentos -->
        <div v-else-if="procedimentos.length > 0" class="table-responsive">
          <table class="table table-hover mb-0">
            <thead class="table-light">
              <tr>
                <th class="text-center status-column" style="width: 60px;">Status</th>
                <th class="text-center" style="width: 90px;">Código</th>
                <th class="text-center" style="width: 100px;">Tipo</th>
                <th class="text-center">Nome</th>
                <th class="text-center" style="width: 140px;">Valor Base</th>
                <th class="text-center acoes-column" style="width: 100px;">Ações</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="procedimento in procedimentos" :key="procedimento.id">
                <td class="text-center status-column">
                  <div class="form-check form-switch d-flex justify-content-center">
                    <input
                      class="form-check-input custom-switch"
                      type="checkbox"
                      :checked="procedimento.ativo"
                      @change="confirmarToggleStatus(procedimento, $event)"
                      :disabled="toggleLoading[procedimento.id]"
                    >
                  </div>
                </td>
                <td class="text-center">
                  <code class="text-muted small">{{ procedimento.codigo || '-' }}</code>
                </td>
                <td class="text-center">
                  <span class="badge badge-sm" :class="getTipoBadgeClass(procedimento.tipo)">
                    <i :class="getTipoIcon(procedimento.tipo)" class="me-1"></i>
                    {{ getTipoLabel(procedimento.tipo) }}
                  </span>
                </td>
                <td class="text-center">
                  <div>
                    <strong>{{ procedimento.nome }}</strong>
                    <div v-if="procedimento.descricao" class="text-muted small">
                      {{ procedimento.descricao.substring(0, 50) }}{{ procedimento.descricao.length > 50 ? '...' : '' }}
                    </div>
                  </div>
                </td>
                <td class="text-center">
                  <strong class="text-success">{{ formatCurrency(procedimento.valor_base) }}</strong>
                  <div v-if="procedimento.valor_minimo" class="text-muted small">
                    Mín: {{ formatCurrency(procedimento.valor_minimo) }}
                  </div>
                </td>
                <td class="text-center acoes-column">
                  <div class="btn-group btn-group-sm">
                    <button
                      class="btn btn-outline-primary btn-sm"
                      @click="openProcedimentoModal(procedimento)"
                      title="Editar"
                    >
                      <i class="fas fa-edit"></i>
                    </button>
                    <button
                      class="btn btn-outline-danger btn-sm"
                      @click="confirmarExclusao(procedimento)"
                      title="Excluir"
                    >
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Estado vazio -->
        <div v-else class="text-center p-5">
          <i class="fas fa-list-alt fa-3x text-muted mb-3"></i>
          <h5 class="text-muted">Nenhum item encontrado</h5>
          <p class="text-muted">
            {{ filtros.busca || filtros.tipo ?
               'Tente ajustar os filtros de busca.' :
               'Comece adicionando seu primeiro item.' }}
          </p>
          <button
            v-if="!filtros.busca && !filtros.tipo"
            class="btn btn-primary"
            @click="openProcedimentoModal()"
          >
            <i class="fas fa-plus me-1"></i>
            Adicionar Primeiro Item
          </button>
        </div>

        <!-- Paginação -->
        <div v-if="paginacao && paginacao.last_page > 1" class="p-3 border-top">
          <nav>
            <ul class="pagination pagination-sm justify-content-center mb-0">
              <li class="page-item" :class="{ disabled: paginacao.current_page === 1 }">
                <button class="page-link" @click="irParaPagina(paginacao.current_page - 1)">
                  <i class="fas fa-chevron-left"></i>
                </button>
              </li>
              <li 
                v-for="pagina in getPaginasVisiveis()" 
                :key="pagina"
                class="page-item" 
                :class="{ active: pagina === paginacao.current_page }"
              >
                <button class="page-link" @click="irParaPagina(pagina)">
                  {{ pagina }}
                </button>
              </li>
              <li class="page-item" :class="{ disabled: paginacao.current_page === paginacao.last_page }">
                <button class="page-link" @click="irParaPagina(paginacao.current_page + 1)">
                  <i class="fas fa-chevron-right"></i>
                </button>
              </li>
            </ul>
          </nav>
        </div>
      </div>
    </div>

    <!-- Modal de Procedimento -->
     <teleport to="body">
      <ProcedimentoModal
      :append-to-body="true"
        ref="procedimentoModal"
        @saved="onProcedimentoSaved"
      />
    </teleport>

    <!-- Modal de Importação -->
    <teleport to="body">
      <ImportTabelaPrecosModal
        ref="importModal"
        @imported="onDadosImportados"
      />
    </teleport>
  </div>
</template>

<script>
import { servicoProdutoService } from '@/services/servicoProdutoService';
import ProcedimentoModal from './modals/ProcedimentoModal.vue';
import ImportTabelaPrecosModal from './modals/ImportTabelaPrecosModal.vue';
import cSwal from '@/utils/cSwal';

export default {
  name: 'TabelaPrecos',
  components: {
    ProcedimentoModal,
    ImportTabelaPrecosModal,
  },
  data() {
    return {
      loading: false,
      exportando: false,
      procedimentos: [],
      paginacao: null,
      filtros: {
        busca: '',
        tipo: '',
        ativo: '',
        page: 1
      },
      toggleLoading: {},
      buscarTimeout: null
    };
  },
  async mounted() {
    await this.carregarProcedimentos();
  },
  methods: {

    async carregarProcedimentos() {
      this.loading = true;
      try {
        // Limpar filtros vazios antes de enviar
        const filtrosLimpos = {};
        Object.keys(this.filtros).forEach(key => {
          if (this.filtros[key] !== '' && this.filtros[key] !== null && this.filtros[key] !== undefined) {
            filtrosLimpos[key] = this.filtros[key];
          }
        });

        const response = await servicoProdutoService.getServicosProdutos(filtrosLimpos);
        this.procedimentos = response.data.data.data || [];
        this.paginacao = {
          current_page: response.data.data.current_page,
          last_page: response.data.data.last_page,
          per_page: response.data.data.per_page,
          total: response.data.data.total
        };
      } catch (error) {
        console.error('Erro ao carregar procedimentos:', error);
        cSwal.cError('Erro ao carregar procedimentos');
      } finally {
        this.loading = false;
      }
    },



    buscarProcedimentos() {
      // Debounce para evitar muitas requisições
      clearTimeout(this.buscarTimeout);
      this.buscarTimeout = setTimeout(() => {
        this.filtros.page = 1;
        this.carregarProcedimentos();
      }, 300);
    },

    limparFiltros() {
      this.filtros = {
        busca: '',
        tipo: '',
        ativo: '',
        page: 1
      };
      this.carregarProcedimentos();
    },

    irParaPagina(pagina) {
      if (pagina >= 1 && pagina <= this.paginacao.last_page) {
        this.filtros.page = pagina;
        this.carregarProcedimentos();
      }
    },

    getPaginasVisiveis() {
      const current = this.paginacao.current_page;
      const last = this.paginacao.last_page;
      const delta = 2;
      const range = [];
      
      for (let i = Math.max(2, current - delta); i <= Math.min(last - 1, current + delta); i++) {
        range.push(i);
      }
      
      if (current - delta > 2) {
        range.unshift('...');
      }
      if (current + delta < last - 1) {
        range.push('...');
      }
      
      range.unshift(1);
      if (last !== 1) {
        range.push(last);
      }
      
      return range.filter((item, index, arr) => arr.indexOf(item) === index);
    },

    confirmarToggleStatus(procedimento, event) {
      // Reverter o estado do checkbox temporariamente
      event.target.checked = procedimento.ativo;

      const novoStatus = !procedimento.ativo;
      const acao = novoStatus ? 'ativar' : 'desativar';

      cSwal.cConfirm(
        `Tem certeza que deseja ${acao} o item "<strong>${procedimento.nome}</strong>"?`,
        () => {
          this.toggleStatus(procedimento);
        },
        {
          confirmButtonText: `Sim, ${acao}`,
          cancelButtonText: 'Cancelar',
          confirmButtonColor: novoStatus ? '#28a745' : '#ffc107'
        }
      );
    },

    async toggleStatus(procedimento) {
      this.toggleLoading[procedimento.id] = true;
      try {
        await servicoProdutoService.toggleStatus(procedimento.id);
        procedimento.ativo = !procedimento.ativo;
        cSwal.cSuccess(`Procedimento ${procedimento.ativo ? 'ativado' : 'desativado'} com sucesso`);
      } catch (error) {
        console.error('Erro ao alterar status:', error);
        cSwal.cError('Erro ao alterar status do procedimento');
      } finally {
        this.toggleLoading[procedimento.id] = false;
      }
    },

    openProcedimentoModal(procedimento = null) {
      this.$refs.procedimentoModal.open(procedimento);
    },

    onProcedimentoSaved() {
      this.carregarProcedimentos();
    },

    abrirModalImportacao() {
      this.$refs.importModal.abrir();
    },

    onDadosImportados() {
      this.carregarProcedimentos();
    },

    async exportarDados() {
      this.exportando = true;

      try {
        // Preparar filtros para exportação
        const filtrosExportacao = {};
        if (this.filtros.tipo) filtrosExportacao.tipo = this.filtros.tipo;
        if (this.filtros.ativo !== '') filtrosExportacao.ativo = this.filtros.ativo;
        if (this.filtros.busca) filtrosExportacao.busca = this.filtros.busca;

        const response = await servicoProdutoService.exportar(filtrosExportacao);

        // Criar blob e fazer download
        const blob = new Blob([response.data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });

        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;

        // Nome do arquivo com data
        const dataAtual = new Date().toISOString().split('T')[0];
        link.download = `tabela-precos-${dataAtual}.xlsx`;

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        cSwal.cSuccess('Dados exportados com sucesso!');
      } catch (error) {
        console.error('Erro ao exportar:', error);
        cSwal.cError('Erro ao exportar dados');
      } finally {
        this.exportando = false;
      }
    },

    async confirmarExclusao(procedimento) {
      cSwal.cConfirm(
        `Tem certeza que deseja excluir o procedimento "<strong>${procedimento.nome}</strong>"?<br><small class="text-muted">Esta ação não pode ser desfeita.</small>`,
        async () => {
          try {
            await servicoProdutoService.deleteServicoProduto(procedimento.id);
            cSwal.cSuccess('Procedimento excluído com sucesso');
            this.carregarProcedimentos();
          } catch (error) {
            console.error('Erro ao excluir procedimento:', error);
            cSwal.cError('Erro ao excluir procedimento');
          }
        },
        {
          confirmButtonText: 'Sim, excluir',
          cancelButtonText: 'Cancelar',
          confirmButtonColor: '#dc3545'
        }
      );
    },

    // Métodos de formatação
    formatCurrency(value) {
      return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL'
      }).format(value);
    },

    formatTempo(minutos) {
      if (minutos < 60) {
        return `${minutos}min`;
      }
      const horas = Math.floor(minutos / 60);
      const mins = minutos % 60;
      return mins > 0 ? `${horas}h ${mins}min` : `${horas}h`;
    },

    getTipoLabel(tipo) {
      const labels = {
        'produto': 'Produto',
        'procedimento': 'Procedimento'
      };
      return labels[tipo] || tipo;
    },

    getTipoIcon(tipo) {
      const icons = {
        'produto': 'fas fa-box',
        'procedimento': 'fas fa-user-md'
      };
      return icons[tipo] || 'fas fa-tag';
    },

    getTipoBadgeClass(tipo) {
      const classes = {
        'produto': 'bg-warning',
        'procedimento': 'bg-primary'
      };
      return classes[tipo] || 'bg-secondary';
    }
  }
};
</script>

<style scoped>
.tabela-precos {
  min-height: 600px;
}

.card {
  border-radius: 0 !important;
}

.card-header {
  border-radius: 0 !important;
}

.table th {
  font-weight: 600;
  font-size: 0.8rem;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
  vertical-align: middle;
  padding: 0.5rem 0.4rem;
}

.table td {
  vertical-align: middle;
  font-size: 0.8rem;
  padding: 0.5rem 0.4rem;
}

.form-switch .form-check-input {
  width: 2.5em;
  height: 1.25em;
}

.custom-switch {
  background-color: #dee2e6 !important;
  border-color: #dee2e6 !important;
}

.custom-switch:checked {
  background-color: #007bff !important;
  border-color: #007bff !important;
}

.custom-switch:focus {
  box-shadow: 0 0 0 0.25rem rgba(0, 123, 255, 0.25) !important;
}

.badge {
  font-size: 0.7rem;
}

.badge-sm {
  font-size: 0.65rem;
  padding: 0.25rem 0.5rem;
}

.btn-group-sm > .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.pagination-sm .page-link {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.input-group-sm .input-group-text {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.form-select-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.bg-gradient-light {
  background: linear-gradient(87deg, #e9ecef 0, #f8f9fc 100%) !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

/* Estilos dos filtros */
.filtros-container {
  background: linear-gradient(135deg, #f8f9fc 0%, #ffffff 100%) !important;
  border-bottom: 1px solid #e3e6f0 !important;
}

.filtro-input {
  border: 1px solid #d1d3e2;
  border-radius: 0.375rem;
  transition: all 0.15s ease-in-out;
  font-size: 0.875rem;
}

.filtro-input:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.filtro-select {
  border: 1px solid #d1d3e2;
  border-radius: 0.375rem;
  transition: all 0.15s ease-in-out;
  font-size: 0.875rem;
  background-color: #fff;
}

.filtro-select:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.filtro-icon {
  background-color: #f8f9fc;
  border-color: #d1d3e2;
  color: #5a5c69;
  border-radius: 0.375rem 0 0 0.375rem;
}

.filtro-btn {
  border: 1px solid #d1d3e2;
  border-radius: 0.375rem;
  transition: all 0.15s ease-in-out;
}

.filtro-btn:hover {
  background-color: #f8f9fc;
  border-color: #adb5bd;
}

/* Padding para colunas Status e Ações */
.status-column {
  padding-left: 1rem !important;
}

.acoes-column {
  padding-right: 1rem !important;
}
</style>
