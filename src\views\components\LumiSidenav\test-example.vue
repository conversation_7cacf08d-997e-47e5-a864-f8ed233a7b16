<template>
  <div>
    <h3>Teste do LumiSidenav com Auto-Collapse</h3>
    
    <lumi-sidenav
      icon="mdi-test-tube"
      class="fixed-end lumi-sidenav"
      v-if="showSidenav"
      :config="sidenavConfig"
      @action="handleSidenavAction"
    ></lumi-sidenav>
    
    <div class="container mt-4">
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5>Teste de Funcionalidade</h5>
            </div>
            <div class="card-body">
              <p>Este é um exemplo de teste para verificar o funcionamento do auto-collapse do LumiSidenav.</p>
              
              <h6>Comportamentos esperados:</h6>
              <ul>
                <li><strong>Ação Normal:</strong> Deve fechar o sidenav automaticamente</li>
                <li><strong>Ação de Confirmação:</strong> NÃO deve fechar o sidenav automaticamente</li>
                <li><strong>Ação com autoCollapse: false:</strong> NÃO deve fechar o sidenav</li>
              </ul>
              
              <div class="mt-3">
                <h6>Log de ações:</h6>
                <div class="bg-light p-3 rounded">
                  <div v-for="(log, index) in actionLogs" :key="index" class="mb-1">
                    <small class="text-muted">{{ log.timestamp }}</small> - 
                    <strong>{{ log.action }}</strong>: {{ log.description }}
                  </div>
                  <div v-if="actionLogs.length === 0" class="text-muted">
                    Nenhuma ação executada ainda...
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import LumiSidenav from "./index.vue";
import { mapState } from "vuex";

export default {
  name: "TestExample",
  components: {
    LumiSidenav
  },
  data() {
    return {
      actionLogs: [],
      sidenavConfig: {
        groups: [
          {
            title: "AÇÕES NORMAIS",
            buttons: [
              {
                text: "Ação Normal 1",
                icon: "add",
                iconType: "material",
                action: "normalAction1"
              },
              {
                text: "Ação Normal 2",
                icon: "edit",
                iconType: "material",
                action: "normalAction2"
              }
            ]
          },
          {
            title: "AÇÕES DE CONFIRMAÇÃO",
            buttons: [
              {
                text: "Logout (confirmação)",
                icon: ["fas", "right-from-bracket"],
                iconType: "fontawesome",
                action: "logout",
                class: "text-warning",
                iconClass: "text-warning",
                textClass: "text-warning"
              },
              {
                text: "Delete (confirmação)",
                icon: "delete",
                iconType: "material",
                action: "deleteAction",
                class: "text-danger",
                iconClass: "text-danger",
                textClass: "text-danger"
              }
            ]
          },
          {
            title: "CONTROLE MANUAL",
            buttons: [
              {
                text: "Sem Auto-Collapse",
                icon: "block",
                iconType: "material",
                action: "manualAction",
                autoCollapse: false,
                class: "text-info",
                iconClass: "text-info",
                textClass: "text-info"
              }
            ]
          }
        ]
      }
    };
  },
  computed: {
    ...mapState(["showSidenav"])
  },
  methods: {
    handleSidenavAction(action, button) {
      const timestamp = new Date().toLocaleTimeString();
      
      // Log da ação
      let description = "";
      
      switch (action) {
        case 'normalAction1':
        case 'normalAction2':
          description = "Ação normal executada - sidenav deve fechar automaticamente";
          break;
        case 'logout':
        case 'deleteAction':
          description = "Ação de confirmação executada - sidenav NÃO deve fechar automaticamente";
          break;
        case 'manualAction':
          description = "Ação com autoCollapse: false - sidenav NÃO deve fechar";
          break;
        default:
          description = "Ação desconhecida";
      }
      
      this.actionLogs.unshift({
        timestamp,
        action,
        description
      });
      
      // Limitar o log a 10 entradas
      if (this.actionLogs.length > 10) {
        this.actionLogs = this.actionLogs.slice(0, 10);
      }
      
      console.log(`Teste - Action: ${action}`, button);
      console.log(`Teste - ${description}`);
    }
  }
};
</script>

<style scoped>
.container {
  max-width: 800px;
}
</style>
