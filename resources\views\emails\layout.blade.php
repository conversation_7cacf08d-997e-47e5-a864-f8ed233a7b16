<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'LUMI Vision')</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f7fa;
            margin: 0;
            padding: 0;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #f5f7fa;
            padding: 20px 0;
        }
        
        .email-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            padding: 30px 20px;
            text-align: center;
            border-radius: 12px 12px 0 0;
            box-shadow: 0 4px 20px rgba(0, 123, 255, 0.15);
        }
        
        .email-logo {
            max-width: 180px;
            height: auto;
            filter: brightness(0) invert(1);
        }
        
        .email-content {
            background-color: #ffffff;
            padding: 40px 30px;
            border-radius: 0 0 12px 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            margin-bottom: 20px;
        }
        
        .email-title {
            font-size: 24px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .email-text {
            font-size: 16px;
            color: #555;
            line-height: 1.8;
            margin-bottom: 20px;
        }
        
        .email-button {
            display: inline-block;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: #ffffff !important;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
            transition: all 0.3s ease;
        }
        
        .email-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
        }
        
        .button-container {
            text-align: center;
            margin: 30px 0;
        }
        
        .email-footer {
            background-color: #ffffff;
            padding: 25px 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            text-align: center;
            font-size: 14px;
            color: #666;
        }
        
        .footer-divider {
            height: 1px;
            background: linear-gradient(90deg, transparent, #e0e6ed, transparent);
            margin: 20px 0;
        }
        
        .footer-links {
            margin: 15px 0;
        }
        
        .footer-links a {
            color: #007bff;
            text-decoration: none;
            margin: 0 10px;
            font-weight: 500;
        }
        
        .footer-links a:hover {
            text-decoration: underline;
        }
        
        .security-notice {
            background-color: #f8f9fa;
            border-left: 4px solid #ffc107;
            padding: 15px 20px;
            margin: 25px 0;
            border-radius: 0 8px 8px 0;
            font-size: 14px;
            color: #856404;
        }
        
        .security-notice strong {
            color: #533f03;
        }
        
        @media only screen and (max-width: 600px) {
            .email-container {
                padding: 10px;
            }
            
            .email-content {
                padding: 25px 20px;
            }
            
            .email-title {
                font-size: 20px;
            }
            
            .email-text {
                font-size: 15px;
            }
            
            .email-button {
                padding: 14px 24px;
                font-size: 15px;
            }
            
            .email-footer {
                padding: 20px 15px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="email-header">
            <img src="{{ asset('assets/img/lumi/lumi-vision-logo-300.png') }}" alt="LUMI Vision" class="email-logo">
        </div>
        
        <!-- Content -->
        <div class="email-content">
            @yield('content')
        </div>
        
        <!-- Footer -->
        <div class="email-footer">
            <div class="footer-divider"></div>
            
            <p><strong>LUMI Vision</strong></p>
            <p>Sistema de Gestão Ortodôntica</p>
            
            <div class="footer-links">
                <a href="https://lumivision.app">Site Oficial</a>
                <a href="mailto:<EMAIL>">Suporte</a>
            </div>
            
            <p style="margin-top: 15px; font-size: 12px; color: #999;">
                © {{ date('Y') }} LUMI Vision. Todos os direitos reservados.
            </p>
        </div>
    </div>
</body>
</html>
