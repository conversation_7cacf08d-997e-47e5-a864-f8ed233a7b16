<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Notifications\Notifiable;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Carbon\Carbon;

class Assinatura extends Model
{
    use HasFactory, Notifiable, LogsActivity;

    protected $table = 'assinaturas';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'clinica_id',
        'plano_id',
        'data_inicio',
        'data_fim',
        'status',
        'valor_mensal',
        'dia_cobranca',
        'meses_fidelidade',
        'motivo_alteracao',
        'periodo_anterior_id',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'data_inicio' => 'date',
            'data_fim' => 'date',
            'valor_mensal' => 'decimal:2',
            'dia_cobranca' => 'integer',
            'meses_fidelidade' => 'integer',
            'periodo_anterior_id' => 'integer',
        ];
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['*']);
    }

    /**
     * Relacionamentos
     */
    public function clinica(): BelongsTo
    {
        return $this->belongsTo(Clinica::class);
    }

    public function plano(): BelongsTo
    {
        return $this->belongsTo(Plano::class);
    }

    public function faturas(): HasMany
    {
        return $this->hasMany(FaturaClinica::class);
    }

    public function periodoAnterior(): BelongsTo
    {
        return $this->belongsTo(Assinatura::class, 'periodo_anterior_id');
    }

    public function proximoPeriodo(): HasMany
    {
        return $this->hasMany(Assinatura::class, 'periodo_anterior_id');
    }

    /**
     * Scopes
     */
    public function scopeAtivas($query)
    {
        return $query->where('status', 'ativo');
    }

    public function scopeEncerradas($query)
    {
        return $query->where('status', 'encerrado');
    }

    public function scopeCanceladas($query)
    {
        return $query->where('status', 'cancelado');
    }

    public function scopePeriodoAtual($query)
    {
        return $query->where('status', 'ativo')
                    ->whereNull('data_fim');
    }

    public function scopeHistorico($query)
    {
        return $query->whereIn('status', ['encerrado', 'cancelado'])
                    ->whereNotNull('data_fim');
    }

    /**
     * Métodos auxiliares
     */
    public function isAtiva(): bool
    {
        return $this->status === 'ativo';
    }

    public function isVigente(): bool
    {
        $hoje = Carbon::now();
        return $this->data_inicio <= $hoje && $this->data_fim >= $hoje;
    }

    public function diasRestantes(): int
    {
        if (!$this->isVigente()) {
            return 0;
        }
        
        return Carbon::now()->diffInDays($this->data_fim, false);
    }

    public function proximaCobranca(): ?Carbon
    {
        if (!$this->isAtiva()) {
            return null;
        }

        $hoje = Carbon::now();
        $diaCobranca = $this->dia_cobranca;
        
        // Se o dia da cobrança já passou neste mês, próxima cobrança é no próximo mês
        if ($hoje->day > $diaCobranca) {
            return $hoje->copy()->addMonth()->day($diaCobranca);
        }
        
        // Se ainda não chegou o dia da cobrança neste mês
        return $hoje->copy()->day($diaCobranca);
    }

    public function valorPorPeriodo(): float
    {
        $multiplicador = match($this->periodo_cobranca) {
            'trimestral' => 3,
            'semestral' => 6,
            'anual' => 12,
            default => 1, // mensal
        };

        return $this->valor_mensal * $multiplicador;
    }

    public function podeSerCancelada(): bool
    {
        if (!$this->isAtiva()) {
            return false;
        }

        // Verificar se ainda está no período de fidelidade
        $inicioFidelidade = $this->data_inicio;
        $fimFidelidade = $inicioFidelidade->copy()->addMonths($this->meses_fidelidade);
        
        return Carbon::now() >= $fimFidelidade;
    }
}
