<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('planos', function (Blueprint $table) {
            // Adicionar campo auto_registro após o campo ativo
            $table->boolean('auto_registro')->default(false)->after('ativo');
            
            // Adicionar índice para performance
            $table->index('auto_registro');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('planos', function (Blueprint $table) {
            // Remover índice
            $table->dropIndex(['auto_registro']);
            
            // Remover coluna
            $table->dropColumn('auto_registro');
        });
    }
};

