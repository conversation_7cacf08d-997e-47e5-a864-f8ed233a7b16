# Validação de Alteração de Plano - 30 Dias

## Resumo da Implementação

Foi implementada uma validação que impede alterações de plano de clínicas dentro de um período de 30 dias, exceto para usuários `system_admin`.

## Problema Resolvido

**Erro original:**
```
SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '54-2025-11-02' for key 'assinaturas_clinica_id_data_inicio_unique'
```

**Causa:** A constraint única `clinica_id + data_inicio` na tabela `assinaturas` impedia criar duas assinaturas para a mesma clínica na mesma data.

## Solução Implementada

### 1. Backend - AssinaturaController::alterarPlano()

**Validação adicionada:**
- Verifica se o usuário é `system_admin`
- Se não for admin, verifica se houve alteração nos últimos 30 dias
- Retorna erro 422 com mensagem específica se houver alteração recente

```php
// Verificar se o usuário é system_admin
$user = auth()->payload();
$isSystemAdmin = $user && isset($user['system_admin']) && $user['system_admin'] == 1;

// Verificar se houve alteração de plano nos últimos 30 dias (apenas para não-admins)
if (!$isSystemAdmin) {
    $ultimaAlteracao = $clinica->assinaturas()
        ->where('created_at', '>=', Carbon::now()->subDays(30))
        ->where('motivo_alteracao', 'like', '%Alteração de plano%')
        ->orderBy('created_at', 'desc')
        ->first();

    if ($ultimaAlteracao) {
        $diasRestantes = 30 - Carbon::now()->diffInDays($ultimaAlteracao->created_at);
        return response()->json([
            'message' => "A assinatura já foi alterada recentemente. A assinatura somente pode ser alterada daqui {$diasRestantes} dias.",
            'dias_restantes' => $diasRestantes,
            'ultima_alteracao' => $ultimaAlteracao->created_at->format('d/m/Y')
        ], 422);
    }
}
```

### 2. Backend - ClinicaController::update()

**Proteção adicional:**
- Impede alteração direta do campo `plano_id` via PATCH
- Redireciona para o endpoint específico de alteração de plano
- Aplica a mesma validação de 30 dias

```php
// Verificar se está tentando alterar o plano_id diretamente
if ($request->has('plano_id')) {
    // Mesma validação de 30 dias...
    
    return response()->json([
        'message' => 'Para alterar o plano da clínica, use o endpoint específico de alteração de plano: POST /clinicas/{id}/alterar-plano'
    ], 422);
}
```

### 3. Frontend - assinaturasService.js

**Tratamento de erros melhorado:**
- Captura erros de validação de 30 dias
- Captura erros de constraint única
- Exibe mensagens amigáveis ao usuário

```javascript
// Verificar se é erro de alteração recente (últimos 30 dias)
if (error.response?.status === 422 && error.response?.data?.dias_restantes) {
    const errorData = error.response.data;
    const errorMessage = `A assinatura já foi alterada recentemente. A assinatura somente pode ser alterada daqui ${errorData.dias_restantes} dias.`;
    throw new Error(errorMessage);
}

// Verificar se é erro de constraint única (mesmo dia)
if (error.response?.status === 500 && error.response?.data?.message?.includes('Duplicate entry')) {
    throw new Error('A assinatura já foi alterada hoje. A assinatura somente pode ser alterada daqui 30 dias.');
}
```

## Regras de Negócio

### Para Usuários Normais:
- ❌ **Não podem** alterar plano se houve alteração nos últimos 30 dias
- ❌ **Não podem** alterar plano diretamente via PATCH da clínica
- ✅ **Podem** alterar plano apenas através do endpoint específico
- ✅ **Recebem** mensagem clara com dias restantes

### Para System Admin:
- ✅ **Podem** alterar plano a qualquer momento
- ✅ **Podem** alterar múltiplas vezes no mesmo dia
- ✅ **Não têm** restrição de 30 dias

## Endpoints Afetados

1. **POST /clinicas/{id}/alterar-plano**
   - Validação de 30 dias implementada
   - Exceção para system_admin

2. **PATCH /clinicas/{id}**
   - Bloqueia alteração direta de plano_id
   - Redireciona para endpoint específico

## Mensagens de Erro

### Alteração Recente (< 30 dias):
```json
{
    "message": "A assinatura já foi alterada recentemente. A assinatura somente pode ser alterada daqui 15 dias.",
    "dias_restantes": 15,
    "ultima_alteracao": "18/10/2025"
}
```

### Tentativa de Alteração Direta:
```json
{
    "message": "Para alterar o plano da clínica, use o endpoint específico de alteração de plano: POST /clinicas/{id}/alterar-plano"
}
```

## Testes Recomendados

1. **Teste Normal:** Alterar plano de uma clínica que não teve alteração recente
2. **Teste 30 dias:** Tentar alterar plano de clínica que teve alteração há menos de 30 dias
3. **Teste Admin:** System admin alterando plano múltiplas vezes
4. **Teste PATCH:** Tentar alterar plano_id via PATCH da clínica
5. **Teste Mesmo Dia:** Tentar alterar plano duas vezes no mesmo dia

## Arquivos Modificados

- `app/Http/Controllers/AssinaturaController.php`
- `app/Http/Controllers/ClinicaController.php`
- `src/services/assinaturasService.js`
