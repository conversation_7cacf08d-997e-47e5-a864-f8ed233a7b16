<template>
  <div>
    <div class="mt-2 contacts-container" style="font-size: 12pt">
      <div class="contatos-flex-container">
        <div class="contatos-flex-header">
          <div class="contatos-flex-icon-cell"></div>
          <div class="contatos-flex-content-cell">
            <label class="form-control-label">Contato</label>
          </div>
          <div v-if="!isEditing" class="contatos-flex-content-cell second-cell">
            <label class="form-control-label">Descrição</label>
          </div>
          <div v-if="isEditing" class="contatos-flex-action-cell"></div>
        </div>
        <div
          class="contatos-flex-row"
          v-for="contato in clinica.contatos"
          v-bind:key="contato.id"
        >
          <div class="contatos-flex-icon-cell">
            <font-awesome-icon
              v-if="contato.tipo != 'telefone'"
              :icon="getContatoIcon(contato.tipo)"
              :class="{
                'text-success': contato.tipo == 'whatsapp',
                'fs-14': contato.tipo == 'email',
                'fs-15': contato.tipo != 'email',
              }"
            />
            <v-icon
              v-if="contato.tipo == 'telefone'"
              style="font-size: 17pt"
              >{{ getContatoIcon(contato.tipo) }}</v-icon
            >
          </div>
          <div class="contatos-flex-content-cell" data-label="Contato:">
            <a
              :href="getContatoHref(contato.tipo, contato.contato)"
              class="hoverable"
              target="_blank"
            >
              {{ contato.contato }}
            </a>
          </div>
          <div v-if="!isEditing" class="contatos-flex-content-cell second-cell" data-label="Descrição:">
            {{ contato.descricao }}
          </div>
          <div v-if="isEditing"
          class="contatos-flex-action-cell">
            <button
              class="btn btn-vsm btn-sm btn-danger"
              @click="excluirContato(contato.id, contato.tipo)"
            >
              <font-awesome-icon :icon="['fas', 'trash']" />
            </button>
          </div>
        </div>

        <!-- Adicionar novo contato -->
        <div v-if="isEditing" class="contatos-flex-row add-contact-row">
          <div class="contatos-flex-icon-cell">
            <div class="dropdown">
              <button
                class="btn btn-sm btn-outline-secondary dropdown-toggle"
                type="button"
                @click="toggleDropdown"
                style="font-size: 0.8rem; padding: 0.25rem 0.5rem;"
              >
                <font-awesome-icon
                  v-if="localNovoContato.tipo != 'telefone'"
                  :icon="getContatoIcon(localNovoContato.tipo)"
                  :class="{
                    'text-success': localNovoContato.tipo == 'whatsapp',
                    'fs-14': localNovoContato.tipo == 'email',
                    'fs-15': localNovoContato.tipo != 'email',
                  }"
                />
                <v-icon
                  v-if="localNovoContato.tipo == 'telefone'"
                  style="font-size: 12pt"
                  >{{ getContatoIcon(localNovoContato.tipo) }}</v-icon
                >
              </button>
              <ul class="dropdown-menu" :class="{ show: dropdownOpen }">
                <li>
                  <a
                    class="dropdown-item"
                    href="#"
                    @click.prevent="handleSelectMeioContato('email')"
                    title="E-mail"
                  >
                    <font-awesome-icon
                      :icon="['fas', 'envelope']"
                      style="font-size: 15pt; margin-right: 8px"
                    />
                    E-mail
                  </a>
                </li>
                <li>
                  <a
                    class="dropdown-item"
                    href="#"
                    @click.prevent="handleSelectMeioContato('telefone')"
                    title="Telefone fixo"
                  >
                    <v-icon style="font-size: 15pt; margin-right: 8px"
                      >mdi-phone</v-icon
                    >
                    Telefone
                  </a>
                </li>
                <li>
                  <a
                    class="dropdown-item"
                    href="#"
                    @click.prevent="handleSelectMeioContato('celular')"
                    title="Celular"
                  >
                    <font-awesome-icon
                      :icon="['fas', 'mobile-screen-button']"
                      style="font-size: 15pt; margin-right: 8px"
                    />
                    Celular
                  </a>
                </li>
                <li>
                  <a
                    class="dropdown-item"
                    href="#"
                    @click.prevent="handleSelectMeioContato('whatsapp')"
                    title="WhatsApp"
                  >
                    <font-awesome-icon
                      :icon="['fab', 'whatsapp']"
                      class="text-success"
                      style="font-size: 16pt; margin-right: 8px"
                    />
                    WhatsApp
                  </a>
                </li>
              </ul>
            </div>
            <input type="text" class="form-control"
              :placeholder="getContatoPlaceholder"
              v-model="localNovoContato.contato"
              ref="contatoInput"
              v-maska="novoContatoMask"
              @input="handleContatoChange">
            <input type="text" placeholder="Descrição"
              v-model="localNovoContato.descricao" class="form-control"
              ref="contatoDescricaoInput">

            <button
              class="btn btn-primary m-0 p-0"
              @click="adicionarContato"
              style="font-size: 1rem; width: 62px; min-width: 62px; height: 34px;"
            >
              <font-awesome-icon :icon="['fas', 'plus']" />
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { phoneMask } from "@/helpers/utils.js";
import { vMaska } from "maska/vue"

export default {
  name: "ClinicaContactInfo",
  directives: { maska: vMaska },
  props: {
    clinica: {
      type: Object,
      required: true
    },
    isEditing: {
      type: Boolean,
      default: false
    },
    isMobile: {
      type: Boolean,
      default: false
    },
    novoContato: {
      type: Object,
      required: true
    },
    getContatoPlaceholder: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      dropdownOpen: false,
      localNovoContato: {
        tipo: this.novoContato.tipo,
        contato: this.novoContato.contato,
        descricao: this.novoContato.descricao
      }
    };
  },
  computed: {
    novoContatoMask() {
      return this.phoneMask(this.localNovoContato.tipo);
    }
  },
  watch: {
    novoContato: {
      handler(newVal) {
        this.localNovoContato = {
          tipo: newVal.tipo,
          contato: newVal.contato,
          descricao: newVal.descricao
        };
      },
      deep: true
    },
    localNovoContato: {
      handler(newVal) {
        if (newVal.tipo !== this.novoContato.tipo) {
          this.$emit('update:field', { field: 'novoContato.tipo', value: newVal.tipo });
        }
        if (newVal.contato !== this.novoContato.contato) {
          this.$emit('update:field', { field: 'novoContato.contato', value: newVal.contato });
        }
        if (newVal.descricao !== this.novoContato.descricao) {
          this.$emit('update:field', { field: 'novoContato.descricao', value: newVal.descricao });
        }
      },
      deep: true
    }
  },
  mounted() {
    document.addEventListener('click', this.handleClickOutside);
  },
  beforeUnmount() {
    document.removeEventListener('click', this.handleClickOutside);
  },
  methods: {
    phoneMask,
    getContatoIcon(tipo) {
      switch (tipo) {
        case 'email':
          return ['fas', 'envelope'];
        case 'telefone':
          return 'mdi-phone';
        case 'celular':
          return ['fas', 'mobile-screen-button'];
        case 'whatsapp':
          return ['fab', 'whatsapp'];
        default:
          return ['fas', 'question'];
      }
    },
    getContatoHref(tipo, contato) {
      switch (tipo) {
        case 'email':
          return `mailto:${contato}`;
        case 'telefone':
        case 'celular':
          return `tel:${contato.replace(/\D/g, '')}`;
        case 'whatsapp':
          return `https://wa.me/55${contato.replace(/\D/g, '')}`;
        default:
          return '#';
      }
    },
    toggleDropdown() {
      this.dropdownOpen = !this.dropdownOpen;
    },
    handleClickOutside(event) {
      if (!this.$el.contains(event.target)) {
        this.dropdownOpen = false;
      }
    },
    handleSelectMeioContato(tipo) {
      this.localNovoContato.tipo = tipo;
      this.dropdownOpen = false;
      this.$emit('select-meio-contato', tipo);
      this.$nextTick(() => {
        if (this.$refs.contatoInput) {
          this.$refs.contatoInput.focus();
        }
      });
    },
    handleContatoChange(event) {
      if (event && event.target) {
        this.localNovoContato.contato = event.target.value;
        this.$emit('contato-change', event);
        this.$emit('update:field', { field: 'novoContato.contato', value: this.localNovoContato.contato });

        if ((this.localNovoContato.tipo === 'celular' || this.localNovoContato.tipo === 'whatsapp') &&
            this.localNovoContato.contato.length > 14 &&
            this.$refs.contatoDescricaoInput) {
          this.$nextTick(() => {
            this.$refs.contatoDescricaoInput.focus();
          });
        }
      }
    },
    adicionarContato() {
      this.$emit('adicionar-contato');
    },
    excluirContato(id, tipo) {
      this.$emit('excluir-contato', id, tipo);
    }
  }
};
</script>

<style scoped>
.contatos-flex-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  border: 1px solid #DDD;
  border-radius: 0.375rem 0.375rem 0 0;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.contatos-flex-header, .contatos-flex-row {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  width: 100%;
  border-bottom: 1px solid #DDD;
}

.contatos-flex-row:last-child {
  border-bottom: none;
}

.contatos-flex-row {
  transition: background-color 0.15s ease;
}

.contatos-flex-row:hover {
  background-color: #f8f9fa;
}

.contatos-flex-header {
  background-color: #f8f9fa;
  font-weight: 600;
}

.contatos-flex-icon-cell {
  flex: 0 0 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 8px;
  border-right: 1px solid #DDD;
}

.contatos-flex-content-cell {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-right: 1px solid #DDD;
  word-break: break-word;
}

.contatos-flex-content-cell:last-child {
  border-right: none;
}

.contatos-flex-content-cell.second-cell {
  flex: 0 0 200px;
}

.contatos-flex-action-cell {
  flex: 0 0 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
}

.add-contact-row {
  background-color: #f8f9fa;
  border-top: 2px solid #007bff;
}

.add-contact-row .contatos-flex-icon-cell {
  flex-direction: column;
  gap: 8px;
  padding: 16px 8px;
}

.hoverable {
  color: #007bff;
  text-decoration: none;
}

.hoverable:hover {
  color: #0056b3;
  text-decoration: underline;
}

.fs-14 {
  font-size: 14pt !important;
}

.fs-15 {
  font-size: 15pt !important;
}

.dropdown-menu {
  min-width: 150px;
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}
</style>
