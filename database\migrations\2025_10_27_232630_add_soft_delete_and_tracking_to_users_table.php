<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Soft delete
            $table->softDeletes();
            
            // Tracking fields
            $table->timestamp('last_login')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            
            // Foreign key for created_by
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeignIdFor('users', 'created_by');
            $table->dropColumn(['deleted_at', 'last_login', 'created_by']);
        });
    }
};
