<template>
  <div class="dashboard-container" :class="{
    'page-entering': isEntering,
    'page-exiting': isExiting,
    'coming-from-navigation': comingFromNavigation
  }">
    <!-- Header com boas-vindas -->
    <div class="dashboard-header" :class="{ 'animated-header': showAnimations }">
      <div class="welcome-section">
        <div class="welcome-text">
          <h1 class="welcome-title" :class="{ 'animated-title': showAnimations }">{{ welcomeMessage }}</h1>
          <p class="welcome-subtitle" :class="{ 'animated-subtitle': showAnimations }">Escolha uma opção para começar</p>
        </div>
      </div>
    </div>

    <!-- Grid de atalhos principais -->
    <div class="shortcuts-grid" :class="{ 'animated-grid': showAnimations }">
      <!-- Agenda -->
      <div class="shortcut-card main-shortcut"
           :class="{ 'animated-card': showAnimations, 'card-exiting': exitingCard === 'agenda' }"
           @click="navigateTo('agenda')"
           :style="{ animationDelay: showAnimations ? '0.05s' : '0s' }">
        <div class="shortcut-icon">
          <i class="mdi mdi-calendar-month"></i>
        </div>
        <div class="shortcut-content">
          <h3 class="shortcut-title">AGENDA</h3>
          <p class="shortcut-description">Gerencie consultas e compromissos</p>
        </div>
      </div>

      <!-- Pacientes -->
      <div class="shortcut-card main-shortcut"
           :class="{ 'animated-card': showAnimations, 'card-exiting': exitingCard === 'pacientes' }"
           @click="navigateTo('pacientes')"
           :style="{ animationDelay: showAnimations ? '0.1s' : '0s' }">
        <div class="shortcut-icon">
          <i class="mdi mdi-account-details"></i>
        </div>
        <div class="shortcut-content">
          <h3 class="shortcut-title">PACIENTES</h3>
          <p class="shortcut-description">Cadastro e histórico de pacientes</p>
        </div>
      </div>

      <!-- Financeiro -->
      <div class="shortcut-card main-shortcut"
           :class="{ 'animated-card': showAnimations, 'card-exiting': exitingCard === 'financeiro' }"
           @click="navigateTo('financeiro')"
           :style="{ animationDelay: showAnimations ? '0.15s' : '0s' }">
        <div class="shortcut-icon">
          <i class="mdi mdi-currency-usd"></i>
        </div>
        <div class="shortcut-content">
          <h3 class="shortcut-title">FINANCEIRO</h3>
          <p class="shortcut-description">Controle financeiro e relatórios</p>
        </div>
      </div>

      <!-- Mentorias (centralizado na linha 2) -->
      <div class="shortcut-card main-shortcut mentorias-card"
           :class="{ 'animated-card': showAnimations, 'card-exiting': exitingCard === 'mentorias' }"
           @click="navigateTo('mentorias')"
           :style="{ animationDelay: showAnimations ? '0.2s' : '0s' }">
        <div class="shortcut-icon">
          <i class="mdi mdi-school"></i>
        </div>
        <div class="shortcut-content">
          <h3 class="shortcut-title">MENTORIAS</h3>
          <p class="shortcut-description">Acompanhamento e orientações</p>
        </div>
      </div>

      <!-- Configurações (centralizado na linha 2) -->
      <div class="shortcut-card main-shortcut config-card" @click="navigateTo('configuracoes')">
        <div class="shortcut-icon shortcut-icon-config">
          <i class="mdi mdi-cog"></i>
        </div>
        <div class="shortcut-content">
          <h3 class="shortcut-title">CONFIGURAÇÕES</h3>
          <p class="shortcut-description">Ajustes do sistema</p>
        </div>
      </div>
    </div>



    <!-- Botão de logout no canto superior direito -->
    <div class="logout-button" @click="handleLogout" title="Sair do sistema">
      <i class="mdi mdi-logout"></i>
    </div>

    <!-- Footer elegante com logo e copyright -->
    <footer class="dashboard-footer">
      <div class="footer-content">
        <img :src="LumiBlueLogo" class="footer-logo" alt="LUMI Vision" />
        <p class="footer-copyright">© {{ currentYear }} LUMI Vision</p>
      </div>
    </footer>
  </div>
</template>
<script>
import LumiBlueLogo from "@/assets/img/lumi/lumi-vision-logo-170.png";
import cSwal from "@/utils/cSwal";

export default {
  name: "Inicio",
  data() {
    return {
      LumiBlueLogo,
      currentYear: new Date().getFullYear(),
      // Estados de animação
      showAnimations: false,
      isEntering: false,
      isExiting: false,
      exitingCard: null,
      comingFromNavigation: false,
      // Controle de navegação
      isNavigating: false,
    };
  },
  computed: {
    welcomeMessage() {
      // Obter nome do usuário e pegar apenas o primeiro nome
      const fullName = this.$user?.nome || 'Usuário';
      const firstName = fullName.trim().split(' ')[0];

      // Mensagem fixa de boas-vindas
      return `Seja bem-vindo, ${firstName}!`;

      /* CÓDIGO ANTERIOR - Mensagem dinâmica sorteada
      // Verificar se já existe uma mensagem salva nas últimas 12h
      const savedMessage = localStorage.getItem('welcomeMessage');
      const savedTimestamp = localStorage.getItem('welcomeMessageTimestamp');
      const savedFirstName = localStorage.getItem('welcomeFirstName');
      const now = Date.now();

      // Se existe mensagem salva, foi há menos de 12h E o nome é o mesmo, usar ela
      if (savedMessage && savedTimestamp && savedFirstName === firstName) {
        const twelveHoursInMs = 12 * 60 * 60 * 1000;
        if (now - parseInt(savedTimestamp) < twelveHoursInMs) {
          return savedMessage;
        }
      }

      // Caso contrário, sortear nova mensagem
      const hour = new Date().getHours();

      // Mensagens baseadas no horário
      const timeBasedMessages = [];
      if (hour >= 5 && hour < 12) {
        timeBasedMessages.push(`Bom dia, ${firstName}!`);
      } else if (hour >= 12 && hour < 18) {
        timeBasedMessages.push(`Boa tarde, ${firstName}!`);
      } else {
        timeBasedMessages.push(`Boa noite, ${firstName}!`);
      }

      // Mensagens gerais (sempre disponíveis)
      const generalMessages = [
        `Bem-vindo de volta, ${firstName}!`,
        `Olá novamente, ${firstName}!`,
        `Que bom te ver, ${firstName}!`,
        `De volta ao trabalho, ${firstName}?`,
        `O futuro começa agora, ${firstName}.`,
        `Tudo pronto para você, ${firstName}.`,
        `Olá, ${firstName}!`,
        `Vamos em frente, ${firstName}!`,
        `Estamos prontos, ${firstName}.`
      ];

      // Combinar todas as mensagens disponíveis
      const allMessages = [...timeBasedMessages, ...generalMessages];

      // Sortear uma mensagem
      const randomIndex = Math.floor(Math.random() * allMessages.length);
      const selectedMessage = allMessages[randomIndex];

      // Salvar mensagem, timestamp e primeiro nome
      localStorage.setItem('welcomeMessage', selectedMessage);
      localStorage.setItem('welcomeMessageTimestamp', now.toString());
      localStorage.setItem('welcomeFirstName', firstName);

      return selectedMessage;
      */
    }
  },
  mounted() {
    // Detectar se está vindo de navegação (via ícone home)
    this.detectNavigationOrigin();

    // Iniciar animações imediatamente para melhor responsividade
    this.$nextTick(() => {
      this.startEnterAnimations();
    });
  },
  methods: {
    detectNavigationOrigin() {
      // Verificar se há um histórico de navegação anterior
      const navigationEntries = window.performance?.getEntriesByType?.('navigation');
      const hasHistory = window.history.length > 1;

      // Verificar se está vindo de uma rota interna do sistema
      const referrer = document.referrer;
      const isInternalNavigation = referrer && referrer.includes(window.location.origin);

      // Se tem histórico e é navegação interna, provavelmente veio via ícone home
      this.comingFromNavigation = hasHistory && isInternalNavigation;
    },

    startEnterAnimations() {
      this.isEntering = true;
      this.showAnimations = true;

      // Remover classe de entrada após as animações (tempo reduzido)
      setTimeout(() => {
        this.isEntering = false;
      }, 800); // Duração total das animações de entrada reduzida
    },

    navigateTo(route) {
      if (this.isNavigating) return; // Prevenir cliques múltiplos

      this.isNavigating = true;
      this.exitingCard = route;
      this.isExiting = true;

      // Aguardar animação de saída antes de navegar
      setTimeout(() => {
        this.$router.push(`/${route}`);
      }, 400); // Duração da animação de saída
    },
    async handleLogout() {
      // Confirmação antes de fazer logout
      const result = await cSwal.cConfirm(
        'Deseja realmente sair do sistema?',
        {
          title: 'Sair',
          icon: 'question',
          confirmButtonText: 'Sim, sair',
          cancelButtonText: 'Cancelar'
        }
      );

      if (result.isConfirmed) {
        cSwal.loading('Saindo...');

        // Importar o serviço de usuários
        const usuariosService = (await import('@/services/usuariosService')).default;

        // Pequeno delay para mostrar o loading
        setTimeout(async () => {
          await usuariosService.logout();
          cSwal.loaded();
        }, 300);
      }
    },
  },
};
</script>

<style scoped>
.dashboard-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fbff 0%, #e8f4fd 100%);
  padding: 2rem 2rem 7rem 2rem; /* Padding-bottom maior para o footer */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* Botão de logout no canto superior direito */
.logout-button {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 2px solid rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.logout-button i {
  font-size: 1.5rem;
  color: #495057;
  transform: scaleX(1); /* Ícone apontando para a direita */
  transition: all 0.3s ease;
}

.logout-button:hover {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
  border-color: rgba(0, 0, 0, 0.1);
}

.logout-button:hover i {
  color: #212529;
  transform: scaleX(1) translateX(-2px);
}

.logout-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Header */
.dashboard-header {
  text-align: center;
  margin-bottom: 3rem;
}

.welcome-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.welcome-text {
  color: #2C82C9;
}

.welcome-title {
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
  font-family: 'Roboto', 'Segoe UI', 'Arial', sans-serif;
  letter-spacing: 0.5px;
  background: linear-gradient(135deg, #2C82C9, #4A9FE7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-subtitle {
  font-size: 1rem;
  margin: 0.5rem 0 0 0;
  opacity: 0.7;
  font-weight: 400;
  color: #73848D;
}

/* Grid de atalhos - 3 na linha 1, 2 centralizados na linha 2 */
.shortcuts-grid {
  display: grid;
  grid-template-columns: repeat(6, minmax(160px, 220px));
  grid-template-rows: auto auto;
  gap: 1rem;
  width: 100%;
  max-width: 720px;
  margin-bottom: 2rem;
  justify-content: center;
}

/* ===== ANIMAÇÕES DE ENTRADA (OTIMIZADAS) ===== */
/* Container principal - animação simples e leve */
.dashboard-container.page-entering {
  animation: pageEnterSimple 0.5s ease-out forwards;
}

.dashboard-container.coming-from-navigation.page-entering {
  animation: pageEnterFromNav 0.6s ease-out forwards;
}

@keyframes pageEnterSimple {
  0% {
    opacity: 0;
    transform: translateY(15px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pageEnterFromNav {
  0% {
    opacity: 0;
    transform: scale(0.98) translateY(20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Header animado - mais simples */
.animated-header {
  animation: headerFadeIn 0.4s ease-out forwards;
  opacity: 0;
}

@keyframes headerFadeIn {
  0% {
    opacity: 0;
    transform: translateY(-15px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Título animado - sem filtros pesados */
.animated-title {
  animation: titleFadeIn 0.5s ease-out 0.1s forwards;
  opacity: 0;
}

@keyframes titleFadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Subtítulo animado - mais rápido */
.animated-subtitle {
  animation: subtitleFadeIn 0.4s ease-out 0.2s forwards;
  opacity: 0;
}

@keyframes subtitleFadeIn {
  0% {
    opacity: 0;
    transform: translateY(8px);
  }
  100% {
    opacity: 0.7;
    transform: translateY(0);
  }
}

/* Primeira linha - 3 botões */
.shortcuts-grid > .shortcut-card:nth-child(1) {
  grid-column: 1 / 3;
}

.shortcuts-grid > .shortcut-card:nth-child(2) {
  grid-column: 3 / 5;
}

.shortcuts-grid > .shortcut-card:nth-child(3) {
  grid-column: 5 / 7;
}

/* Segunda linha - 2 botões centralizados */
.mentorias-card {
  grid-column: 2 / 4;
  grid-row: 2;
}

.config-card {
  grid-column: 4 / 6;
  grid-row: 2;
}

/* ===== ANIMAÇÕES DOS CARDS (OTIMIZADAS) ===== */
/* Cards animados na entrada - mais leves */
.animated-card {
  animation: cardEnterSimple 0.4s ease-out forwards;
  opacity: 0;
  transform: translateY(20px);
}

@keyframes cardEnterSimple {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animação de saída dos cards */
.card-exiting {
  animation: cardExit 0.4s cubic-bezier(0.4, 0, 0.6, 1) forwards;
  z-index: 10;
}

@keyframes cardExit {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05) translateY(-5px);
  }
  100% {
    opacity: 0;
    transform: scale(0.95) translateY(20px);
  }
}


/* Animação de saída da página */
.dashboard-container.page-exiting {
  animation: pageExit 0.4s cubic-bezier(0.4, 0, 0.6, 1) forwards;
}

@keyframes pageExit {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.98) translateY(10px);
  }
}

/* Cards de atalho principais */
.shortcut-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fbff 100%);
  border-radius: 20px;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(33, 80, 115, 0.1);
  border: 2px solid rgba(33, 80, 115, 0.1);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  position: relative;
  overflow: hidden;
}

/* Card de configurações com cor cinza mas mesmo tamanho dos outros */
.config-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2px solid rgba(0, 0, 0, 0.05);
}

.config-card .shortcut-icon-config {
  background: linear-gradient(135deg, #6c757d, #495057);
}

.config-card .shortcut-title {
  color: #495057;
}

.config-card .shortcut-description {
  color: #6c757d;
}

.shortcut-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #215073, #2a6a94, #3d8ab8);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.shortcut-card:hover::before {
  transform: scaleX(1);
}

.shortcut-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(33, 80, 115, 0.2);
  border-color: rgba(33, 80, 115, 0.3);
}

.shortcut-card:active {
  transform: translateY(-4px);
}

/* Efeito especial para cards não animados ainda */
.shortcut-card:not(.animated-card):hover {
  animation: cardHoverPulse 0.3s ease-out;
}

@keyframes cardHoverPulse {
  0% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-4px) scale(1.01);
  }
  100% {
    transform: translateY(-8px) scale(1.02);
  }
}

/* Prevenir hover durante animações de saída */
.card-exiting:hover {
  transform: none !important;
  box-shadow: none !important;
  animation: cardExit 0.4s cubic-bezier(0.4, 0, 0.6, 1) forwards !important;
}

/* Hover do card de configurações */
.config-card::before {
  background: linear-gradient(90deg, #6c757d, #495057);
}

.config-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  border-color: rgba(0, 0, 0, 0.1);
}

.config-card:active {
  transform: translateY(-4px);
}

.config-card:hover .shortcut-icon-config {
  transform: rotate(90deg) scale(1.1);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}



/* Ícones dos cards principais */
.shortcut-icon {
  width: 70px;
  height: 70px;
  border-radius: 18px;
  background: linear-gradient(135deg, #1a78c0, #2a6a94);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.2rem;
  transition: all 0.3s ease;
}

.shortcut-icon i {
  font-size: 2.2rem;
  color: white;
}

.shortcut-card:hover .shortcut-icon {
  transform: scale(1.1);
  box-shadow: 0 8px 24px rgba(33, 80, 115, 0.3);
}

/* Ícone do card de configurações - mesmo tamanho dos outros */
.shortcut-icon-config {
  width: 70px;
  height: 70px;
  border-radius: 18px;
  background: linear-gradient(135deg, #6c757d, #495057);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.shortcut-icon-config i {
  font-size: 2.2rem;
  color: white;
}



/* Conteúdo dos cards principais */
.shortcut-content {
  flex: 1;
}

.shortcut-title {
  font-size: 1rem;
  font-weight: 700;
  color: #215073;
  margin: 0 0 0.5rem 0;
  font-family: 'Roboto', 'Segoe UI', 'Arial', sans-serif;
  letter-spacing: 1px;
  text-transform: uppercase;
}

.shortcut-description {
  font-size: 0.85rem;
  color: #73848D;
  margin: 0;
  line-height: 1.4;
}



/* Footer elegante e fixo */
.dashboard-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 251, 255, 0.95) 100%);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(44, 130, 201, 0.1);
  box-shadow: 0 -4px 20px rgba(33, 80, 115, 0.08);
  padding: 1rem 0;
  z-index: 50;
  transition: all 0.3s ease;
}

.footer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.footer-logo {
  width: 70px;
  height: auto;
  opacity: 0.85;
  filter: grayscale(5%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.footer-logo:hover {
  opacity: 1;
  transform: scale(1.08);
  filter: grayscale(0%);
}

.footer-copyright {
  font-size: 0.75rem;
  color: #73848D;
  margin: 0;
  font-weight: 500;
  letter-spacing: 0.3px;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.footer-copyright:hover {
  opacity: 1;
}

/* Responsividade */
/* Telas médias - mantém sequência normal dos cards */
@media (max-width: 900px) {
  .shortcuts-grid {
    grid-template-columns: repeat(2, minmax(160px, 1fr));
    grid-template-rows: auto auto auto;
    max-width: 480px;
    gap: 1rem;
    justify-content: center;
  }

  /* Remove o posicionamento especial - layout normal */
  .shortcuts-grid > .shortcut-card:nth-child(1),
  .shortcuts-grid > .shortcut-card:nth-child(2),
  .shortcuts-grid > .shortcut-card:nth-child(3) {
    grid-column: auto;
  }

  .mentorias-card,
  .config-card {
    grid-column: auto;
    grid-row: auto;
  }

  .shortcut-card {
    padding: 1.3rem;
  }

  .shortcut-icon,
  .shortcut-icon-config {
    width: 65px;
    height: 65px;
  }

  .shortcut-icon i,
  .shortcut-icon-config i {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 1.5rem 1.5rem 6rem 1.5rem;
  }

  .welcome-title {
    font-size: 1.6rem;
  }

  .welcome-subtitle {
    font-size: 0.95rem;
  }

  /* Footer mais compacto em tablets */
  .dashboard-footer {
    padding: 0.75rem 0;
  }

  .footer-content {
    gap: 0.4rem;
  }

  .footer-logo {
    width: 60px;
  }

  .footer-copyright {
    font-size: 0.7rem;
  }

  .shortcuts-grid {
    grid-template-columns: repeat(2, 1fr);
    max-width: 420px;
    gap: 0.9rem;
  }

  /* Mantém sequência normal */
  .shortcuts-grid > .shortcut-card:nth-child(1),
  .shortcuts-grid > .shortcut-card:nth-child(2),
  .shortcuts-grid > .shortcut-card:nth-child(3) {
    grid-column: auto;
  }

  .mentorias-card,
  .config-card {
    grid-column: auto;
    grid-row: auto;
  }

  .shortcut-card {
    padding: 1.1rem;
  }

  .shortcut-icon,
  .shortcut-icon-config {
    width: 58px;
    height: 58px;
    margin-bottom: 0.9rem;
  }

  .shortcut-icon i,
  .shortcut-icon-config i {
    font-size: 1.9rem;
  }

  .shortcut-title {
    font-size: 0.88rem;
  }

  .shortcut-description {
    font-size: 0.78rem;
  }
}

@media (max-width: 480px) {
  .dashboard-container {
    padding: 1rem 1rem 5.5rem 1rem;
  }

  .welcome-title {
    font-size: 1.4rem;
  }

  .welcome-subtitle {
    font-size: 0.9rem;
  }

  .shortcuts-grid {
    max-width: 360px;
    gap: 0.75rem;
  }

  /* Mantém sequência normal */
  .shortcuts-grid > .shortcut-card:nth-child(1),
  .shortcuts-grid > .shortcut-card:nth-child(2),
  .shortcuts-grid > .shortcut-card:nth-child(3) {
    grid-column: auto;
  }

  .mentorias-card,
  .config-card {
    grid-column: auto;
    grid-row: auto;
  }

  .shortcut-card {
    padding: 0.95rem;
  }

  .shortcut-icon,
  .shortcut-icon-config {
    width: 52px;
    height: 52px;
    margin-bottom: 0.75rem;
  }

  .shortcut-icon i,
  .shortcut-icon-config i {
    font-size: 1.7rem;
  }

  .shortcut-title {
    font-size: 0.78rem;
  }

  .shortcut-description {
    font-size: 0.72rem;
  }

  .footer-logo {
    width: 60px;
  }

  /* Footer super compacto em mobile */
  .dashboard-footer {
    padding: 0.6rem 0;
  }

  .footer-content {
    gap: 0.3rem;
  }

  .footer-logo {
    width: 50px;
  }

  .footer-copyright {
    font-size: 0.65rem;
    letter-spacing: 0.2px;
  }
}

/* Media query para altura de tela reduzida - Adaptação equilibrada */
@media (max-height: 700px) {
  .dashboard-container {
    padding: 1.8rem 2rem 3.5rem 2rem; /* Mantém mais espaço vertical */
  }

  .dashboard-header {
    margin-bottom: 2.5rem; /* Mantém espaço confortável do header */
  }

  .welcome-title {
    font-size: 1.7rem; /* Título um pouco maior */
  }

  .welcome-subtitle {
    font-size: 0.95rem;
  }

  .shortcuts-grid {
    gap: 0.9rem; /* Gap mais generoso */
    margin-bottom: 1.8rem;
  }

  .shortcut-card {
    padding: 1.2rem; /* Cards com mais respiro */
  }

  .shortcut-icon,
  .shortcut-icon-config {
    width: 62px; /* Ícones maiores */
    height: 62px;
    margin-bottom: 1rem;
  }

  .shortcut-icon i,
  .shortcut-icon-config i {
    font-size: 2rem; /* Ícones mais visíveis */
  }

  .shortcut-title {
    font-size: 0.9rem; /* Títulos mais legíveis */
    margin-bottom: 0.4rem;
  }

  .shortcut-description {
    font-size: 0.8rem; /* Descrições confortáveis */
  }

  .dashboard-footer {
    padding: 0.5rem 0;
  }

  .footer-content {
    gap: 0;
  }

  /* Esconde o logo quando a altura é pequena */
  .footer-logo {
    display: none;
  }

  .footer-copyright {
    font-size: 0.7rem;
  }
}

/* Para telas muito baixas (como netbooks antigos ou janelas pequenas) */
@media (max-height: 600px) {
  .dashboard-container {
    padding: 1.5rem 1.5rem 3rem 1.5rem; /* Mais padding vertical */
  }

  .dashboard-header {
    margin-bottom: 2rem; /* Mais espaço no header */
  }

  .welcome-title {
    font-size: 1.5rem; /* Título maior */
  }

  .welcome-subtitle {
    font-size: 0.9rem;
    margin-top: 0.4rem;
  }

  .shortcuts-grid {
    gap: 0.75rem; /* Gap mais generoso */
    margin-bottom: 1.5rem;
    max-width: 600px;
  }

  .shortcut-card {
    padding: 1rem; /* Cards com mais respiro */
  }

  .shortcut-icon,
  .shortcut-icon-config {
    width: 52px; /* Ícones maiores */
    height: 52px;
    margin-bottom: 0.8rem;
  }

  .shortcut-icon i,
  .shortcut-icon-config i {
    font-size: 1.7rem; /* Ícones mais visíveis */
  }

  .shortcut-title {
    font-size: 0.82rem; /* Títulos mais legíveis */
    margin-bottom: 0.3rem;
  }

  .shortcut-description {
    font-size: 0.74rem; /* Descrições confortáveis */
    line-height: 1.35;
  }

  .dashboard-footer {
    padding: 0.4rem 0;
  }

  .footer-copyright {
    font-size: 0.65rem;
  }

  /* Botão de logout proporcionalmente ajustado */
  .logout-button {
    width: 42px;
    height: 42px;
    top: 1rem;
    right: 1rem;
  }

  .logout-button i {
    font-size: 1.3rem;
  }
}

/* Para telas muito pequenas (iPhone SE, etc) - Largura <= 400px */
@media (max-width: 400px) {
  .dashboard-container {
    padding: 1rem 0.75rem 5.5rem 0.75rem; /* Padding lateral menor */
  }

  .dashboard-header {
    margin-bottom: 1.5rem;
    padding: 0 0.5rem; /* Adiciona padding para não colar nas bordas */
  }

  .welcome-title {
    font-size: 1.3rem;
  }

  .welcome-subtitle {
    font-size: 0.85rem;
  }

  .shortcuts-grid {
    gap: 0.7rem;
    max-width: 100%;
  }

  .shortcut-card {
    padding: 0.85rem; /* Padding menor nos cards */
  }

  .shortcut-icon,
  .shortcut-icon-config {
    width: 50px;
    height: 50px;
    margin-bottom: 0.7rem;
  }

  .shortcut-icon i,
  .shortcut-icon-config i {
    font-size: 1.6rem;
  }

  .shortcut-title {
    font-size: 0.76rem;
  }

  .shortcut-description {
    font-size: 0.7rem;
  }

  /* Botão de logout menor */
  .logout-button {
    width: 38px;
    height: 38px;
    top: 0.75rem;
    right: 0.75rem;
  }

  .logout-button i {
    font-size: 1.1rem;
  }
}

/* Combinação: Telas pequenas E baixas (iPhone SE em landscape, por exemplo) */
@media (max-width: 400px) and (max-height: 600px) {
  .dashboard-container {
    padding: 0.75rem 0.75rem 3rem 0.75rem;
  }

  .dashboard-header {
    margin-bottom: 1.2rem;
  }

  .welcome-title {
    font-size: 1.2rem;
  }

  .welcome-subtitle {
    font-size: 0.8rem;
  }

  .shortcuts-grid {
    gap: 0.6rem;
    margin-bottom: 1rem;
  }

  .shortcut-card {
    padding: 0.75rem; /* Ainda mais compacto */
  }

  .shortcut-icon,
  .shortcut-icon-config {
    width: 46px;
    height: 46px;
    margin-bottom: 0.6rem;
  }

  .shortcut-icon i,
  .shortcut-icon-config i {
    font-size: 1.5rem;
  }

  .shortcut-title {
    font-size: 0.72rem;
    margin-bottom: 0.25rem;
  }

  .shortcut-description {
    font-size: 0.66rem;
  }

  .logout-button {
    width: 36px;
    height: 36px;
    top: 0.6rem;
    right: 0.6rem;
  }

  .logout-button i {
    font-size: 1rem;
  }
}
</style>
