<?php

namespace App\Console\Commands;

use App\Models\Assinatura;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class LimparDuplicatasAssinaturas extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'assinaturas:limpar-duplicatas';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Limpar duplicatas na tabela de assinaturas';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Procurando duplicatas...');

        // Encontrar duplicatas
        $duplicatas = DB::select("
            SELECT clinica_id, data_inicio, COUNT(*) as total
            FROM assinaturas 
            GROUP BY clinica_id, data_inicio 
            HAVING COUNT(*) > 1
        ");

        if (empty($duplicatas)) {
            $this->info('Nenhuma duplicata encontrada.');
            return 0;
        }

        $this->info('Encontradas ' . count($duplicatas) . ' duplicatas:');

        foreach ($duplicatas as $duplicata) {
            $this->line("- Clínica {$duplicata->clinica_id}, Data {$duplicata->data_inicio}: {$duplicata->total} registros");

            // Buscar todos os registros duplicados
            $registros = Assinatura::where('clinica_id', $duplicata->clinica_id)
                ->where('data_inicio', $duplicata->data_inicio)
                ->orderBy('id')
                ->get();

            // Manter apenas o primeiro, remover os outros
            $primeiro = $registros->first();
            $paraRemover = $registros->skip(1);

            foreach ($paraRemover as $registro) {
                $this->line("  Removendo registro ID: {$registro->id}");
                $registro->delete();
            }

            $this->line("  Mantido registro ID: {$primeiro->id}");
        }

        $this->info('Duplicatas removidas com sucesso!');
        return 0;
    }
}
