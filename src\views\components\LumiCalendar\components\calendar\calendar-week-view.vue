<template>
  <!--calendar week-view-->
  <div data-widget-item="calendar-inside">
    <!--calendar--header-->
    <div
      class="calendar--week-view--header grid grid-flow-col w-full sticky top-0 z-two dynamic-grid"
      :style="gridTemplateColumns"
    >
      <!--time-column-cell-->
      <div class="time-header" />
      <!--day-column-cell-->
      <div
        class="select-none day-header w-full pt-1 px-2 pb-4 text-left border-b border-E0E0E0 bg-white"
        v-for="(weekDayDate, weekindex) in filteredWeekDays"
        :class="{
          'border-r': weekindex !== filteredWeekDays.length - 1,
          weekindex: isWeekend(weekDayDate),
          selection: weekDayDate.getDate() === dateSelected.getDate(),
        }"
        :key="weekindex"
      >
        <!--dayname-->
        <span
          class="block text-71717A font-bold text-0dt625 leading-3 uppercase"
          :class="{
            'calendar--week-view-not-in---week':
              weekDayDate.getMonth() !== dateSelected.getMonth(),
          }"
        >
          {{ dayName(weekDayDate, weekDayDate.getDate()).slice(0, -1) }}
        </span>
        <!--daynumber-->
        <span
          class="block text-black font-medium text-1dt375 leading-8"
          :class="{
            'calendar--week-view-not-in---week':
              weekDayDate.getMonth() !== dateSelected.getMonth(),
          }"
        >
          {{ weekDayDate.getDate() }}
        </span>
      </div>
      <!--time-column-cell-->
      <div class="time-header" />
    </div>
    <!--calendar--row-->
    <div
      v-for="time in dayTimes"
      :key="time"
      class="calendar--week-view--row grid grid-flow-col w-full dynamic-grid"
      :style="gridTemplateColumns"
    >
      <!--time-row-cell-->
      <div
        class="select-none time-cell text-left text-71717A font-medium text-xs pointer"
      >
        {{ timeFormat(time) }}
      </div>
      <!--day-row-cell-->
      <div
        class="relative select-none day-cell w-full text-left border-b border-E0E0E0"
        v-for="(weekDayDate, weekindex) in filteredWeekDays"
        :class="{
          'border-r': weekindex !== filteredWeekDays.length - 1,
          weekindex: isWeekend(weekDayDate),
          selection: weekDayDate.getDate() === dateSelected.getDate(),
          'past-time-cell': isPastTime(weekDayDate, time),
          'disabled-time-cell': !props.isTimeActive(weekDayDate, time),
          'calendar-cell-clickable': props.isTimeActive(weekDayDate, time) && !isPastTime(weekDayDate, time)
        }"
        :key="weekindex"
        @click="handleCellClick(weekDayDate, time, $event)"
        @mouseenter="handleCellHover(weekDayDate, time, $event)"
        @mouseleave="hideHoverCard"
        @mousemove="updateHoverCardPosition($event)"
      >
        <!-- events are here -->
        <div class="w-full flex flex-col">
          <!-- event component - agora com posicionamento relativo para empilhar verticalmente -->
          <Events
            class="relative w-full"
            :eventDate="weekDayDate"
            :eventTime="time"
            :slots="slots"
          />
        </div>
        <!-- Overlay para hover -->
        <div class="calendar-cell-overlay"></div>
      </div>
      <!--time-row-cell-->
      <div
        class="time-cell select-none text-right text-71717A font-medium text-xs"
      >
        {{ timeFormat(time) }}
      </div>
    </div>

    <!-- Hover Card -->
    <CalendarHoverCard
      :visible="hoverCard.visible"
      :x="hoverCard.x"
      :y="hoverCard.y"
      :date="hoverCard.date"
      :time="hoverCard.time"
      :view="'week'"
    />
  </div>
</template>

<script setup lang="ts">
import { reactive, computed, ref } from "vue";
import type { Slots } from "vue";

export interface Props {
  dayTimes?: string[];
  weekDays?: Date[];
  dateSelected: Date;
  slots: Slots;
  isDayActive?: (date: Date) => boolean;
  isTimeActive?: (date: Date, time: string) => boolean;
}

import Events from "./calendar-event.vue";
import CalendarHoverCard from "./CalendarHoverCard.vue";
import {
  dayName,
  timeFormat,
} from "./common";

const props = withDefaults(defineProps<Props>(), {
  dayTimes: () => [],
  weekDays: () => [],
  isDayActive: () => () => true,
  isTimeActive: () => () => true,
});

// Estado para controlar se mostra todos os dias
const showAllDays = ref(false);

// Função para alternar visualização de todos os dias
const toggleShowAllDays = () => {
  showAllDays.value = !showAllDays.value;
};

// Mapeamento de dias da semana
const dayOfWeekMap = {
  0: 'domingo',
  1: 'segunda',
  2: 'terca',
  3: 'quarta',
  4: 'quinta',
  5: 'sexta',
  6: 'sabado'
};

// Computed para filtrar dias baseado nas configurações da agenda
const filteredWeekDays = computed(() => {
  // Se showAllDays está ativo, mostrar todos os dias
  if (showAllDays.value) {
    return props.weekDays;
  }

  // Usar a prop isDayActive para filtrar dias
  return props.weekDays.filter(date => props.isDayActive(date));
});

// Computed para gerar o grid template columns dinamicamente
const gridTemplateColumns = computed(() => {
  const numDays = filteredWeekDays.value.length;
  // 3rem para a coluna de tempo à esquerda + colunas dos dias + 3rem para a coluna de tempo à direita
  const dayColumns = Array(numDays).fill('minmax(0, 1fr)').join(' ');
  const gridTemplate = `3rem ${dayColumns} 3rem`;

  console.log('Grid template columns:', gridTemplate, 'Num days:', numDays);

  return {
    '--dynamic-grid-columns': gridTemplate
  };
});

// Estado do hover card
const hoverCard = reactive({
  visible: false,
  x: 0,
  y: 0,
  date: null as Date | null,
  time: null as string | null
});

// Verificar se é final de semana (sábado ou domingo)
const isWeekend = (date: Date) => {
  const dayOfWeek = date.getDay();
  return dayOfWeek === 0 || dayOfWeek === 6; // 0 = domingo, 6 = sábado
};

// Verificar se é horário passado
const isPastTime = (date: Date, time: string) => {
  const now = new Date();
  const targetDate = new Date(date);
  const [hours, minutes] = time.split(':').map(Number);
  targetDate.setHours(hours, minutes, 0, 0);
  return targetDate < now;
};

// Interface para configuração de horário
interface WorkingHoursConfig {
  horario_inicio: string;
  horario_fim: string;
  tem_horario_almoco: boolean;
  horario_almoco_inicio?: string;
  horario_almoco_fim?: string;
}

// Função para obter informações do horário de funcionamento de um dia
const getWorkingHoursInfo = (date: Date): WorkingHoursConfig | null => {
  try {
    const { default: store } = require('@/store');
    const agendaConfig = store.getters['agendaConfig/agendaConfig'];

    if (!agendaConfig) return null;

    const dayOfWeekMap: Record<number, string> = {
      0: 'domingo',
      1: 'segunda',
      2: 'terca',
      3: 'quarta',
      4: 'quinta',
      5: 'sexta',
      6: 'sabado'
    };

    const dayOfWeek = dayOfWeekMap[date.getDay()];
    let dayConfig: WorkingHoursConfig | null = null;

    // Verificar se há configurações específicas por dia
    if (agendaConfig.configuracoes_por_dia && typeof agendaConfig.configuracoes_por_dia === 'object') {
      dayConfig = agendaConfig.configuracoes_por_dia[dayOfWeek];
    }

    // Se não há configuração específica para o dia, usar configuração geral
    if (!dayConfig) {
      dayConfig = {
        horario_inicio: agendaConfig.horario_inicio || '08:00',
        horario_fim: agendaConfig.horario_fim || '18:00',
        tem_horario_almoco: agendaConfig.tem_horario_almoco || false,
        horario_almoco_inicio: agendaConfig.horario_almoco_inicio,
        horario_almoco_fim: agendaConfig.horario_almoco_fim
      };
    }

    return dayConfig;
  } catch (error) {
    console.error('Erro ao obter informações de horário:', error);
    return null;
  }
};

// Métodos para lidar com cliques nas células
const handleCellClick = (date: Date, time: string, event?: MouseEvent) => {
  // Não permitir agendar para horários passados
  if (isPastTime(date, time)) {
    return;
  }

  // Não permitir clique se estiver sobre um evento existente
  if (event && isHoveringOverEvent(event)) {
    return;
  }

  // Se o horário está desativado, mostrar aviso mas permitir continuar
  if (!props.isTimeActive(date, time)) {
    const workingHours = getWorkingHoursInfo(date);

    if (workingHours) {
      let message = `Este horário está fora do expediente configurado.\n\n`;
      message += `Horário de funcionamento:\n`;
      message += `${workingHours.horario_inicio} às ${workingHours.horario_fim}`;

      if (workingHours.tem_horario_almoco && workingHours.horario_almoco_inicio && workingHours.horario_almoco_fim) {
        message += `\n\nIntervalo de almoço:\n`;
        message += `${workingHours.horario_almoco_inicio} às ${workingHours.horario_almoco_fim}`;
      }

      message += `\n\nDeseja agendar mesmo assim?`;

      // Usar SweetAlert2 para confirmação
      const { default: Swal } = require('sweetalert2');
      Swal.fire({
        title: 'Horário Fora do Expediente',
        text: message,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Sim, agendar',
        cancelButtonText: 'Cancelar',
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
      }).then((result: any) => {
        if (result.isConfirmed) {
          // Emitir evento personalizado para o componente pai
          const customEvent = new CustomEvent('calendar:cell-clicked', {
            detail: {
              date: date,
              time: time,
              view: 'week',
              outsideWorkingHours: true
            },
            bubbles: true
          });
          document.dispatchEvent(customEvent);
        }
      });

      return;
    }
  }

  // Emitir evento personalizado para o componente pai
  const customEvent = new CustomEvent('calendar:cell-clicked', {
    detail: {
      date: date,
      time: time,
      view: 'week'
    },
    bubbles: true
  });
  document.dispatchEvent(customEvent);
};

// Verificar se o cursor está sobre um evento existente ou seus popups
const isHoveringOverEvent = (event: MouseEvent) => {
  const target = event.target as HTMLElement;

  // Verificar se o elemento clicado ou seus pais têm a classe de evento ou popup
  const isOverEvent = target.closest('.calendar--event') !== null ||
                     target.closest('.single-event-popup') !== null ||
                     target.closest('.more-event-body') !== null ||
                     target.closest('.calendar--action') !== null;

  if (isOverEvent) return true;

  // Verificação adicional: se há popups visíveis na tela, considerar que estamos interagindo com eventos
  const visiblePopups = document.querySelectorAll('.single-event-popup, .more-event-body');
  if (visiblePopups.length > 0) {
    // Se há popups visíveis e o cursor está próximo deles, não mostrar hover card
    for (const popup of visiblePopups) {
      const rect = popup.getBoundingClientRect();
      const mouseX = event.clientX;
      const mouseY = event.clientY;

      // Criar uma área expandida ao redor do popup para evitar conflitos
      const margin = 20;
      if (mouseX >= rect.left - margin &&
          mouseX <= rect.right + margin &&
          mouseY >= rect.top - margin &&
          mouseY <= rect.bottom + margin) {
        return true;
      }
    }
  }

  return false;
};

// Métodos para o hover card
const handleCellHover = (date: Date, time: string, event: MouseEvent) => {
  // SEMPRE verificar primeiro se estamos sobre um evento existente
  if (isHoveringOverEvent(event)) {
    hideHoverCard();
    return;
  }

  // Só mostrar hover card se estivermos em área vazia da célula
  hoverCard.visible = true;
  hoverCard.x = event.clientX;
  hoverCard.y = event.clientY;
  hoverCard.date = date;
  hoverCard.time = time;
};

const hideHoverCard = () => {
  hoverCard.visible = false;
};

const updateHoverCardPosition = (event: MouseEvent) => {
  // Se estiver sobre um evento, esconder o hover card
  if (isHoveringOverEvent(event)) {
    hideHoverCard();
    return;
  }

  if (hoverCard.visible) {
    hoverCard.x = event.clientX;
    hoverCard.y = event.clientY;
  }
};
</script>

<style lang="scss" scoped>
.calendar--week-view-not-in---week {
  opacity: 0.5;
}
.calendar--week-view--header {
  .day-header {
    &.weekindex {
      background-color: #fafafa;
    }
    &.selection {
      background-color: #eff6ff;
    }
  }
}
.calendar--week-view--row {
  .time-cell {
    position: relative;
    transform: translateY(-0.5rem);
  }
  .day-cell {
    &.weekindex {
      background-color: #fafafa;
    }
    &.selection {
      background-color: #eff6ff;
    }
  }
}

/* Estilos para células clicáveis */
.calendar-cell-clickable {
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    background-color: rgba(20, 112, 233, 0.05) !important;

    .calendar-cell-overlay {
      opacity: 1;
    }
  }

  .calendar-cell-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(20, 112, 233, 0.08) 0%, rgba(20, 112, 233, 0.02) 100%);
    opacity: 0;
    transition: opacity 0.2s ease;
    pointer-events: none;
    border-radius: 2px;
  }

  &:active {
    background-color: rgba(20, 112, 233, 0.1) !important;
  }

  &.past-time-cell {
    // Não aplicar opacidade na célula inteira, apenas indicação visual sutil
    position: relative;

    // Overlay que só aparece em áreas vazias
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(249, 115, 22, 0.03);
      pointer-events: none;
      z-index: 0;
    }

    &:hover {
      &::before {
        background-color: rgba(249, 115, 22, 0.05);
      }

      .calendar-cell-overlay {
        background: linear-gradient(135deg, rgba(249, 115, 22, 0.08) 0%, rgba(249, 115, 22, 0.02) 100%);
      }
    }

    &:active {
      &::before {
        background-color: rgba(249, 115, 22, 0.05);
      }
      transform: none;
    }

    // Garantir que eventos funcionem normalmente com z-index maior
    .calendar--event {
      position: relative;
      z-index: 1;
      cursor: pointer;
      opacity: 1;
      pointer-events: auto;

      // Remover qualquer efeito de horário passado dos eventos
      &:hover {
        opacity: 0.8;
      }
    }
  }
}

/* Estilos para o botão de mostrar/ocultar dias */
.btn {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  border-radius: 0.375rem;
  border: 1px solid;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
}

.btn-outline-primary {
  color: #0d6efd;
  border-color: #0d6efd;
  background-color: transparent;
}

.btn-outline-primary:hover {
  color: #fff;
  background-color: #0d6efd;
  border-color: #0d6efd;
}

.btn-outline-primary:focus {
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Garantir que o grid dinâmico funcione corretamente */
.dynamic-grid {
  display: grid !important;
}

/* Sobrescrever qualquer classe CSS que possa estar interferindo */
.calendar--week-view--header.dynamic-grid,
.calendar--week-view--row.dynamic-grid {
  grid-template-columns: var(--dynamic-grid-columns) !important;
}

/* Remover qualquer grid template columns fixo */
.calendar--week-view--header,
.calendar--week-view--row {
  &.dynamic-grid {
    /* Resetar qualquer grid template columns que possa estar sendo aplicado */
    grid-template-columns: var(--dynamic-grid-columns) !important;

    /* Garantir que não há classes conflitantes */
    &.grid-cols-3-repeat-7-minmax-0v1fr-3rem {
      grid-template-columns: var(--dynamic-grid-columns) !important;
    }
  }
}

/* Estilos para horários desabilitados */
.disabled-time-cell {
  cursor: default !important;
  position: relative;

  // Overlay para indicar que o horário está desabilitado
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(156, 163, 175, 0.08);
    pointer-events: none;
    z-index: 1;
  }

  // Reduzir opacidade do conteúdo
  span {
    opacity: 0.4;
  }

  // Não permitir hover em horários desabilitados
  &:hover {
    background-color: transparent !important;

    .calendar-cell-overlay {
      opacity: 0 !important;
    }

    &::after {
      background-color: rgba(156, 163, 175, 0.12);
    }
  }

  // Não permitir active em horários desabilitados
  &:active {
    background-color: transparent !important;
    transform: none !important;
  }

  // Garantir que eventos funcionem normalmente
  .calendar--event {
    position: relative;
    z-index: 2;
    cursor: pointer;
    opacity: 1;
    pointer-events: auto;
  }
}

/* Permitir que as células cresçam verticalmente para acomodar múltiplos eventos */
.day-cell {
  min-height: 2.5rem; /* Altura mínima reduzida para economizar espaço */
  height: auto !important; /* Permitir crescimento automático */
  overflow: visible; /* Não cortar conteúdo */
  padding: 0.25rem; /* Pequeno padding para espaçamento */

  /* Garantir que o conteúdo interno possa crescer */
  > div {
    width: 100%;
    height: auto;
    min-height: 100%;
  }
}

/* Garantir que o componente de eventos possa crescer */
.calendar--event {
  position: relative;
  width: 100%;
  height: auto;
  min-height: fit-content;
}

/* Ajustar o overlay para não interferir com os eventos */
.calendar-cell-overlay {
  pointer-events: none;
  z-index: 0;
}
</style>
