<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Carbon\Carbon;

class CustomPasswordResetController extends Controller
{
    /**
     * Enviar link de reset de senha
     */
    public function sendResetLink(Request $request): JsonResponse
    {
        $request->validate([
            'email' => ['required', 'email'],
        ]);

        // Buscar usuário pelo email
        $user = User::where('email', $request->email)->first();

        if (!$user) {
            throw ValidationException::withMessages([
                'email' => ['Não encontramos nenhuma conta com este endereço de e-mail.'],
            ]);
        }

        // Gerar token único
        $token = Str::random(64);

        // Salvar token na tabela password_reset_tokens
        DB::table('password_reset_tokens')->updateOrInsert(
            ['email' => $request->email],
            [
                'email' => $request->email,
                'token' => Hash::make($token),
                'created_at' => Carbon::now()
            ]
        );

        // URL para reset (frontend)
        $resetUrl = config('app.lumivision_url') . '/redefinir-senha?token=' . $token . '&email=' . urlencode($request->email);

        // Enviar e-mail
        try {
            Mail::send('emails.password-reset', [
                'user' => $user,
                'resetUrl' => $resetUrl,
                'token' => $token
            ], function ($message) use ($user) {
                $message->to($user->email, $user->name)
                        ->subject('Redefinir Senha - LUMI Vision');
            });

            return response()->json([
                'status' => 'success',
                'message' => 'Link de redefinição de senha enviado para seu e-mail!'
            ]);

        } catch (\Exception $e) {
            Log::error('Erro ao enviar e-mail de reset de senha: ' . $e->getMessage());

            return response()->json([
                'status' => 'error',
                'message' => 'Erro ao enviar e-mail. Tente novamente em alguns minutos.'
            ], 500);
        }
    }

    /**
     * Validar token de reset
     */
    public function validateResetToken(Request $request): JsonResponse
    {
        $request->validate([
            'token' => ['required'],
            'email' => ['required', 'email'],
        ]);

        $passwordReset = DB::table('password_reset_tokens')
            ->where('email', $request->email)
            ->first();

        if (!$passwordReset) {
            return response()->json([
                'status' => 'error',
                'message' => 'Token inválido ou expirado.'
            ], 400);
        }

        // Verificar se o token não expirou (60 minutos)
        if (Carbon::parse($passwordReset->created_at)->addMinutes(60)->isPast()) {
            DB::table('password_reset_tokens')->where('email', $request->email)->delete();
            
            return response()->json([
                'status' => 'error',
                'message' => 'Token expirado. Solicite um novo link de redefinição.'
            ], 400);
        }

        // Verificar se o token é válido
        if (!Hash::check($request->token, $passwordReset->token)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Token inválido.'
            ], 400);
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Token válido.'
        ]);
    }

    /**
     * Redefinir senha
     */
    public function resetPassword(Request $request): JsonResponse
    {
        $request->validate([
            'token' => ['required'],
            'email' => ['required', 'email'],
            'password' => ['required', 'min:8', 'confirmed'],
        ]);

        $passwordReset = DB::table('password_reset_tokens')
            ->where('email', $request->email)
            ->first();

        if (!$passwordReset) {
            return response()->json([
                'status' => 'error',
                'message' => 'Token inválido ou expirado.'
            ], 400);
        }

        // Verificar se o token não expirou (60 minutos)
        if (Carbon::parse($passwordReset->created_at)->addMinutes(60)->isPast()) {
            DB::table('password_reset_tokens')->where('email', $request->email)->delete();
            
            return response()->json([
                'status' => 'error',
                'message' => 'Token expirado. Solicite um novo link de redefinição.'
            ], 400);
        }

        // Verificar se o token é válido
        if (!Hash::check($request->token, $passwordReset->token)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Token inválido.'
            ], 400);
        }

        // Buscar usuário
        $user = User::where('email', $request->email)->first();

        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Usuário não encontrado.'
            ], 400);
        }

        // Atualizar senha
        $user->update([
            'password' => Hash::make($request->password),
            'remember_token' => Str::random(60),
        ]);

        // Remover token usado
        DB::table('password_reset_tokens')->where('email', $request->email)->delete();

        return response()->json([
            'status' => 'success',
            'message' => 'Senha redefinida com sucesso! Você já pode fazer login com sua nova senha.'
        ]);
    }
}
