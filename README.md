# 🦷 Lumi Ortho System - API

[![<PERSON><PERSON>](https://img.shields.io/badge/Laravel-11.x-red.svg)](https://laravel.com)
[![PHP](https://img.shields.io/badge/PHP-8.2+-blue.svg)](https://php.net)
[![JWT](https://img.shields.io/badge/JWT-Auth-green.svg)](https://jwt-auth.readthedocs.io)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

Sistema completo de gestão para clínicas ortodônticas com funcionalidades avançadas de multi-tenancy, gestão de pacientes, modelos 3D, financeiro e muito mais.

## 📋 Índice

- [Características](#-características)
- [Tecnologias](#-tecnologias)
- [Instalação](#-instalação)
- [Configuração](#-configuração)
- [Autenticação](#-autenticação)
- [Multi-Tenancy](#-multi-tenancy)
- [<PERSON>ó<PERSON><PERSON>](#-módulos-principais)
- [API Endpoints](#-api-endpoints)
- [Testes](#-testes)
- [Deployment](#-deployment)
- [Contribuição](#-contribuição)

## ✨ Características

### 🏥 **Multi-Tenancy por Clínica**
- Isolamento completo de dados entre clínicas
- Global Scopes automáticos para segurança
- Administração centralizada do sistema

### 👥 **Gestão de Usuários e Permissões**
- Autenticação JWT com claims customizados
- Níveis de acesso: System Admin, Clínica Admin, Usuário
- Middleware de autorização granular

### 🦷 **Gestão Ortodôntica Completa**
- Cadastro e acompanhamento de pacientes
- Gestão de dentistas e especialistas
- Histórico detalhado de tratamentos
- Sistema de consultas e agendamentos

### 💰 **Módulo Financeiro**
- Contas a pagar e receber
- Orçamentos e aprovações
- Controle de parcelas e vencimentos
- Relatórios financeiros

### 🎯 **Modelos 3D e Diagnósticos**
- Upload e visualização de modelos 3D
- Sistema de tags para diagnósticos
- Integração com ferramentas ortodônticas

### 📊 **Auditoria e Histórico**
- Log completo de todas as ações (ActionHistory)
- Rastreamento de mudanças em tempo real
- Relatórios de atividades por usuário/clínica

### 🔧 **Recursos Avançados**
- Upload e processamento de imagens
- Sistema de formulários personalizáveis
- Mentoria e acompanhamento
- Integração com sistemas externos

## 🛠 Tecnologias

### Backend
- **Laravel 11.x** - Framework PHP moderno
- **PHP 8.2+** - Linguagem de programação
- **MySQL/PostgreSQL** - Banco de dados relacional
- **JWT Auth** - Autenticação stateless
- **Spatie Activity Log** - Auditoria de ações

### Pacotes Principais
- `tymon/jwt-auth` - Autenticação JWT
- `intervention/image` - Processamento de imagens
- `spatie/laravel-activitylog` - Log de atividades
- `laravel/sanctum` - API tokens
- `pestphp/pest` - Framework de testes

### Ferramentas de Desenvolvimento
- **Laravel Pint** - Code styling
- **Pest** - Testes unitários e de feature
- **Laravel Sail** - Ambiente Docker
- **Spatie Ignition** - Debug avançado

## 🚀 Instalação

### Pré-requisitos
- PHP 8.2 ou superior
- Composer
- MySQL 8.0+ ou PostgreSQL 13+
- Node.js 18+ (para assets)

### 1. Clone o Repositório
```bash
git clone https://github.com/seu-usuario/lumi-api4.git
cd lumi-api4
```

### 2. Instale as Dependências
```bash
composer install
```

### 3. Configure o Ambiente
```bash
cp .env.example .env
php artisan key:generate
php artisan jwt:secret
```

### 4. Configure o Banco de Dados
Edite o arquivo `.env`:
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=lumi_ortho
DB_USERNAME=seu_usuario
DB_PASSWORD=sua_senha
```

### 5. Execute as Migrations
```bash
php artisan migrate
```

### 6. Popule o Banco (Opcional)
```bash
php artisan db:seed
```

### 7. Inicie o Servidor
```bash
php artisan serve
```

A API estará disponível em `http://localhost:8000`

## ⚙️ Configuração

### Configuração de E-mail (SMTP)
```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=sua-senha-de-app
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="Lumi Ortho System"
```

### Configuração JWT
```env
JWT_SECRET=sua_chave_secreta_jwt
JWT_TTL=1440  # 24 horas
JWT_REFRESH_TTL=20160  # 2 semanas
```

### Configuração de Storage
```env
FILESYSTEM_DISK=local
# Para produção, configure S3 ou similar
```

## 🔐 Autenticação

### Login
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "senha123"
}
```

**Resposta:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 86400,
  "user": {
    "id": 1,
    "name": "João Silva",
    "email": "<EMAIL>",
    "clinica": {
      "id": 1,
      "nome": "Clínica Exemplo",
      "slug": "clinica-exemplo"
    },
    "system_admin": false,
    "clinica_admin": true
  }
}
```

### Uso do Token
```http
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

### Refresh Token
```http
POST /auth/refresh
Authorization: Bearer seu_token_atual
```

## 🏢 Multi-Tenancy

O sistema implementa multi-tenancy por clínica usando **Global Scopes**:

### Como Funciona
1. **Usuários** pertencem a uma clínica específica
2. **Global Scopes** filtram automaticamente dados por `clinica_id`
3. **System Admins** podem acessar dados de todas as clínicas
4. **Clínica Admins** gerenciam apenas sua clínica

### Modelos com Multi-Tenancy
- `Paciente` - Pacientes da clínica
- `Dentista` - Dentistas da clínica
- `Consulta` - Consultas da clínica
- `FinanceiroReceber` - Contas a receber
- `FinanceiroPagar` - Contas a pagar
- `Orcamento` - Orçamentos
- `ServicoProduto` - Serviços e produtos
- `Consultorio` - Consultórios

### Bypass do Scope (Admins)
```php
// Para admins acessarem dados de outras clínicas
$pacientes = Paciente::withoutGlobalScope(ClinicaScope::class)->get();
```

## 📦 Módulos Principais

### 👥 Gestão de Pacientes
- **Modelo:** `Paciente`
- **Controller:** `PacientesController`
- **Funcionalidades:**
  - CRUD completo de pacientes
  - Upload de fotos de perfil
  - Histórico de tratamentos
  - Formulários de boas-vindas
  - Contatos e responsáveis

### 🦷 Gestão de Dentistas
- **Modelo:** `Dentista`
- **Controller:** `Dentistas`
- **Funcionalidades:**
  - Cadastro de ortodontistas
  - Matrícula automática
  - Especialidades e contatos
  - Vinculação com usuários

### 💰 Módulo Financeiro
- **Modelos:** `FinanceiroReceber`, `FinanceiroPagar`
- **Controllers:** `FinanceiroReceberController`, `FinanceiroPagarController`
- **Funcionalidades:**
  - Contas a pagar e receber
  - Controle de parcelas
  - Status de pagamento
  - Relatórios financeiros

### 🎯 Modelos 3D
- **Modelo:** `Modelo3D`
- **Controller:** `Modelo3DController`
- **Funcionalidades:**
  - Upload de arquivos 3D
  - Visualização integrada
  - Tags de diagnóstico
  - Histórico por paciente

### 📋 Orçamentos
- **Modelo:** `Orcamento`
- **Controller:** `OrcamentoController`
- **Funcionalidades:**
  - Criação de orçamentos
  - Aprovação/rejeição
  - Conversão em tratamento
  - Controle de validade

## 🔗 API Endpoints

### Autenticação
```
POST   /auth/login          # Login
POST   /auth/logout         # Logout
POST   /auth/refresh        # Refresh token
POST   /auth/me             # Dados do usuário
```

### Pacientes
```
GET    /pacientes           # Listar pacientes
POST   /pacientes           # Criar paciente
GET    /pacientes/{id}      # Buscar paciente
PUT    /pacientes/{id}      # Atualizar paciente
DELETE /pacientes/{id}      # Deletar paciente
GET    /pacientes/search    # Buscar pacientes
```

### Dentistas
```
GET    /dentistas           # Listar dentistas
POST   /dentistas           # Criar dentista
GET    /dentistas/{id}      # Buscar dentista
PUT    /dentistas/{id}      # Atualizar dentista
DELETE /dentistas/{id}      # Deletar dentista
```

### Financeiro
```
GET    /financeiro-receber  # Contas a receber
POST   /financeiro-receber  # Criar conta
PUT    /financeiro-receber/{id}/pagar  # Marcar como pago

GET    /financeiro-pagar    # Contas a pagar
POST   /financeiro-pagar    # Criar conta
PUT    /financeiro-pagar/{id}/pagar   # Marcar como pago
```

### Modelos 3D
```
GET    /modelo3d            # Listar modelos
POST   /modelo3d            # Upload modelo
GET    /modelo3d/{id}       # Buscar modelo
DELETE /modelo3d/{id}       # Deletar modelo
```

### Administração (System Admin)
```
GET    /clinicas            # Listar clínicas
POST   /clinicas            # Criar clínica
GET    /users               # Listar usuários
GET    /action-history      # Histórico de ações
```

## 🧪 Testes

### Executar Todos os Testes
```bash
php artisan test
```

### Executar Testes Específicos
```bash
# Testes de Feature
php artisan test --testsuite=Feature

# Testes Unitários
php artisan test --testsuite=Unit

# Teste específico
php artisan test tests/Feature/Auth/LoginTest.php
```

### Coverage
```bash
php artisan test --coverage
```

## 🚀 Deployment

### Preparação para Produção
```bash
# Otimizar autoloader
composer install --optimize-autoloader --no-dev

# Cache de configuração
php artisan config:cache

# Cache de rotas
php artisan route:cache

# Cache de views
php artisan view:cache

# Executar migrations
php artisan migrate --force
```

### Variáveis de Ambiente (Produção)
```env
APP_ENV=production
APP_DEBUG=false
APP_URL=https://api.lumiorthosystem.com.br

# Database
DB_CONNECTION=mysql
DB_HOST=seu-host-producao
DB_DATABASE=lumi_ortho_prod
DB_USERNAME=usuario_prod
DB_PASSWORD=senha_segura_prod

# Cache
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

# Mail
MAIL_MAILER=smtp
MAIL_HOST=smtp.mailgun.org
# ... outras configurações de email
```

### Docker (Opcional)
```bash
# Usando Laravel Sail
./vendor/bin/sail up -d
```

## 📚 Documentação Adicional

- [Configuração SMTP](CONFIGURACAO_SMTP.md)
- [ActionHistory System](docs/ActionHistory_Implementation_Summary.md)
- [Testing Guide](docs/ActionHistory_Testing_Guide.md)
- [User Search Examples](docs/UserSearch_Filter_Examples.md)

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/nova-funcionalidade`)
3. Commit suas mudanças (`git commit -am 'Adiciona nova funcionalidade'`)
4. Push para a branch (`git push origin feature/nova-funcionalidade`)
5. Abra um Pull Request

### Padrões de Código
```bash
# Verificar estilo de código
./vendor/bin/pint --test

# Corrigir estilo automaticamente
./vendor/bin/pint
```

## 📄 Licença

Este projeto está licenciado sob a Licença MIT - veja o arquivo [LICENSE](LICENSE) para detalhes.

## 🆘 Suporte

- **Email:** <EMAIL>
- **Documentação:** [docs.lumiorthosystem.com.br](https://docs.lumiorthosystem.com.br)
- **Issues:** [GitHub Issues](https://github.com/seu-usuario/lumi-api4/issues)

---

**Desenvolvido com ❤️ para revolucionar a ortodontia digital**
