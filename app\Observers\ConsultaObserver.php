<?php

namespace App\Observers;

use App\Models\Consulta;
use App\Models\Paciente;
use Illuminate\Support\Facades\DB;

class ConsultaObserver
{
    /**
     * Dispara sempre que uma consulta é criada, atualizada ou deletada
     */
    public function saved(Consulta $consulta)
    {
        $this->atualizarCamposPaciente($consulta->paciente_id);
    }

    public function deleted(Consulta $consulta)
    {
        $this->atualizarCamposPaciente($consulta->paciente_id);
    }

    /**
     * Atualiza os campos ultima_consulta e proxima_consulta do paciente
     */
    protected function atualizarCamposPaciente($pacienteId)
    {
        $ultima = \App\Models\Consulta::where('paciente_id', $pacienteId)
            ->where('status', '!=', 'cancelada')
            ->where('horario', '<=', now())
            ->orderByDesc('horario')
            ->value('horario');

        $proxima = \App\Models\Consulta::where('paciente_id', $pacienteId)
            ->where('status', '!=', 'cancelada')
            ->where('horario', '>=', now())
            ->orderBy('horario')
            ->value('horario');

        \App\Models\Paciente::where('id', $pacienteId)->update([
            'ultima_consulta' => $ultima,
            'proxima_consulta' => $proxima,
        ]);
    }
}
