# Comando: Mi<PERSON><PERSON> Consultórios para Clínicas

## Descrição

Este comando foi criado para resolver o problema de clínicas que ainda não possuem consultórios cadastrados após a implementação do sistema de multi-consultórios.

O comando realiza as seguintes ações:

1. **Identifica clínicas sem consultórios**: Verifica todas as clínicas do sistema e identifica aquelas que não possuem nenhum consultório cadastrado.

2. **Cria consultório padrão**: Para cada clínica sem consultório, cria um consultório padrão com as seguintes características:
   - **Nome**: "Consultório 1"
   - **Descrição**: "Consultório padrão criado automaticamente"
   - **Cor**: `#1e3a8a` (azul escuro primary)
   - **Ícone**: `fas fa-clinic-medical` (ícone de clínica)
   - **Status**: Ativo
   - **Ordem**: 1

3. **Vincula consultas existentes**: <PERSON>ca todas as consultas "soltas" (sem `consultorio_id`) daquela clínica e as vincula ao consultório recém-criado.

## Como Usar

### Executar o comando

```bash
php artisan consultorios:migrar-clinicas
```

### Saída do Comando

O comando exibe:
- Barra de progresso durante o processamento
- Tabela resumo com:
  - Total de clínicas no sistema
  - Quantidade de clínicas sem consultório
  - Quantidade de consultórios criados
  - Quantidade de consultas vinculadas

### Exemplo de Saída

```
🏥 Iniciando migração de consultórios...

📊 Total de clínicas encontradas: 15

 15/15 [============================] 100%

✅ Migração concluída!

+--------------------------+------------+
| Métrica                  | Quantidade |
+--------------------------+------------+
| Total de clínicas        | 15         |
| Clínicas sem consultório | 8          |
| Consultórios criados     | 8          |
| Consultas vinculadas     | 142        |
+--------------------------+------------+

🎉 8 consultórios criados com sucesso!
📋 142 consultas vinculadas aos novos consultórios!
```

## Segurança

- O comando utiliza **transações de banco de dados** para garantir a consistência dos dados
- Se ocorrer algum erro durante o processamento de uma clínica, a transação é revertida (rollback) e o comando continua processando as demais clínicas
- Clínicas que já possuem consultórios são automaticamente ignoradas

## Quando Executar

Este comando deve ser executado:
- Após a implementação do sistema de multi-consultórios
- Sempre que identificar clínicas sem consultórios cadastrados
- Pode ser executado múltiplas vezes sem problemas (é idempotente)

## Observações

- O comando **não** sobrescreve consultórios existentes
- O comando **não** altera consultas que já possuem `consultorio_id` definido
- É seguro executar o comando em produção
- Recomenda-se executar em horários de baixo tráfego para evitar impacto na performance

## Código Fonte

O código do comando está localizado em:
```
app/Console/Commands/MigrarConsultoriosClinicas.php
```

