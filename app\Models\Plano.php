<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Plano extends Model
{
    use HasFactory, Notifiable, LogsActivity;

    protected $table = 'planos';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'nome',
        'descricao',
        'cor',
        'ativo',
        'auto_registro',
        'modulo_clinica',
        'modulo_ortodontia',
        'quantidade_usuarios',
        'quantidade_ortodontistas',
        'quantidade_agendas',
        'quantidade_cadeiras',
        'meses_fidelidade_minima',
        'dias_gratuitos',
        'quantidade_mentorias_mensais',
        'valor_mensal',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'ativo' => 'boolean',
            'auto_registro' => 'boolean',
            'modulo_clinica' => 'boolean',
            'modulo_ortodontia' => 'boolean',
            'quantidade_usuarios' => 'integer',
            'quantidade_ortodontistas' => 'integer',
            'quantidade_agendas' => 'integer',
            'quantidade_cadeiras' => 'integer',
            'meses_fidelidade_minima' => 'integer',
            'dias_gratuitos' => 'integer',
            'quantidade_mentorias_mensais' => 'integer',
            'valor_mensal' => 'decimal:2',
        ];
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['*']);
    }

    /**
     * Relacionamentos
     */
    public function clinicas(): HasMany
    {
        return $this->hasMany(Clinica::class);
    }

    /**
     * Scopes
     */
    public function scopeAtivos($query)
    {
        return $query->where('ativo', true);
    }

    public function scopeComOrtodontia($query)
    {
        return $query->where('modulo_ortodontia', true);
    }

    public function scopeAutoRegistro($query)
    {
        return $query->where('auto_registro', true);
    }

    /**
     * Accessors
     */
    public function getValorMensalFormatadoAttribute()
    {
        if ($this->valor_mensal === null) {
            return 'Gratuito';
        }
        
        return 'R$ ' . number_format($this->valor_mensal, 2, ',', '.');
    }

    public function getQuantidadeUsuariosTextoAttribute()
    {
        return $this->quantidade_usuarios === null ? 'Ilimitado' : $this->quantidade_usuarios;
    }

    public function getQuantidadeOrtodontistasTextoAttribute()
    {
        return $this->quantidade_ortodontistas === null ? 'Ilimitado' : $this->quantidade_ortodontistas;
    }

    public function getQuantidadeAgendasTextoAttribute()
    {
        return $this->quantidade_agendas === null ? 'Ilimitado' : $this->quantidade_agendas;
    }

    public function getQuantidadeCadeirasTextoAttribute()
    {
        return $this->quantidade_cadeiras === null ? 'Ilimitado' : $this->quantidade_cadeiras;
    }

    public function getQuantidadeMentoriasMensaisTextoAttribute()
    {
        if (!$this->modulo_ortodontia) {
            return 'N/A';
        }
        
        return $this->quantidade_mentorias_mensais === null ? 'Ilimitado' : $this->quantidade_mentorias_mensais;
    }

    /**
     * Scopes
     */
    public function scopeAtivo($query)
    {
        return $query->where('ativo', true);
    }

    /**
     * Métodos auxiliares
     */
    public function temModuloOrtodontia(): bool
    {
        return $this->modulo_ortodontia;
    }

    public function ehGratuito(): bool
    {
        return $this->valor_mensal === null || $this->valor_mensal == 0;
    }

    public function temLimiteUsuarios(): bool
    {
        return $this->quantidade_usuarios !== null;
    }
}
