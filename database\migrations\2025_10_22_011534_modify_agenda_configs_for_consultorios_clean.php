<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Primeiro, vamos remover a foreign key constraint do user_id se existir
        try {
            Schema::table('agenda_configs', function (Blueprint $table) {
                $table->dropForeign(['user_id']);
            });
        } catch (Exception $e) {
            // Ignorar se não existir
        }

        // Segundo, remover o índice único do user_id se existir
        try {
            Schema::table('agenda_configs', function (Blueprint $table) {
                $table->dropUnique(['user_id']);
            });
        } catch (Exception $e) {
            // Ignorar se não existir
        }

        // Terceiro, adicionar as novas colunas se não existirem e modificar user_id
        Schema::table('agenda_configs', function (Blueprint $table) {
            // Adicionar coluna consultorio_id se não existir
            if (!Schema::hasColumn('agenda_configs', 'consultorio_id')) {
                $table->unsignedBigInteger('consultorio_id')->nullable()->after('user_id');
            }

            // Adicionar campo para indicar se a configuração está ativa se não existir
            if (!Schema::hasColumn('agenda_configs', 'ativo')) {
                $table->boolean('ativo')->default(true)->after('antecedencia_maxima_agendamento');
            }

            // Tornar user_id nullable
            $table->unsignedBigInteger('user_id')->nullable()->change();
        });

        // Quarto, adicionar foreign keys e índices
        Schema::table('agenda_configs', function (Blueprint $table) {
            // Recriar foreign key do user_id (sem unique)
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            // Adicionar chave estrangeira para consultório
            $table->foreign('consultorio_id')->references('id')->on('consultorios')->onDelete('cascade');

            // Criar índice único para consultório (apenas um config ativo por consultório)
            $table->unique(['consultorio_id', 'ativo'], 'agenda_configs_consultorio_ativo_unique');

            // Adicionar índices para performance
            $table->index(['consultorio_id']);
            $table->index(['user_id', 'consultorio_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Primeiro, remover índices e foreign keys
        Schema::table('agenda_configs', function (Blueprint $table) {
            $table->dropUnique('agenda_configs_consultorio_ativo_unique');
            $table->dropIndex(['consultorio_id']);
            $table->dropIndex(['user_id', 'consultorio_id']);
            $table->dropForeign(['consultorio_id']);
            $table->dropForeign(['user_id']);
        });

        // Segundo, remover colunas adicionadas
        Schema::table('agenda_configs', function (Blueprint $table) {
            $table->dropColumn(['consultorio_id', 'ativo']);
        });

        // Terceiro, restaurar user_id como não nullable
        Schema::table('agenda_configs', function (Blueprint $table) {
            $table->unsignedBigInteger('user_id')->nullable(false)->change();
        });

        // Quarto, restaurar constraint unique e foreign key do user_id
        Schema::table('agenda_configs', function (Blueprint $table) {
            $table->unique('user_id');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }
};
