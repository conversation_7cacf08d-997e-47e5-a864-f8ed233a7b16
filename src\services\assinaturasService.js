import axios from '@/services/axios';

export const assinaturasService = {
  /**
   * Obter histórico de períodos de assinatura de uma clínica
   */
  async getAssinaturas(clinicaId, filters = {}) {
    try {
      const params = new URLSearchParams();

      if (filters.status) params.append('status', filters.status);
      if (filters.apenas_historico) params.append('apenas_historico', filters.apenas_historico);
      if (filters.periodo_atual) params.append('periodo_atual', filters.periodo_atual);

      const response = await axios.get(`/clinicas/${clinicaId}/assinaturas?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar histórico de assinaturas:', error);
      throw error;
    }
  },

  /**
   * Obter assinatura específica
   */
  async getAssinatura(clinicaId, assinaturaId) {
    try {
      const response = await axios.get(`/clinicas/${clinicaId}/assinaturas/${assinaturaId}`);
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar assinatura:', error);
      throw error;
    }
  },

  /**
   * Criar nova assinatura
   */
  async createAssinatura(clinicaId, data) {
    try {
      const response = await axios.post(`/clinicas/${clinicaId}/assinaturas`, data);
      return response.data;
    } catch (error) {
      console.error('Erro ao criar assinatura:', error);
      throw error;
    }
  },

  /**
   * Atualizar assinatura
   */
  async updateAssinatura(clinicaId, assinaturaId, data) {
    try {
      const response = await axios.put(`/clinicas/${clinicaId}/assinaturas/${assinaturaId}`, data);
      return response.data;
    } catch (error) {
      console.error('Erro ao atualizar assinatura:', error);
      throw error;
    }
  },

  /**
   * Cancelar assinatura ativa
   */
  async cancelarAssinatura(clinicaId, data) {
    try {
      const response = await axios.post(`/clinicas/${clinicaId}/cancelar-assinatura`, data);
      return response.data;
    } catch (error) {
      console.error('Erro ao cancelar assinatura:', error);
      throw error;
    }
  },

  /**
   * Obter assinatura ativa da clínica
   */
  async getAssinaturaAtiva(clinicaId) {
    try {
      const response = await axios.get(`/clinicas/${clinicaId}/assinatura-ativa`);
      return response.data;
    } catch (error) {
      if (error.response?.status === 404) {
        return null; // Nenhuma assinatura ativa
      }
      console.error('Erro ao buscar assinatura ativa:', error);
      throw error;
    }
  },

  /**
   * Alterar plano da clínica
   */
  async alterarPlano(clinicaId, novoPlanoId, dataInicio, motivoAlteracao = null) {
    try {
      const response = await axios.post(`/clinicas/${clinicaId}/alterar-plano`, {
        novo_plano_id: novoPlanoId,
        data_inicio: dataInicio,
        motivo_alteracao: motivoAlteracao
      });
      return response.data;
    } catch (error) {
      console.error('Erro ao alterar plano:', error);

      // Verificar se é erro de validação (422) - mensagem já vem formatada do backend
      if (error.response?.status === 422) {
        const errorMessage = error.response.data.message || 'Erro de validação';
        throw new Error(errorMessage);
      }

      // Verificar se é erro de constraint única (500) - fallback caso a validação não tenha funcionado
      if (error.response?.status === 500 && error.response?.data?.message?.includes('Duplicate entry')) {
        throw new Error('A assinatura já foi alterada hoje. A assinatura somente pode ser alterada daqui 30 dias.');
      }

      // Para outros erros, manter o comportamento original
      throw error;
    }
  },

  /**
   * Validar dados de assinatura
   */
  validateAssinaturaData(data) {
    const errors = {};

    if (!data.plano_id) {
      errors.plano_id = 'Plano é obrigatório';
    }

    if (!data.data_inicio) {
      errors.data_inicio = 'Data de início é obrigatória';
    }

    if (!data.data_fim) {
      errors.data_fim = 'Data de fim é obrigatória';
    }

    if (data.data_inicio && data.data_fim && data.data_inicio >= data.data_fim) {
      errors.data_fim = 'Data de fim deve ser posterior à data de início';
    }

    if (data.valor_mensal === null || data.valor_mensal === undefined || data.valor_mensal < 0) {
      errors.valor_mensal = 'Valor mensal deve ser maior ou igual a zero';
    }

    if (!data.periodo_cobranca) {
      errors.periodo_cobranca = 'Período de cobrança é obrigatório';
    }

    if (!data.dia_cobranca || data.dia_cobranca < 1 || data.dia_cobranca > 31) {
      errors.dia_cobranca = 'Dia de cobrança deve estar entre 1 e 31';
    }

    if (data.meses_fidelidade === null || data.meses_fidelidade === undefined || data.meses_fidelidade < 0) {
      errors.meses_fidelidade = 'Meses de fidelidade deve ser maior ou igual a zero';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  },

  /**
   * Formatar status da assinatura
   */
  formatStatus(status) {
    const statusMap = {
      'ativo': { text: 'Ativo', color: 'success' },
      'cancelado': { text: 'Cancelado', color: 'danger' },
      'suspenso': { text: 'Suspenso', color: 'warning' },
      'trial': { text: 'Trial', color: 'info' }
    };

    return statusMap[status] || { text: status, color: 'secondary' };
  },

  /**
   * Formatar período de cobrança
   */
  formatPeriodoCobranca(periodo) {
    const periodoMap = {
      'mensal': 'Mensal',
      'trimestral': 'Trimestral',
      'semestral': 'Semestral',
      'anual': 'Anual'
    };

    return periodoMap[periodo] || periodo;
  },

  /**
   * Calcular valor por período
   */
  calcularValorPorPeriodo(valorMensal, periodo) {
    const multiplicador = {
      'mensal': 1,
      'trimestral': 3,
      'semestral': 6,
      'anual': 12
    };

    return valorMensal * (multiplicador[periodo] || 1);
  },

  /**
   * Formatar valor monetário
   */
  formatCurrency(value) {
    if (value === null || value === undefined) {
      return 'Gratuito';
    }
    
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  },

  /**
   * Calcular dias restantes da assinatura
   */
  calcularDiasRestantes(dataFim) {
    if (!dataFim) return 0;
    
    const hoje = new Date();
    const fim = new Date(dataFim);
    const diffTime = fim - hoje;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return Math.max(0, diffDays);
  },

  /**
   * Verificar se assinatura está vigente
   */
  isVigente(dataInicio, dataFim) {
    if (!dataInicio || !dataFim) return false;
    
    const hoje = new Date();
    const inicio = new Date(dataInicio);
    const fim = new Date(dataFim);
    
    return hoje >= inicio && hoje <= fim;
  },

  /**
   * Obter próxima data de cobrança
   */
  calcularProximaCobranca(diaCobranca) {
    const hoje = new Date();
    const proximaCobranca = new Date(hoje.getFullYear(), hoje.getMonth(), diaCobranca);
    
    // Se o dia já passou neste mês, próxima cobrança é no próximo mês
    if (proximaCobranca <= hoje) {
      proximaCobranca.setMonth(proximaCobranca.getMonth() + 1);
    }
    
    return proximaCobranca;
  },

  /**
   * Verificar se uma assinatura pode ser cancelada
   */
  podeSerCancelada(assinatura) {
    if (!assinatura || assinatura.status !== 'ativo') {
      return false;
    }

    // Verificar se ainda não venceu
    const dataFim = new Date(assinatura.data_fim);
    const hoje = new Date();

    return dataFim >= hoje;
  }
};

export default assinaturasService;
