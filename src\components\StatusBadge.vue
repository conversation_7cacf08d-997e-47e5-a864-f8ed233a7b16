<template>
  <span :class="badgeClass">
    {{ status }}
  </span>
</template>

<script>
export default {
  name: 'StatusBadge',
  props: {
    status: {
      type: String,
      required: true
    },
    type: {
      type: String,
      default: 'general', // 'general' ou 'fidelidade'
      validator: (value) => ['general', 'fidelidade'].includes(value)
    }
  },
  computed: {
    badgeClass() {
      const baseClass = 'badge badge-sm';
      
      if (this.type === 'fidelidade') {
        switch (this.status.toLowerCase()) {
          case 'período de teste':
            return `${baseClass} bg-gradient-info`;
          case 'em fidelidade':
            return `${baseClass} bg-gradient-warning`;
          case 'fidelidade cumprida':
            return `${baseClass} bg-gradient-success`;
          default:
            return `${baseClass} bg-gradient-secondary`;
        }
      } else {
        // type === 'general'
        switch (this.status.toLowerCase()) {
          case 'ativo':
            return `${baseClass} bg-gradient-success`;
          case 'pouco ativo':
            return `${baseClass} bg-gradient-warning`;
          case 'inativo':
            return `${baseClass} bg-gradient-danger`;
          default:
            return `${baseClass} bg-gradient-secondary`;
        }
      }
    }
  }
};
</script>

<style scoped>
.badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}
</style>
