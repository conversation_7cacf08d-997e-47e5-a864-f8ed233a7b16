<template>
  <div
    ref="eventContainer"
    :data-date-start="datetime_start"
    :data-date-end="datetime_end"
    :data-evendate="eventDate"
    class="w-full"
  >
    <template v-if="slots.eventCard">
      <component
        :is="slots.eventCard"
        :date="eventDate"
        :time="eventTime"
        :cardEvent="RdvsPkg"
      />
    </template>
    <template v-else>
      <!-- Mostrar todos os eventos empilhados verticalmente -->
      <div
        v-if="RdvsPkg.length > 0"
        class="w-full flex flex-col gap-2"
      >
        <!-- Iterar sobre todos os eventos -->
        <div
          v-for="(rdv, index) in RdvsPkg"
          :key="index"
          class="relative"
        >
          <div
            :ref="el => { if (el) eventCards[index] = el as HTMLElement }"
            data-widget-item="event--button"
            class="cursor-pointer rounded event-card hover:opacity-80 active:animate-pulse calendar--event"
            @click.stop.prevent="openEvtList(index)"
          >
            <div class="event-body select-none w-full p-0dt375">
              <div class="single-event-inf">
                <!-- Linha 1: Horário + Status -->
                <div class="flex items-center gap-1 mb-1">
                  <span
                    :data-rdv-date="rdv.date"
                    :title="
                      isoStringToDate(rdv.date).toLocaleString($i18n.locale)
                    "
                    class="text-left text-09101D font-medium text-xs calendar--event-time"
                  >
                    {{
                      timeFormat(
                        `${hours(rdv.date)}:${minutes(rdv.date)}`,
                        true
                      )
                    }}
                  </span>
                  <span
                    class="text-left text-1dt563 leading-4 event-dot calendar--event-dot"
                    >&#183;</span
                  >
                  <span
                    class="text-left calendar--event-keyword text-A1A1AA font-normal text-xs"
                    style="word-wrap: break-word; overflow-wrap: break-word; white-space: normal;"
                  >
                    {{ getStatusText(rdv) }}
                  </span>
                </div>

                <!-- Linha 2: Nome do Paciente -->
                <div class="font-semibold text-0EA5E9 text-sm leading-4">
                  <span
                    :title="rdv?.comment ?? ''"
                    class="block text-left capitalize calendar--event-name"
                    style="word-wrap: break-word; overflow-wrap: break-word; white-space: normal;"
                  >
                    {{ rdv.name }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Popup individual para este evento -->
          <div
            class="absolute z-one w-full bg-white rounded-lg p-3 flex flex-col single-event-popup space-y-2"
            v-if="openSingleEvent && currentEventIndex === index && actionsEnabled"
            :class="{ 'right-0': popupr, 'bottom-full': popupb }"
            :ref="el => { if (el) eventPopups[index] = el as HTMLElement }"
          >
            <LinkAction
              v-if="configs?.viewEvent"
              @clicked="viewEvent(rdv.id)"
              class="calendar--event-view-action calendar--action"
              :text="configs?.viewEvent?.text || $t('calendar.view')"
            >
              <template v-if="configs?.viewEvent?.icon" #icon>
                <BlueEye />
              </template>
            </LinkAction>

            <LinkAction
              v-if="configs?.openPatientEvent"
              @clicked="openPatientFor(rdv.id)"
              :text="configs?.openPatientEvent?.text || $t('calendar.open_patient')"
              class="calendar--event-open-patient-action calendar--action"
            >
              <template v-if="configs?.openPatientEvent?.icon" #icon>
                <GreenUser />
              </template>
            </LinkAction>
          </div>
        </div>
      </div>

    </template>
  </div>
</template>

<script setup lang="ts">
import type { ComputedRef, Slots } from "vue";

export interface Props {
  eventDate: Date;
  eventTime?: string;
  slots: Slots;
}

import { E_CustomEvents, useEventsStore } from "../../stores/events";
import type { Appointment, Configs } from "../../stores/events";
import LinkAction from "./assets/link-action.vue";
import BlueEye from "./assets/blue-eye.vue";
import OrangeUpdate from "./assets/orange-update.vue";
import GreenUser from "./assets/green-user.vue";
import { onMounted, ref, watch, computed } from "vue";
import type { Ref } from "vue";
import {
  twoDigit,
  isoStringToDate,
  incrementTime,
  fixDateTime,
  hours,
  minutes,
  timeFormat,
} from "./common";

const props = withDefaults(defineProps<Props>(), {
  eventTime: "",
});

const store = useEventsStore();
const eventContainer: Ref<HTMLElement | null> = ref(null);
const eventSide: Ref<HTMLElement | null> = ref(null);
const eventList: Ref<HTMLElement | null> = ref(null);
const eventCards: Ref<HTMLElement[]> = ref([]);
const eventPopups: Ref<HTMLElement[]> = ref([]);
const openEventList: Ref<boolean> = ref(false);
const openSingleEvent: Ref<boolean> = ref(false);
const currentEventIndex: Ref<number> = ref(0);

const configs = computed<Configs>(() => store.getConfigs);
const calendarEvents = computed<Appointment[]>(() => store.getEvents);

const actionsEnabled = computed<boolean>(() => {
  const actions = ["viewEvent", "reportEvent", "openPatientEvent"];
  return actions.some(
    (it: string) => (configs?.value as Record<string, any>)[it] !== undefined
  );
});

const datetime_start: Ref<Date | null> = ref(null);
const datetime_end: Ref<Date | null> = ref(null);

// to define popup position
const popupr: Ref<boolean> = ref(false);
const popupb: Ref<boolean> = ref(false);

// Função para obter o texto do status da consulta
const getStatusText = (rdv: Appointment): string => {
  // Verificar se tem o campo confirmada (boolean) no objeto _original
  if (rdv._original && rdv._original.confirmada === true) {
    return 'Consulta confirmada';
  }

  // Verificar se tem confirmada diretamente no objeto rdv
  if ((rdv as any).confirmada === true) {
    return 'Consulta confirmada';
  }

  // Padrão
  return 'Consulta agendada';
};

//events containers
const RdvsPkg: ComputedRef<Appointment[]> = computed((): Appointment[] => {
  const start = datetime_start.value as Date;
  const end = datetime_end.value as Date;

  if (!start || !end || !props.eventTime) {
    return [];
  }

  // Criar uma chave única para este slot de tempo para otimizar a busca
  const slotKey = `${start.getFullYear()}-${start.getMonth()}-${start.getDate()}-${start.getHours()}-${start.getMinutes()}`;

  const filteredEvents = calendarEvents.value.filter((rdv: Appointment) => {
    const eventDate = isoStringToDate(rdv.date);

    // Criar chave do evento para comparação rápida
    const eventKey = `${eventDate.getFullYear()}-${eventDate.getMonth()}-${eventDate.getDate()}-${eventDate.getHours()}-${eventDate.getMinutes()}`;

    // Comparação direta das chaves - muito mais eficiente
    return eventKey === slotKey;
  });

  return filteredEvents;
});

const closeEventList = () => {
  openEventList.value = false;
  // to hide single event popup
  openSingleEvent.value = false;
};

const openEvtList = (index: number = 0) => {
  // Armazenar o índice do evento clicado
  currentEventIndex.value = index;

  // Obter o elemento do card clicado
  const clickedCard = eventCards.value[index];
  if (!clickedCard) {
    console.error('Card não encontrado para o índice:', index);
    return;
  }

  const _bpos = clickedCard.getBoundingClientRect();
  const calendarInside = document.querySelector('[data-widget-item="calendar-inside"]') as HTMLElement;

  if (!calendarInside) {
    console.error('Elemento calendar-inside não encontrado');
    return;
  }

  const _bpar = calendarInside.getBoundingClientRect();

  // Sempre abrir o popup individual do evento clicado
  // Não importa se há múltiplos eventos no mesmo horário
  openSingleEvent.value = true;

  //set automatically popup position, right or left
  popupr.value = _bpar.width < _bpos.x;
  popupb.value = _bpos.y > _bpar.height * 0.8;
};

const viewEvent = (id: string | number | unknown): void => {
  const event = new CustomEvent(E_CustomEvents.VIEW, {
    detail: { id },
  });
  document.body.dispatchEvent(event);
  closeEventList();
};

const reportEventFor = (id: string | number | unknown): void => {
  const event = new CustomEvent(E_CustomEvents.REPORT, {
    detail: { id },
  });
  document.body.dispatchEvent(event);
  closeEventList();
};

const openPatientFor = (id: string | number | unknown): void => {
  const event = new CustomEvent(E_CustomEvents.OPEN_PATIENT, {
    detail: { id },
  });
  document.body.dispatchEvent(event);
  closeEventList();
};

const setDatetime = (): void => {
  datetime_start.value = fixDateTime(props.eventDate as Date, props.eventTime);
  datetime_end.value = fixDateTime(
    props.eventDate as Date,
    incrementTime(props.eventTime)
  );
};

/**
 * watch props
 */
watch(props, () => {
  // transform props binding to datetime
  setDatetime();
});

onMounted(() => {
  document.addEventListener("click", (event) => {
    if (
      eventContainer.value &&
      !(eventContainer.value as HTMLElement).contains(
        event.target as Node | null
      )
    ) {
      closeEventList();
    }
  });
  //
  setDatetime();
});
</script>

<style lang="scss" scoped>
.event-card {
  background: rgba(14, 165, 233, 0.1);
  min-height: fit-content;
  height: auto;
  width: 100%;
  display: block;
  margin-bottom: 0.25rem;

  &:last-child {
    margin-bottom: 0;
  }
}

.event-body {
  min-height: fit-content;
  height: auto;
  width: 100%;
}

.single-event-inf {
  min-height: fit-content;
  height: auto;
  width: 100%;
}

.event-dot {
  position: relative;
  top: 2px;
  left: 4px;
}

.more-event-body,
.single-event-popup {
  margin-top: -16px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.more-event-body--item {
  border-color: rgba(0, 0, 0, 0.15);
}

.more-event-body-item-dot {
  position: relative;
  top: 2px;
}

.more-event-body--item:last-child {
  border: 0;
}

:deep(.calendar--event-report-action) {
  color: #e07a2c;
}

:deep(.calendar--event-view-action) {
  color: #0ea5e9;
}

:deep(.calendar--event-open-patient-action) {
  color: #10b981;
}

:deep(.calendar--action) {
  text-decoration: none;
}

/* Garantir que o nome do paciente quebre linha e não sobreponha */
:deep(.calendar--event-name) {
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
  max-width: 100%;
  line-height: 1.2;
}

/* Garantir que as keywords também quebrem linha se necessário */
:deep(.calendar--event-keyword) {
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
  max-width: 100%;
}
</style>
