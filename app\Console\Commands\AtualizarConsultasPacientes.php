<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Paciente;
use App\Models\Consulta;

class AtualizarConsultasPacientes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pacientes:atualizar-consultas';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Atualiza os campos ultima_consulta e proxima_consulta de todos os pacientes existentes';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Atualizando pacientes...');

        $pacientes = Paciente::select('id')->cursor(); // usa cursor pra não estourar memória

        $count = 0;
        foreach ($pacientes as $p) {
            $ultima = Consulta::where('paciente_id', $p->id)
                ->where('status', '!=', 'cancelada')
                ->where('horario', '<=', now())
                ->orderByDesc('horario')
                ->value('horario');

            $proxima = Consulta::where('paciente_id', $p->id)
                ->where('status', '!=', 'cancelada')
                ->where('horario', '>=', now())
                ->orderBy('horario')
                ->value('horario');

            $p->update([
                'ultima_consulta' => $ultima,
                'proxima_consulta' => $proxima,
            ]);

            $count++;
            if ($count % 100 === 0) {
                $this->info("{$count} pacientes atualizados...");
            }
        }

        $this->info("Concluído! Total: {$count} pacientes atualizados.");
    }
}
