<template>
  <div
    class="modal fade"
    id="importTabelaPrecosModal"
    tabindex="-1"
    aria-labelledby="importTabelaPrecosModalLabel"
    aria-hidden="true"
    data-bs-backdrop="static"
    data-bs-keyboard="false"
  >
    <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
      <div class="modal-content">
        <!-- Header -->
        <div class="modal-header bg-gradient-primary text-white">
          <h5 class="modal-title" id="importTabelaPrecosModalLabel">
            <i class="fas fa-file-import me-2"></i>
            Importar Tabela de Preços
          </h5>
          <button
            type="button"
            class="btn-close btn-close-white"
            @click="fechar"
            :disabled="importando"
          ></button>
        </div>

        <!-- Body -->
        <div class="modal-body">
          <!-- Etapa 1: Upload do arquivo -->
          <div v-if="etapa === 1" class="upload-section">
            <div class="row">
              <!-- Coluna Esquerda: Upload -->
              <div class="col-md-6">
                <div class="text-center mb-3">
                  <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-2"></i>
                  <h6>Selecione o arquivo para importação</h6>
                  <p class="text-muted small mb-3">
                    Formatos aceitos: Excel (.xlsx, .xls) ou CSV (.csv)
                  </p>
                </div>

                <!-- Área de upload -->
                <div
                  class="upload-area"
                  :class="{ 'drag-over': dragOver }"
                  @drop.prevent="handleDrop"
                  @dragover.prevent="dragOver = true"
                  @dragleave.prevent="dragOver = false"
                  @click="$refs.fileInput.click()"
                >
                  <input
                    type="file"
                    ref="fileInput"
                    @change="handleFileSelect"
                    accept=".xlsx,.xls,.csv"
                    class="d-none"
                  />
                  <div class="upload-content">
                    <i class="fas fa-file-excel fa-3x text-success mb-2"></i>
                    <p class="mb-1">
                      <strong>Clique para selecionar</strong> ou arraste o arquivo aqui
                    </p>
                    <p class="text-muted small mb-0">Tamanho máximo: 5MB</p>
                  </div>
                </div>

                <!-- Arquivo selecionado -->
                <div v-if="arquivo" class="alert alert-success mt-3 mb-0">
                  <div class="d-flex align-items-center justify-content-between">
                    <div>
                      <i class="fas fa-check-circle me-2"></i>
                      <strong>{{ arquivo.name }}</strong>
                      <span class="text-muted ms-2 small">({{ formatFileSize(arquivo.size) }})</span>
                    </div>
                    <button class="btn btn-sm btn-outline-danger" @click.stop="removerArquivo">
                      <i class="fas fa-times"></i>
                    </button>
                  </div>
                </div>
              </div>

              <!-- Coluna Direita: Colunas Esperadas -->
              <div class="col-md-6">
                <div class="colunas-esperadas-container">
                  <h6 class="mb-2">
                    <i class="fas fa-info-circle me-2 text-primary"></i>
                    Colunas Esperadas
                  </h6>
                  <p class="text-muted small mb-3">
                    Sua planilha deve conter as seguintes colunas (a ordem não importa):
                  </p>
                  <div class="row g-2">
                    <div class="col-6" v-for="campo in camposEsperados" :key="campo.key">
                      <div class="campo-info-compact">
                        <div class="d-flex align-items-start justify-content-between">
                          <strong class="small">{{ campo.label }}</strong>
                          <span v-if="campo.obrigatorio" class="badge bg-danger badge-sm">*</span>
                        </div>
                        <small class="text-muted d-block" style="font-size: 0.7rem;">{{ campo.descricao }}</small>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Botão para processar -->
            <div class="text-end mt-3" v-if="arquivo">
              <button
                class="btn btn-primary"
                @click="processarArquivo"
                :disabled="!arquivo || processando"
              >
                <span v-if="processando">
                  <span class="spinner-border spinner-border-sm me-2"></span>
                  Processando...
                </span>
                <span v-else>
                  <i class="fas fa-arrow-right me-2"></i>
                  Próximo: Mapear Colunas
                </span>
              </button>
            </div>
          </div>

          <!-- Etapa 2: Mapeamento de colunas -->
          <div v-if="etapa === 2" class="mapping-section">
            <div class="alert alert-info">
              <i class="fas fa-map-marked-alt me-2"></i>
              <strong>Mapeie as colunas da sua planilha</strong> para os campos do sistema.
              Encontramos <strong>{{ dadosPreview.length }}</strong> linhas para importar.
            </div>

            <!-- Tabela de mapeamento -->
            <div class="table-responsive">
              <table class="table table-bordered table-hover">
                <thead class="table-light">
                  <tr>
                    <th style="width: 30%">Coluna da Planilha</th>
                    <th style="width: 30%">Mapear para</th>
                    <th style="width: 40%">Preview dos Dados</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(coluna, index) in colunasArquivo" :key="index">
                    <td>
                      <strong>{{ coluna }}</strong>
                    </td>
                    <td>
                      <select
                        class="form-select form-select-sm"
                        v-model="mapeamento[coluna]"
                      >
                        <option value="">-- Ignorar esta coluna --</option>
                        <option
                          v-for="campo in camposEsperados"
                          :key="campo.key"
                          :value="campo.key"
                        >
                          {{ campo.label }}
                          {{ campo.obrigatorio ? '*' : '' }}
                        </option>
                      </select>
                    </td>
                    <td>
                      <code class="small">{{ getPreviewDados(coluna) }}</code>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- Validação do mapeamento -->
            <div v-if="errosMapeamento.length > 0" class="alert alert-danger">
              <h6 class="alert-heading">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Erros no Mapeamento
              </h6>
              <ul class="mb-0">
                <li v-for="(erro, index) in errosMapeamento" :key="index">{{ erro }}</li>
              </ul>
            </div>

            <!-- Botões de navegação -->
            <div class="d-flex justify-content-between mt-3">
              <button class="btn btn-outline-secondary" @click="voltarParaUpload">
                <i class="fas fa-arrow-left me-2"></i>
                Voltar
              </button>
              <button
                class="btn btn-primary"
                @click="avancarParaPreview"
                :disabled="errosMapeamento.length > 0"
              >
                <i class="fas fa-arrow-right me-2"></i>
                Próximo: Visualizar Dados
              </button>
            </div>
          </div>

          <!-- Etapa 3: Preview e confirmação -->
          <div v-if="etapa === 3" class="preview-section">
            <div class="alert alert-success">
              <i class="fas fa-check-circle me-2"></i>
              <strong>Pronto para importar!</strong>
              Revise os dados abaixo antes de confirmar a importação.
            </div>

            <!-- Estatísticas -->
            <div class="row g-3 mb-3">
              <div class="col-md-4">
                <div class="stat-card">
                  <i class="fas fa-list-ol text-primary"></i>
                  <div class="stat-value">{{ dadosMapeados.length }}</div>
                  <div class="stat-label">Itens a Importar</div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="stat-card">
                  <i class="fas fa-user-md text-success"></i>
                  <div class="stat-value">{{ contarPorTipo('procedimento') }}</div>
                  <div class="stat-label">Procedimentos</div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="stat-card">
                  <i class="fas fa-box text-warning"></i>
                  <div class="stat-value">{{ contarPorTipo('produto') }}</div>
                  <div class="stat-label">Produtos</div>
                </div>
              </div>
            </div>

            <!-- Preview dos dados -->
            <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
              <table class="table table-sm table-striped">
                <thead class="table-dark sticky-top">
                  <tr>
                    <th>#</th>
                    <th>Código</th>
                    <th>Nome</th>
                    <th>Tipo</th>
                    <th>Valor Base</th>
                    <th>Valor Mín.</th>
                    <th>Valor Máx.</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(item, index) in dadosMapeados.slice(0, 50)" :key="index">
                    <td>{{ index + 1 }}</td>
                    <td><code>{{ item.codigo || '-' }}</code></td>
                    <td>{{ item.nome }}</td>
                    <td>
                      <span class="badge" :class="getTipoBadgeClass(item.tipo)">
                        {{ getTipoLabel(item.tipo) }}
                      </span>
                    </td>
                    <td>{{ formatCurrency(item.valor_base) }}</td>
                    <td>{{ item.valor_minimo ? formatCurrency(item.valor_minimo) : '-' }}</td>
                    <td>{{ item.valor_maximo ? formatCurrency(item.valor_maximo) : '-' }}</td>
                  </tr>
                </tbody>
              </table>
            </div>

            <p v-if="dadosMapeados.length > 50" class="text-muted text-center mt-2">
              <small>Mostrando apenas os primeiros 50 itens. Total: {{ dadosMapeados.length }}</small>
            </p>

            <!-- Botões de ação -->
            <div class="d-flex justify-content-between mt-3">
              <button class="btn btn-outline-secondary" @click="voltarParaMapeamento" :disabled="importando">
                <i class="fas fa-arrow-left me-2"></i>
                Voltar
              </button>
              <button
                class="btn btn-success btn-lg"
                @click="confirmarImportacao"
                :disabled="importando"
              >
                <span v-if="importando">
                  <span class="spinner-border spinner-border-sm me-2"></span>
                  Importando...
                </span>
                <span v-else>
                  <i class="fas fa-check me-2"></i>
                  Confirmar Importação
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as XLSX from 'xlsx';
import { servicoProdutoService } from '@/services/servicoProdutoService';
import cSwal from '@/utils/cSwal';
import { Modal } from 'bootstrap';

export default {
  name: 'ImportTabelaPrecosModal',
  data() {
    return {
      modalInstance: null,
      etapa: 1, // 1: Upload, 2: Mapeamento, 3: Preview
      arquivo: null,
      dragOver: false,
      processando: false,
      importando: false,

      // Dados do arquivo
      colunasArquivo: [],
      dadosPreview: [],
      mapeamento: {},
      dadosMapeados: [],

      // Campos esperados
      camposEsperados: [
        { key: 'codigo', label: 'Código', obrigatorio: false, descricao: 'Código único do item' },
        { key: 'nome', label: 'Nome', obrigatorio: true, descricao: 'Nome do procedimento/produto' },
        { key: 'descricao', label: 'Descrição', obrigatorio: false, descricao: 'Descrição detalhada' },
        { key: 'tipo', label: 'Tipo', obrigatorio: true, descricao: 'procedimento ou produto' },
        { key: 'valor_base', label: 'Valor Base', obrigatorio: true, descricao: 'Valor padrão (R$)' },
        { key: 'valor_minimo', label: 'Valor Mínimo', obrigatorio: false, descricao: 'Valor mínimo (R$)' },
        { key: 'valor_maximo', label: 'Valor Máximo', obrigatorio: false, descricao: 'Valor máximo (R$)' },
        { key: 'unidade', label: 'Unidade Tempo', obrigatorio: false, descricao: 'minutos, horas ou sessões' },
        { key: 'tempo_estimado', label: 'Tempo Estimado', obrigatorio: false, descricao: 'Duração estimada' },
        { key: 'observacoes', label: 'Observações', obrigatorio: false, descricao: 'Observações adicionais' },
        { key: 'ativo', label: 'Ativo', obrigatorio: false, descricao: 'Sim/Não, 1/0, S/N' },
      ],
    };
  },
  computed: {
    errosMapeamento() {
      const erros = [];

      // Verificar campos obrigatórios
      const camposObrigatorios = this.camposEsperados.filter(c => c.obrigatorio);
      for (const campo of camposObrigatorios) {
        const mapeado = Object.values(this.mapeamento).includes(campo.key);
        if (!mapeado) {
          erros.push(`Campo obrigatório "${campo.label}" não foi mapeado`);
        }
      }

      // Verificar duplicatas
      const valores = Object.values(this.mapeamento).filter(v => v !== '');
      const duplicatas = valores.filter((v, i) => valores.indexOf(v) !== i);
      if (duplicatas.length > 0) {
        erros.push('Existem colunas mapeadas para o mesmo campo');
      }

      return erros;
    },
  },
  methods: {
    abrir() {
      this.resetar();
      if (!this.modalInstance) {
        this.modalInstance = new Modal(document.getElementById('importTabelaPrecosModal'));
      }
      this.modalInstance.show();
    },

    fechar() {
      if (this.modalInstance) {
        this.modalInstance.hide();
      }
      this.resetar();
    },

    resetar() {
      this.etapa = 1;
      this.arquivo = null;
      this.dragOver = false;
      this.processando = false;
      this.importando = false;
      this.colunasArquivo = [];
      this.dadosPreview = [];
      this.mapeamento = {};
      this.dadosMapeados = [];
    },

    handleDrop(e) {
      this.dragOver = false;
      const files = e.dataTransfer.files;
      if (files.length > 0) {
        this.handleFile(files[0]);
      }
    },

    handleFileSelect(e) {
      const files = e.target.files;
      if (files.length > 0) {
        this.handleFile(files[0]);
      }
    },

    handleFile(file) {
      // Validar tipo de arquivo
      const extensao = file.name.split('.').pop().toLowerCase();
      if (!['xlsx', 'xls', 'csv'].includes(extensao)) {
        cSwal.cError('Formato de arquivo inválido. Use Excel (.xlsx, .xls) ou CSV (.csv)');
        return;
      }

      // Validar tamanho (5MB)
      if (file.size > 5 * 1024 * 1024) {
        cSwal.cError('Arquivo muito grande. Tamanho máximo: 5MB');
        return;
      }

      this.arquivo = file;

      // Processar automaticamente
      this.processarArquivo();
    },

    removerArquivo() {
      this.arquivo = null;
      this.$refs.fileInput.value = '';
    },

    async processarArquivo() {
      if (!this.arquivo) return;

      this.processando = true;

      try {
        const reader = new FileReader();

        reader.onload = (e) => {
          try {
            const data = new Uint8Array(e.target.result);
            const workbook = XLSX.read(data, { type: 'array' });

            // Pegar a primeira planilha
            const firstSheet = workbook.Sheets[workbook.SheetNames[0]];

            // Converter para JSON
            const jsonData = XLSX.utils.sheet_to_json(firstSheet, { header: 1 });

            if (jsonData.length < 2) {
              throw new Error('Arquivo vazio ou sem dados');
            }

            // Primeira linha são os cabeçalhos
            this.colunasArquivo = jsonData[0].filter(col => col && col.toString().trim() !== '');

            // Restante são os dados
            this.dadosPreview = jsonData.slice(1).filter(row => {
              // Filtrar linhas vazias
              return row.some(cell => cell !== null && cell !== undefined && cell.toString().trim() !== '');
            });

            // Tentar mapear automaticamente
            this.tentarMapeamentoAutomatico();

            // Avançar para etapa 2
            this.etapa = 2;
            this.processando = false;
          } catch (error) {
            console.error('Erro ao processar arquivo:', error);
            cSwal.cError('Erro ao processar arquivo: ' + error.message);
            this.processando = false;
          }
        };

        reader.onerror = () => {
          cSwal.cError('Erro ao ler arquivo');
          this.processando = false;
        };

        reader.readAsArrayBuffer(this.arquivo);
      } catch (error) {
        console.error('Erro:', error);
        cSwal.cError('Erro ao processar arquivo');
        this.processando = false;
      }
    },

    tentarMapeamentoAutomatico() {
      // Tentar mapear automaticamente baseado em nomes similares
      this.mapeamento = {};

      for (const coluna of this.colunasArquivo) {
        const colunaLower = coluna.toLowerCase().trim();

        for (const campo of this.camposEsperados) {
          const campoLower = campo.label.toLowerCase();
          const keyLower = campo.key.toLowerCase();

          if (colunaLower === campoLower ||
              colunaLower === keyLower ||
              colunaLower.includes(keyLower) ||
              keyLower.includes(colunaLower)) {
            this.mapeamento[coluna] = campo.key;
            break;
          }
        }
      }
    },

    getPreviewDados(coluna) {
      const index = this.colunasArquivo.indexOf(coluna);
      const exemplos = this.dadosPreview.slice(0, 3).map(row => row[index]).filter(v => v);
      return exemplos.join(', ') || '-';
    },

    voltarParaUpload() {
      this.etapa = 1;
      this.colunasArquivo = [];
      this.dadosPreview = [];
      this.mapeamento = {};
    },

    avancarParaPreview() {
      if (this.errosMapeamento.length > 0) return;

      // Mapear os dados
      this.dadosMapeados = this.dadosPreview.map((row, rowIndex) => {
        const item = {};

        for (const [coluna, campo] of Object.entries(this.mapeamento)) {
          if (campo) {
            const index = this.colunasArquivo.indexOf(coluna);
            let valor = row[index];

            // Processar valor baseado no tipo de campo
            if (campo === 'valor_base' || campo === 'valor_minimo' || campo === 'valor_maximo') {
              // Converter para número, removendo símbolos de moeda
              valor = this.parseValorMonetario(valor);
            } else if (campo === 'tempo_estimado') {
              valor = parseInt(valor) || null;
            } else if (campo === 'tipo') {
              valor = valor?.toString().toLowerCase().trim();
              // Normalizar tipo
              if (valor === 'proc' || valor === 'procedimento') valor = 'procedimento';
              if (valor === 'prod' || valor === 'produto') valor = 'produto';
            } else if (campo === 'ativo') {
              // Converter para boolean
              valor = this.parseBoolean(valor);
            } else if (campo === 'unidade') {
              // Normalizar unidade de tempo
              valor = this.parseUnidadeTempo(valor);
            }

            item[campo] = valor;
          }
        }

        // Definir valores padrão
        if (!item.tipo) item.tipo = 'procedimento';
        if (!item.unidade) item.unidade = 'minutos';
        if (item.ativo === undefined || item.ativo === null) item.ativo = true;

        return item;
      }).filter(item => item.nome && item.valor_base); // Filtrar itens inválidos

      this.etapa = 3;
    },

    parseValorMonetario(valor) {
      if (!valor) return null;

      // Converter para string e remover símbolos
      let valorStr = valor.toString()
        .replace(/[R$\s]/g, '')
        .replace(/\./g, '')
        .replace(',', '.');

      const numero = parseFloat(valorStr);
      return isNaN(numero) ? null : numero;
    },

    parseBoolean(valor) {
      if (valor === null || valor === undefined || valor === '') return true;

      const valorStr = valor.toString().toLowerCase().trim();

      // Valores que representam "true"
      if (['1', 'sim', 's', 'yes', 'y', 'true', 'ativo', 'verdadeiro'].includes(valorStr)) {
        return true;
      }

      // Valores que representam "false"
      if (['0', 'não', 'nao', 'n', 'no', 'false', 'inativo', 'falso'].includes(valorStr)) {
        return false;
      }

      // Padrão: true
      return true;
    },

    parseUnidadeTempo(valor) {
      if (!valor) return 'minutos';

      const valorStr = valor.toString().toLowerCase().trim();

      // Normalizar unidades
      if (['min', 'minuto', 'minutos', 'minute', 'minutes'].includes(valorStr)) {
        return 'minutos';
      }
      if (['h', 'hr', 'hora', 'horas', 'hour', 'hours'].includes(valorStr)) {
        return 'horas';
      }
      if (['sessao', 'sessão', 'sessoes', 'sessões', 'session', 'sessions'].includes(valorStr)) {
        return 'sessões';
      }

      // Se não reconhecer, retornar o valor original ou padrão
      return valorStr || 'minutos';
    },

    voltarParaMapeamento() {
      this.etapa = 2;
      this.dadosMapeados = [];
    },

    async confirmarImportacao() {
      this.importando = true;

      try {
        // Importar em lote
        const response = await servicoProdutoService.importarLote(this.dadosMapeados);

        const sucesso = response.data?.data?.sucesso || 0;
        const erros = response.data?.data?.erros || 0;

        if (erros === 0) {
          cSwal.cSuccess(`${sucesso} itens importados com sucesso!`);
        } else {
          cSwal.cWarning(
            `Importação concluída com avisos:<br>` +
            `✓ ${sucesso} itens importados<br>` +
            `✗ ${erros} itens com erro`
          );
        }

        this.$emit('imported');
        this.fechar();
      } catch (error) {
        console.error('Erro ao importar:', error);
        cSwal.cError('Erro ao importar dados: ' + (error.response?.data?.message || error.message));
      } finally {
        this.importando = false;
      }
    },

    // Métodos auxiliares
    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return Math.round(bytes / Math.pow(k, i) * 100) / 100 + ' ' + sizes[i];
    },

    formatCurrency(value) {
      if (!value) return '-';
      return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL'
      }).format(value);
    },

    getTipoLabel(tipo) {
      const labels = {
        'produto': 'Produto',
        'procedimento': 'Procedimento'
      };
      return labels[tipo] || tipo;
    },

    getTipoBadgeClass(tipo) {
      const classes = {
        'produto': 'bg-warning',
        'procedimento': 'bg-primary'
      };
      return classes[tipo] || 'bg-secondary';
    },

    contarPorTipo(tipo) {
      return this.dadosMapeados.filter(item => item.tipo === tipo).length;
    },
  },
};
</script>

<style scoped>
.bg-gradient-primary {
  background: linear-gradient(87deg, #007bff 0, #0056b3 100%) !important;
}

.upload-area {
  border: 3px dashed #dee2e6;
  border-radius: 12px;
  padding: 2rem 1.5rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #f8f9fa;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-area:hover {
  border-color: #007bff;
  background-color: #e7f1ff;
}

.upload-area.drag-over {
  border-color: #28a745;
  background-color: #d4edda;
}

.upload-content {
  width: 100%;
}

.campo-info {
  padding: 0.5rem;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #007bff;
}

.campo-info-compact {
  padding: 0.4rem 0.5rem;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 2px solid #007bff;
  font-size: 0.85rem;
}

.colunas-esperadas-container {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 1rem;
  height: 100%;
}

.badge-sm {
  font-size: 0.65rem;
  padding: 0.15rem 0.3rem;
}

.stat-card {
  text-align: center;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8f9fc 0%, #ffffff 100%);
  border-radius: 12px;
  border: 1px solid #e3e6f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.stat-card i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0.5rem 0;
}

.stat-label {
  font-size: 0.875rem;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table-responsive {
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.sticky-top {
  position: sticky;
  top: 0;
  z-index: 10;
}

.modal-xl {
  max-width: 1200px;
}

.btn-close-white {
  filter: brightness(0) invert(1);
}
</style>

