<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('clinicas', function (Blueprint $table) {
            // Adicionar campo plano_id
            $table->unsignedBigInteger('plano_id')->nullable()->after('slug');
            
            // Criar foreign key
            $table->foreign('plano_id')->references('id')->on('planos')->onDelete('set null');
            
            // Adicionar índice para performance
            $table->index('plano_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('clinicas', function (Blueprint $table) {
            // Remover foreign key e índice
            $table->dropForeign(['plano_id']);
            $table->dropIndex(['plano_id']);
            
            // Remover coluna
            $table->dropColumn('plano_id');
        });
    }
};
