<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Support\Facades\Log;

return Application::configure(basePath: dirname(__DIR__))
    ->withProviders([
        App\Providers\ActionHistoryServiceProvider::class,
    ])
    ->withRouting(
        // web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        apiPrefix: '',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->api(prepend: [
            \Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful::class,
        ]);

        $middleware->alias([
            'verified' => \App\Http\Middleware\EnsureEmailIsVerified::class,
            'system_admin' => \App\Http\Middleware\CheckSystemAdmin::class,
            'action_history' => \App\Http\Middleware\ActionHistoryMiddleware::class,
        ]);

        //
    })
    ->withExceptions(function (Exceptions $exceptions) {
        // Tratar TokenExpiredException para retornar 401 ao invés de 500
        $exceptions->render(function (\Tymon\JWTAuth\Exceptions\TokenExpiredException $e, $request) {
            Log::warning('Token expirado detectado', [
                'url' => $request->fullUrl(),
                'method' => $request->method(),
            ]);
            
            return response()->json([
                'message' => 'Token has expired',
                'error' => 'token_expired'
            ], 401);
        });

        // Tratar TokenInvalidException para retornar 401
        $exceptions->render(function (\Tymon\JWTAuth\Exceptions\TokenInvalidException $e, $request) {
            Log::warning('Token inválido detectado', [
                'url' => $request->fullUrl(),
                'method' => $request->method(),
            ]);
            
            return response()->json([
                'message' => 'Token is invalid',
                'error' => 'token_invalid'
            ], 401);
        });

        // Tratar TokenBlacklistedException para retornar 401
        $exceptions->render(function (\Tymon\JWTAuth\Exceptions\TokenBlacklistedException $e, $request) {
            Log::warning('Token blacklisted detectado', [
                'url' => $request->fullUrl(),
                'method' => $request->method(),
            ]);
            
            return response()->json([
                'message' => 'Token has been blacklisted',
                'error' => 'token_blacklisted'
            ], 401);
        });
    })->create();
