<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AgendaConfig extends Model
{
    use HasFactory;

    protected $table = 'agenda_configs';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'consultorio_id',
        'horario_inicio',
        'horario_fim',
        'dias_semana',
        'configuracoes_por_dia',
        'duracao_padrao_consulta',
        'permitir_duracao_personalizada',
        'intervalo_entre_consultas',
        'horario_almoco_inicio',
        'horario_almoco_fim',
        'tem_horario_almoco',
        'permitir_agendamento_passado',
        'permitir_agendamento_feriados',
        'antecedencia_minima_agendamento',
        'antecedencia_maxima_agendamento',
        'ativo',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected function casts(): array
    {
        return [
            'dias_semana' => 'array',
            'configuracoes_por_dia' => 'array',
            'permitir_duracao_personalizada' => 'boolean',
            'tem_horario_almoco' => 'boolean',
            'permitir_agendamento_passado' => 'boolean',
            'permitir_agendamento_feriados' => 'boolean',
            'ativo' => 'boolean',
            'horario_inicio' => 'datetime:H:i',
            'horario_fim' => 'datetime:H:i',
            'horario_almoco_inicio' => 'datetime:H:i',
            'horario_almoco_fim' => 'datetime:H:i',
        ];
    }

    /**
     * Get the user that owns the agenda config.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the consultorio that owns the agenda config.
     */
    public function consultorio(): BelongsTo
    {
        return $this->belongsTo(Consultorio::class);
    }

    /**
     * Get the default configuration for a new user
     */
    public static function getDefaultConfig(): array
    {
        $defaultDayConfig = [
            'horario_inicio' => '08:00',
            'horario_fim' => '18:00',
            'tem_horario_almoco' => false,
            'horario_almoco_inicio' => '12:00',
            'horario_almoco_fim' => '13:00'
        ];

        return [
            'horario_inicio' => '08:00:00',
            'horario_fim' => '18:00:00',
            'dias_semana' => ['segunda', 'terca', 'quarta', 'quinta', 'sexta'],
            'configuracoes_por_dia' => [
                'segunda' => $defaultDayConfig,
                'terca' => $defaultDayConfig,
                'quarta' => $defaultDayConfig,
                'quinta' => $defaultDayConfig,
                'sexta' => $defaultDayConfig,
            ],
            'duracao_padrao_consulta' => 30,
            'permitir_duracao_personalizada' => true,
            'intervalo_entre_consultas' => 0,
            'horario_almoco_inicio' => null,
            'horario_almoco_fim' => null,
            'tem_horario_almoco' => false,
            'permitir_agendamento_passado' => false,
            'permitir_agendamento_feriados' => false,
            'antecedencia_minima_agendamento' => 0,
            'antecedencia_maxima_agendamento' => 720,
        ];
    }

    /**
     * Create default configuration for a user
     */
    public static function createDefaultForUser(int $userId): self
    {
        $config = self::getDefaultConfig();
        $config['user_id'] = $userId;

        return self::create($config);
    }

    /**
     * Create default configuration for a consultorio
     */
    public static function createDefaultForConsultorio(int $consultorioId): self
    {
        $config = self::getDefaultConfig();
        $config['consultorio_id'] = $consultorioId;
        $config['ativo'] = true;

        return self::create($config);
    }

    /**
     * Get configuration for a user, creating default if not exists
     */
    public static function getForUser(int $userId): self
    {
        $config = self::where('user_id', $userId)->first();

        if (!$config) {
            $config = self::createDefaultForUser($userId);
        }

        return $config;
    }

    /**
     * Get configuration for a consultorio, creating default if not exists
     */
    public static function getForConsultorio(int $consultorioId): self
    {
        $config = self::where('consultorio_id', $consultorioId)
            ->where('ativo', true)
            ->first();

        if (!$config) {
            $config = self::createDefaultForConsultorio($consultorioId);
        }

        return $config;
    }

    /**
     * Generate time slots based on configuration
     */
    public function generateTimeSlots(): array
    {
        $slots = [];

        try {
            // Tentar diferentes formatos para o horário
            $horarioInicio = $this->horario_inicio;
            $horarioFim = $this->horario_fim;

            // Se já é um objeto Carbon, converter para string
            if ($horarioInicio instanceof \Carbon\Carbon) {
                $horarioInicio = $horarioInicio->format('H:i:s');
            }
            if ($horarioFim instanceof \Carbon\Carbon) {
                $horarioFim = $horarioFim->format('H:i:s');
            }

            // Garantir formato H:i:s
            if (strlen($horarioInicio) === 5) {
                $horarioInicio .= ':00';
            }
            if (strlen($horarioFim) === 5) {
                $horarioFim .= ':00';
            }

            $inicio = \Carbon\Carbon::createFromFormat('H:i:s', $horarioInicio);
            $fim = \Carbon\Carbon::createFromFormat('H:i:s', $horarioFim);

            $current = $inicio->copy();

            while ($current->lessThan($fim)) {
                // Verificar se não está no horário de almoço
                if ($this->tem_horario_almoco && $this->isLunchTime($current)) {
                    $current->addMinutes($this->duracao_padrao_consulta + $this->intervalo_entre_consultas);
                    continue;
                }

                $slots[] = $current->format('H:i');
                $current->addMinutes($this->duracao_padrao_consulta + $this->intervalo_entre_consultas);
            }
        } catch (\Exception $e) {
            // Em caso de erro, retornar horários padrão
            \Log::error('Erro ao gerar time slots: ' . $e->getMessage());
            $slots = ['08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
                     '14:00', '14:30', '15:00', '15:30', '16:00', '16:30', '17:00', '17:30'];
        }

        return $slots;
    }

    /**
     * Check if time is within lunch break
     */
    private function isLunchTime(\Carbon\Carbon $time): bool
    {
        if (!$this->tem_horario_almoco || !$this->horario_almoco_inicio || !$this->horario_almoco_fim) {
            return false;
        }

        try {
            $horarioAlmocoInicio = $this->horario_almoco_inicio;
            $horarioAlmocoFim = $this->horario_almoco_fim;

            // Se já é um objeto Carbon, converter para string
            if ($horarioAlmocoInicio instanceof \Carbon\Carbon) {
                $horarioAlmocoInicio = $horarioAlmocoInicio->format('H:i:s');
            }
            if ($horarioAlmocoFim instanceof \Carbon\Carbon) {
                $horarioAlmocoFim = $horarioAlmocoFim->format('H:i:s');
            }

            // Garantir formato H:i:s
            if (strlen($horarioAlmocoInicio) === 5) {
                $horarioAlmocoInicio .= ':00';
            }
            if (strlen($horarioAlmocoFim) === 5) {
                $horarioAlmocoFim .= ':00';
            }

            $almocoInicio = \Carbon\Carbon::createFromFormat('H:i:s', $horarioAlmocoInicio);
            $almocoFim = \Carbon\Carbon::createFromFormat('H:i:s', $horarioAlmocoFim);

            return $time->greaterThanOrEqualTo($almocoInicio) && $time->lessThan($almocoFim);
        } catch (\Exception $e) {
            \Log::error('Erro ao verificar horário de almoço: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if a day of week is active
     */
    public function isDayActive(string $dayOfWeek): bool
    {
        return in_array($dayOfWeek, $this->dias_semana);
    }

    /**
     * Get formatted configuration for frontend
     */
    public function toFrontendFormat(): array
    {
        $data = [
            'dias_semana' => $this->dias_semana,
            'configuracoes_por_dia' => $this->configuracoes_por_dia ?: (object)[],
            'duracao_padrao_consulta' => $this->duracao_padrao_consulta,
            'permitir_duracao_personalizada' => $this->permitir_duracao_personalizada,
            'intervalo_entre_consultas' => $this->intervalo_entre_consultas,
            'permitir_agendamento_passado' => $this->permitir_agendamento_passado,
            'permitir_agendamento_feriados' => $this->permitir_agendamento_feriados,
            'antecedencia_minima_agendamento' => $this->antecedencia_minima_agendamento,
            'antecedencia_maxima_agendamento' => $this->antecedencia_maxima_agendamento,
            'time_slots' => $this->generateTimeSlots(),
        ];

        // Incluir campos antigos apenas se não houver configurações por dia (compatibilidade)
        if (empty($this->configuracoes_por_dia)) {
            $data['horario_inicio'] = $this->horario_inicio ? \Carbon\Carbon::parse($this->horario_inicio)->format('H:i') : '08:00';
            $data['horario_fim'] = $this->horario_fim ? \Carbon\Carbon::parse($this->horario_fim)->format('H:i') : '18:00';
            $data['horario_almoco_inicio'] = $this->horario_almoco_inicio ? \Carbon\Carbon::parse($this->horario_almoco_inicio)->format('H:i') : null;
            $data['horario_almoco_fim'] = $this->horario_almoco_fim ? \Carbon\Carbon::parse($this->horario_almoco_fim)->format('H:i') : null;
            $data['tem_horario_almoco'] = $this->tem_horario_almoco;
        }

        return $data;
    }
}
