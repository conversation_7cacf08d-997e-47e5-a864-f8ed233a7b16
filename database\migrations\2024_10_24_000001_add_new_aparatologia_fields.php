<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('aparatologia', function (Blueprint $table) {
            // Campo para prescrição do aparelho
            $table->text('aparelho_prescricao')->nullable()->after('aparelho_utilizado');
            
            // Novos campos para alinhadores
            $table->text('alinhador_tipo')->nullable()->after('aparelho_prescricao');
            $table->text('alinhador_observacoes')->nullable()->after('alinhador_tipo');
            
            // Novos campos para ortopedia
            $table->text('ortopedia_tipo_aparelho')->nullable()->after('alinhador_observacoes');
            $table->text('ortopedia_laboratorio')->nullable()->after('ortopedia_tipo_aparelho');
            
            // Campo boolean para miofuncional
            $table->boolean('miofuncional')->default(false)->after('ortopedia_laboratorio');
            
            // Campo para outro tipo de aparelho
            $table->text('outro_aparelho')->nullable()->after('miofuncional');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('aparatologia', function (Blueprint $table) {
            $table->dropColumn([
                'aparelho_prescricao',
                'alinhador_tipo',
                'alinhador_observacoes',
                'ortopedia_tipo_aparelho',
                'ortopedia_laboratorio',
                'miofuncional',
                'outro_aparelho'
            ]);
        });
    }
};
