import { createStore } from "vuex";
import notifications from "./modules/notifications";
import agendaConfig from "./modules/agendaConfig";

export default createStore({
  state: {
    hideConfigButton: false,
    isPinned: true,
    showConfig: false,
    sidebarType: "bg-gradient-dark",
    isRTL: false,
    color: "success",
    isNavFixed: false,
    isAbsolute: false,
    showNavs: true,
    showSidenav: true,
    showNavbar: true,
    showFooter: true,
    showMain: true,
    isDarkMode: false,
    navbarFixed:
      "position-sticky blur shadow-blur left-auto top-1 z-index-sticky px-0 mx-4",
    absolute: "position-absolute px-4 mx-0 w-100 z-index-2",
    token: {},
  },
  mutations: {
    toggleConfigurator(state) {
      state.showConfig = !state.showConfig;
    },
    navbarMinimize(state) {
      const sidenav_show = document.querySelector(".g-sidenav-show");

      if (sidenav_show.classList.contains("g-sidenav-pinned")) {
        sidenav_show.classList.remove("g-sidenav-pinned");
        state.isPinned = true;
      } else {
        sidenav_show.classList.add("g-sidenav-pinned");
        state.isPinned = false;
      }
    },
    navbarFixed(state) {
      if (state.isNavFixed === false) {
        state.isNavFixed = true;
      } else {
        state.isNavFixed = false;
      }
    },
    toggleEveryDisplay(state) {
      state.showNavbar = !state.showNavbar;
      state.showSidenav = !state.showSidenav;
      state.showFooter = !state.showFooter;
    },
    toggleHideConfig(state) {
      state.hideConfigButton = !state.hideConfigButton;
    },
    color(state, payload) {
      state.color = payload;
    },

    // New mutations for user state
    setUsername(state, username) {
      state.username = username;
    },
    setSystemAdmin(state, isAdmin) {
      state.system_admin = isAdmin;
    },
    setToken(state, token) {
      state.token = token;
    },
  },
  actions: {
    setColor({ commit }, payload) {
      commit("color", payload);
    },

    // New actions for user state
    updateUsername({ commit }, username) {
      commit("setUsername", username);
    },
    updateSystemAdmin({ commit }, isAdmin) {
      commit("setSystemAdmin", isAdmin);
    },
  },
  getters: {
    // New getters for user state
    getUsername(state) {
      return state.username;
    },
    isSystemAdmin(state) {
      return state.system_admin;
    },
  },
  modules: {
    notifications,
    agendaConfig
  }
});
