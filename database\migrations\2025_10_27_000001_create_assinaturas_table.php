<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assinaturas', function (Blueprint $table) {
            $table->id();

            // Relacionamentos
            $table->foreignId('clinica_id')->constrained('clinicas')->onDelete('cascade');
            $table->foreignId('plano_id')->constrained('planos')->onDelete('restrict');

            // Período da assinatura (histórico de períodos)
            $table->date('data_inicio');
            $table->date('data_fim')->nullable(); // null = período ativo atual

            // Status do período
            $table->enum('status', ['ativo', 'encerrado', 'cancelado'])->default('ativo');

            // Configurações financeiras (fixas para este período)
            $table->decimal('valor_mensal', 10, 2);
            $table->integer('dia_cobranca')->default(1); // Dia do mês para cobrança (1-31)

            // Fidelidade (copiada do plano no momento da criação)
            $table->integer('meses_fidelidade')->default(0);

            // Motivo da alteração/encerramento
            $table->text('motivo_alteracao')->nullable();

            // Controle de alterações
            $table->foreignId('periodo_anterior_id')->nullable()->constrained('assinaturas')->onDelete('set null');

            $table->timestamps();

            // Índices para performance
            $table->index(['clinica_id', 'status']);
            $table->index(['clinica_id', 'data_inicio', 'data_fim']);
            $table->index('status');
            $table->unique(['clinica_id', 'data_inicio']); // Evita períodos duplicados na mesma data
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assinaturas');
    }
};
