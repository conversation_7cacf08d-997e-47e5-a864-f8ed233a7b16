<template>
  <lumi-sidenav
    icon="mdi-hospital-building"
    class="fixed-end lumi-sidenav"
    v-if="showSidenav"
    :config="sidenavConfig"
    @action="handleSidenavAction"
  ></lumi-sidenav>

  <div class="main-page-content">
    <!-- Tab de Clínicas -->
    <div v-if="selectedTab === 'clinicas'" class="row">
      <div class="col-12">
        <div class="w-100 text-center mt-4">
          <input
            type="text"
            class="search-input"
            placeholder="Pesquisar clínicas..."
            @input="updateList($event.target.value)"
            v-model="search"
          />
        </div>

        <div v-if="isLoading.clinicasList" class="w-100 text-center py-5">
          <div class="spinner-border text-primary" role="status"></div>
        </div>

        <v-table v-if="!isLoading.clinicasList && clinicas.length == 0" class="m-3">
          <tbody>
            <tr>
              <td
                class="bg-gradient-light text-dark text-center"
                style="border-radius: 3px; padding: 2px 20px"
              >
                <span v-if="search == ''"
                  >Ainda não existem clínicas cadastradas.</span
                >
                <span v-if="search != ''">A busca não encontrou nenhuma clínica.</span>
              </td>
            </tr>
          </tbody>
        </v-table>

        <EasyDataTable
          v-if="clinicas.length > 0"
          :headers="tableheaders"
          :items="clinicas"
          @click-row="openClinica"
          body-row-class-name="clickable"
          header-item-class-name="table-header-item"
          body-item-class-name="table-body-item"
          rowsPerPageMessage="Clínicas por página"
          rowsOfPageSeparatorMessage="de"
          emptyMessage="Sem resultados"
        >
          <template #header-nome="header">
            <div class="w-100 ps-3">CLÍNICA</div>
          </template>

          <template #item-nome="item">
            <div class="w-100 ps-3">
              <h6 class="mb-0 text-sm text-bold">{{ item.nome }}</h6>
              <div class="text-muted" style="font-size: 0.7rem; line-height: 1.2;">
                Cadastrado em: {{ formatDate(item.created_at) }}
              </div>
            </div>
          </template>

          <template #header-pacientes_count="header">
            <div class="text-center w-100">PACIENTES</div>
          </template>

          <template #item-pacientes_count="{ pacientes_count }">
            <div class="w-100 text-center">
              <span class="badge bg-info mx-auto" style="font-size: 9pt">
                {{ pacientes_count }}
              </span>
            </div>
          </template>

          <template #header-usuarios_count="header">
            <div class="text-center w-100">USUÁRIOS</div>
          </template>

          <template #item-usuarios_count="{ usuarios_count }">
            <div class="w-100 text-center">
              <span class="badge bg-success mx-auto" style="font-size: 9pt">
                {{ usuarios_count }}
              </span>
            </div>
          </template>

          <template #header-mentorias_count="header">
            <div class="text-center w-100">MENTORIAS</div>
          </template>

          <template #item-mentorias_count="item">
            <div class="w-100 text-center">
              <span class="text-dark fw-bold">
                {{ getMentoriasStatus(item) }}
              </span>
            </div>
          </template>

          <template #header-plano_nome="header">
            <div class="text-center w-100">PLANO</div>
          </template>

          <template #item-plano_nome="item">
            <div class="w-100 text-center">
              <div
                class="badge px-2 py-1 fw-bold text-white mb-1"
                :style="{ backgroundColor: item.plano_cor || '#6c757d', minWidth: '100px', display: 'inline-block' }"
              >
                {{ item.plano_nome }}
              </div>
              <div class="text-muted small">
                {{ item.plano_valor_formatado || 'N/A' }}
              </div>
            </div>
          </template>

          <template #header-status_fidelidade="header">
            <div class="text-center w-100">FIDELIDADE</div>
          </template>

          <template #item-status_fidelidade="{ status_fidelidade }">
            <div class="w-100 text-center">
              <StatusBadge :status="status_fidelidade" type="fidelidade" />
            </div>
          </template>

          <template #header-status="header">
            <div class="text-center w-100">STATUS</div>
          </template>

          <template #item-status="{ status }">
            <div class="w-100 text-center">
              <StatusBadge :status="status" type="general" />
            </div>
          </template>
        </EasyDataTable>
      </div>
    </div>

    <!-- Tab de Gerenciar Planos -->
    <div v-if="selectedTab === 'planos'" class="p-0 container-fluid">
      <div class="row mb-4">
        <div class="col-lg-12 position-relative z-index-2">
          <GerenciadorPlanos />
        </div>
      </div>
    </div>
  </div>

  <div class="modal fade lumi-fade" tabindex="-1" id="modalNovaClinica" ref="modalNovaClinica">
    <div class="modal-dialog modal-dialog-centered" style="max-width: 750px">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Nova clínica</h5>
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="modal"
            aria-label="Close"
            ref="closeModalNovaClinica"
          ></button>
        </div>
        <div class="modal-body px-4">
          <!-- Dados da Clínica -->
          <div class="row">
            <div class="col-12">
              <h6 class="text-primary mb-3">
                <font-awesome-icon :icon="['fas', 'hospital-building']" class="me-2" />
                Dados da Clínica
              </h6>
            </div>

            <div class="col-12">
              <label>
                <span class="me-1"><font-awesome-icon :icon="['fas', 'hospital-building']" /></span>
                Nome da clínica:
              </label>

              <MaterialInput
                type="text"
                v-model="novaClinica.nome"
                ref="nome"
                :isRequired="true"
                :input="
                  function ($event) {
                    capitalizeAll($event);
                  }
                "
              />
            </div>

            <div class="col-12 mt-3">
              <label>
                <span class="me-1"><font-awesome-icon :icon="['fas', 'layer-group']" /></span>
                Plano:
              </label>

              <div v-if="isLoading.planos" class="text-center py-2">
                <div class="spinner-border spinner-border-sm text-primary" role="status">
                  <span class="visually-hidden">Carregando planos...</span>
                </div>
              </div>

              <select
                v-else
                class="form-select"
                v-model="novaClinica.plano_id"
                :class="{ 'is-invalid': !novaClinica.plano_id }"
              >
                <option value="">Selecione um plano</option>
                <option
                  v-for="plano in planos"
                  :key="plano.id"
                  :value="plano.id"
                >
                  {{ plano.nome }} - {{ plano.valor_mensal_formatado || 'Gratuito' }}
                </option>
              </select>
            </div>

            <div class="col-12 mt-3">
              <label>
                <span class="me-1"><font-awesome-icon :icon="['fas', 'bars']" /></span>
                Observações:
              </label>
              <textarea
                name=""
                id="novaClinica_observacoes"
                class="form-control"
                rows="3"
                v-model="novaClinica.observacoes"
                placeholder="Observações sobre a clínica..."
              ></textarea>
            </div>
          </div>

          <!-- Dados do Dentista Admin -->
          <div class="row mt-4">
            <div class="col-12">
              <h6 class="text-success mb-3">
                <font-awesome-icon :icon="['fas', 'user-md']" class="me-2" />
                Dentista Administrador
              </h6>
            </div>

            <div class="col-12">
              <label>
                <span class="me-1"><font-awesome-icon :icon="['fas', 'user']" /></span>
                Nome do dentista:
              </label>
              <MaterialInput
                type="text"
                v-model="novaClinica.dentista.nome"
                :isRequired="true"
                :input="
                  function ($event) {
                    capitalizeAll($event);
                  }
                "
              />
            </div>

            <div class="col-12 mt-3">
              <label>
                <span class="me-1"><font-awesome-icon :icon="['fas', 'envelope']" /></span>
                E-mail:
              </label>
              <MaterialInput
                type="email"
                v-model="novaClinica.dentista.email"
                :isRequired="true"
                placeholder="<EMAIL>"
              />
            </div>

            <div class="col-12 mt-3">
              <label>
                <span class="me-1"><font-awesome-icon :icon="['fas', 'key']" /></span>
                Senha (gerada automaticamente):
              </label>
              <div class="input-group">
                <input
                  type="text"
                  class="form-control"
                  v-model="novaClinica.dentista.senha"
                  readonly
                  style="background-color: #f8f9fa;"
                />
                <button
                  class="btn btn-outline-secondary"
                  type="button"
                  @click="generateDentistaPassword"
                  title="Gerar nova senha"
                >
                  <font-awesome-icon :icon="['fas', 'refresh']" />
                </button>
              </div>
              <small class="text-muted">
                Esta senha será usada pelo dentista para acessar o sistema.
              </small>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-primary" @click="confirmAddNovaClinica" :disabled="!isClinicaFormValid">
            Adicionar clínica
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import cSwal from "@/utils/cSwal.js";
import MaterialInput from "@/components/MaterialInput.vue";
import StatusBadge from "@/components/StatusBadge.vue";
import { mapMutations, mapState } from "vuex";
import LumiSidenav from "@/views/components/LumiSidenav/index.vue";
import GerenciadorPlanos from "@/components/GerenciadorPlanos.vue";
import { getClinicas, adicionarClinica, getClinicasWithCounts } from "@/services/clinicasService";
import { planosService } from "@/services/planosService";
import { capitalizeAll } from "@/helpers/utils.js";
import { closeModalWithAnimation } from "@/utils/modalHelper.js";

const tableheaders = [
  { text: "CLÍNICA", value: "nome", sortable: true },
  { text: "PLANO", value: "plano_nome", sortable: true },
  { text: "STATUS", value: "status", sortable: true },
  { text: "PACIENTES", value: "pacientes_count", sortable: true },
  { text: "USUÁRIOS", value: "usuarios_count", sortable: true },
  { text: "MENTORIAS", value: "mentorias_count", sortable: true },
  { text: "FIDELIDADE", value: "status_fidelidade", sortable: true },
];

var clinicas = [];

var search = "";

function getNovaClinica() {
  return {
    nome: "",
    plano_id: "",
    observacoes: "",
    dentista: {
      nome: "",
      email: "",
      senha: ""
    }
  };
}

// Função para gerar senha aleatória
function generateRandomPassword(length = 8) {
  const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  let password = "";
  for (let i = 0; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  return password;
}

export default {
  name: "Clinicas",
  components: {
    MaterialInput,
    StatusBadge,
    LumiSidenav,
    GerenciadorPlanos,
  },
  async created() {
    if (!this.$store?.state?.token?.system_admin) {
      this.$router.push({ name: "Agenda" });
    }
    this.updateList();
  },

  mounted() {
    // Configurar eventos do modal
    const modalElement = document.getElementById('modalNovaClinica');
    if (modalElement) {
      // Quando o modal for aberto, focar no campo de nome e carregar planos
      modalElement.addEventListener("shown.bs.modal", async () => {
        // Carregar planos se ainda não foram carregados
        if (this.planos.length === 0) {
          await this.loadPlanos();
        }

        // Gerar senha automática se não existe
        if (!this.novaClinica.dentista.senha) {
          this.generateDentistaPassword();
        }

        // Focar no campo de nome
        if (this.$refs.nome) {
          this.$refs.nome.getInput().focus();
        }
      });

      // Adicionar classe para animação de fechamento
      modalElement.addEventListener('hide.bs.modal', () => {
        modalElement.classList.add('modal-closing');
      });

      // Remover a classe após o modal estar completamente fechado
      modalElement.addEventListener('hidden.bs.modal', () => {
        modalElement.classList.remove('modal-closing');
      });

      // Fechar sidenav quando o modal for aberto em telas pequenas
      modalElement.addEventListener('show.bs.modal', () => {
        // Verificar se estamos em uma tela pequena (< 992px - breakpoint md do Bootstrap)
        if (window.innerWidth < 992) {
          this.closeSidenav();
        }
      });
    }
  },
  data() {
    return {
      isLoading: {
        clinicasList: true,
        planos: false,
      },
      tableheaders,
      search,
      novaClinica: getNovaClinica(),
      clinicas,
      planos: [],
      selectedTab: 'clinicas', // Tab ativa
      sidenavConfig: {
        groups: [
          {
            title: "CLÍNICAS",
            buttons: [
              {
                text: "Lista de Clínicas",
                icon: ["fas", "list"],
                iconType: "fontawesome",
                action: "changeTab",
                actionData: "clinicas",
                active: true
              },
              {
                text: "Nova clínica",
                icon: ["fas", "plus"],
                iconType: "fontawesome",
                action: "newClinic",
                attributes: {
                  "data-bs-toggle": "modal",
                  "data-bs-target": "#modalNovaClinica"
                }
              }
            ]
          },
          {
            title: "SISTEMA",
            buttons: [
              {
                text: "Gerenciar Planos",
                icon: ["fas", "layer-group"],
                iconType: "fontawesome",
                action: "changeTab",
                actionData: "planos",
                class: "text-success",
                textClass: "text-success"
              }
            ]
          }
        ]
      }
    };
  },
  watch: {
    selectedTab: {
      immediate: true,
      handler(newTab) {
        this.updateSidenavConfig(newTab);
      }
    }
  },
  methods: {
    ...mapMutations(["navbarMinimize"]),

    // Método para validar email
    isValidEmail(email) {
      if (!email || typeof email !== 'string') return false;
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(email);
    },

    // Método para formatar status das mentorias
    getMentoriasStatus(item) {
      const solicitadas = 0; // TODO: implementar contagem de mentorias solicitadas este mês
      const maximo = item.plano_mentorias_max || 0;
      const total = item.mentorias_count || 0;

      if (maximo === null) {
        return `${solicitadas}/∞ (${total})`;
      }

      return `${solicitadas}/${maximo} (${total})`;
    },

    // Método para fechar a sidenav
    closeSidenav() {
      // Verificar se a sidenav está aberta
      const sidenavElement = document.querySelector(".g-sidenav-show");
      if (sidenavElement && sidenavElement.classList.contains("g-sidenav-pinned")) {
        // Usar o método do Vuex para fechar a sidenav
        this.navbarMinimize();
      }
    },

    handleSidenavAction(action, button) {
      console.log(`Action: ${action}`, button);

      // Implementar as ações da sidenav
      switch (action) {
        case 'newClinic':
          // Modal já é aberto automaticamente pelos atributos data-bs-*
          break;
        case 'managePlans':
          this.changeTab('planos');
          break;
        case 'changeTab':
          this.changeTab(button.actionData);
          break;
      }
    },

    changeTab(tab) {
      this.selectedTab = tab;
    },

    updateSidenavConfig(activeTab) {
      // Atualizar configuração da sidenav baseada na tab ativa
      this.sidenavConfig.groups.forEach(group => {
        group.buttons.forEach(button => {
          if (button.actionData) {
            button.active = button.actionData === activeTab;
          }
        });
      });
    },
    capitalizeAll,

    // Método para formatar data
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    },

    async updateList(search = "") {
      this.isLoading.clinicasList = true;
      // Usar getClinicasWithCounts para obter clínicas com contadores
      this.clinicas = await getClinicasWithCounts();
      if (search) {
        this.clinicas = this.clinicas.filter(clinica =>
          clinica.nome.toLowerCase().includes(search.toLowerCase())
        );
      }
      this.isLoading.clinicasList = false;
    },

    async loadPlanos() {
      this.isLoading.planos = true;
      try {
        const response = await planosService.getPlanosForClinicas();
        this.planos = response.data || [];
      } catch (error) {
        console.error('Erro ao carregar planos:', error);
        cSwal.cError('Erro ao carregar planos disponíveis.');
        this.planos = [];
      } finally {
        this.isLoading.planos = false;
      }
    },

    generateDentistaPassword() {
      this.novaClinica.dentista.senha = generateRandomPassword(8);
    },

    confirmAddNovaClinica() {
      cSwal.cConfirm("Deseja realmente adicionar esta clínica?", async () => {
        cSwal.loading("Adicionando clínica...");

        try {
          const add = await adicionarClinica(this.novaClinica);
          cSwal.loaded();

          if (add) {
            cSwal.cSuccess("A clínica foi adicionada com sucesso!");
            this.novaClinica = getNovaClinica();

            // Fechar o modal com animação
            closeModalWithAnimation('modalNovaClinica');

            // Fechar a sidenav
            this.closeSidenav();
          } else {
            cSwal.cError("Ocorreu um erro ao adicionar a clínica.");
          }
        } catch (error) {
          cSwal.loaded();
          console.error('Erro ao adicionar clínica:', error);
          cSwal.cError(error.message || "Ocorreu um erro ao adicionar a clínica.");
        }

        await this.updateList(this.search);
      });
    },

    openClinica(clinica) {
      // Navegar para a página de detalhes da clínica
      this.$router.push({
        name: "Clinica",
        params: {
          id: clinica.id,
        },
      });
    },
  },
  computed: {
    ...mapState([
      "isRTL",
      "color",
      "isAbsolute",
      "isNavFixed",
      "navbarFixed",
      "absolute",
      "showSidenav",
      "showNavbar",
      "showFooter",
      "showConfig",
      "hideConfigButton",
    ]),
    // Check if all required fields are filled
    isClinicaFormValid() {
      return (
        this.novaClinica.nome &&
        this.novaClinica.nome.trim() !== '' &&
        this.novaClinica.plano_id &&
        this.novaClinica.dentista.nome &&
        this.novaClinica.dentista.nome.trim() !== '' &&
        this.novaClinica.dentista.email &&
        this.novaClinica.dentista.email.trim() !== '' &&
        this.isValidEmail(this.novaClinica.dentista.email) &&
        this.novaClinica.dentista.senha &&
        this.novaClinica.dentista.senha.length >= 8
      );
    }
  },
};
</script>
