<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('aparatologia', function (Blueprint $table) {
            // Alterar o campo miofuncional de boolean para text nullable
            $table->text('miofuncional')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('aparatologia', function (Blueprint $table) {
            // Reverter para boolean com default false
            $table->boolean('miofuncional')->default(false)->change();
        });
    }
};
