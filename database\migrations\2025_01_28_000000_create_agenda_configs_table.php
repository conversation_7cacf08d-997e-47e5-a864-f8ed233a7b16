<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('agenda_configs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            
            // Configurações de horário
            $table->time('horario_inicio')->default('08:00:00');
            $table->time('horario_fim')->default('18:00:00');
            
            // Dias da semana (JSON array com os dias ativos)
            // Exemplo: ["segunda", "terca", "quarta", "quinta", "sexta"]
            $table->json('dias_semana')->default('["segunda", "terca", "quarta", "quinta", "sexta"]');
            
            // Configurações de duração das consultas
            $table->integer('duracao_padrao_consulta')->default(30); // em minutos
            $table->boolean('permitir_duracao_personalizada')->default(true);
            
            // Configurações de intervalo entre consultas
            $table->integer('intervalo_entre_consultas')->default(0); // em minutos
            
            // Configurações de horário de almoço (opcional)
            $table->time('horario_almoco_inicio')->nullable();
            $table->time('horario_almoco_fim')->nullable();
            $table->boolean('tem_horario_almoco')->default(false);
            
            // Configurações adicionais
            $table->boolean('permitir_agendamento_passado')->default(false);
            $table->boolean('permitir_agendamento_feriados')->default(false);
            $table->integer('antecedencia_minima_agendamento')->default(0); // em horas
            $table->integer('antecedencia_maxima_agendamento')->default(720); // em horas (30 dias)
            
            $table->timestamps();
            
            // Índices
            $table->unique('user_id'); // Cada usuário tem apenas uma configuração
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('agenda_configs');
    }
};
