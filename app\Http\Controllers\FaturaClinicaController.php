<?php

namespace App\Http\Controllers;

use App\Models\FaturaClinica;
use App\Models\Clinica;
use App\Models\Assinatura;
use App\Traits\LogsActionHistory;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use Carbon\Carbon;

class FaturaClinicaController extends Controller
{
    use LogsActionHistory;

    /**
     * Display a listing of faturas for a specific clinica.
     */
    public function index(Request $request, $clinicaId): JsonResponse
    {
        $clinica = Clinica::findOrFail($clinicaId);
        
        $query = $clinica->faturasClinica()
                        ->with(['assinatura.plano'])
                        ->orderBy('data_vencimento', 'desc');

        // Filtros opcionais
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('mes') && $request->has('ano')) {
            $query->doMes($request->mes, $request->ano);
        }

        if ($request->has('data_inicio') && $request->has('data_fim')) {
            $query->whereBetween('data_vencimento', [
                $request->data_inicio,
                $request->data_fim
            ]);
        }

        $faturas = $query->paginate($request->get('per_page', 15));

        return response()->json($faturas);
    }

    /**
     * Store a newly created fatura.
     */
    public function store(Request $request, $clinicaId): JsonResponse
    {
        $clinica = Clinica::findOrFail($clinicaId);

        $validated = $request->validate([
            'assinatura_id' => 'sometimes|exists:assinaturas,id',
            'descricao' => 'required|string|max:255',
            'valor_nominal' => 'required|numeric|min:0',
            'valor_desconto' => 'sometimes|numeric|min:0',
            'valor_acrescimo' => 'sometimes|numeric|min:0',
            'data_vencimento' => 'required|date',
            'observacoes' => 'sometimes|string|max:1000',
        ]);

        // Calcular valor final
        $valorDesconto = $validated['valor_desconto'] ?? 0;
        $valorAcrescimo = $validated['valor_acrescimo'] ?? 0;
        $valorFinal = $validated['valor_nominal'] - $valorDesconto + $valorAcrescimo;

        $validated['clinica_id'] = $clinicaId;
        $validated['valor_desconto'] = $valorDesconto;
        $validated['valor_acrescimo'] = $valorAcrescimo;
        $validated['valor_final'] = $valorFinal;
        $validated['status'] = 'pendente';

        // Se não foi especificada assinatura, usar a ativa
        if (!isset($validated['assinatura_id'])) {
            $assinaturaAtiva = $clinica->assinaturaAtiva()->first();
            if ($assinaturaAtiva) {
                $validated['assinatura_id'] = $assinaturaAtiva->id;
            }
        }

        $fatura = FaturaClinica::create($validated);
        $fatura->load(['assinatura.plano', 'clinica']);

        // Log da ação
        $this->logCreateAction($fatura, $clinica, $request, 
            "Created new fatura for clinica: {$clinica->nome}");

        return response()->json($fatura, 201);
    }

    /**
     * Display the specified fatura.
     */
    public function show($clinicaId, $id): JsonResponse
    {
        $clinica = Clinica::findOrFail($clinicaId);
        
        $fatura = $clinica->faturasClinica()
                         ->with(['assinatura.plano'])
                         ->findOrFail($id);

        return response()->json($fatura);
    }

    /**
     * Update the specified fatura.
     */
    public function update(Request $request, $clinicaId, $id): JsonResponse
    {
        $clinica = Clinica::findOrFail($clinicaId);
        $fatura = $clinica->faturasClinica()->findOrFail($id);

        // Não permitir edição de faturas pagas
        if ($fatura->isPaga()) {
            return response()->json([
                'message' => 'Não é possível editar uma fatura já paga.'
            ], 422);
        }

        $validated = $request->validate([
            'descricao' => 'sometimes|string|max:255',
            'valor_nominal' => 'sometimes|numeric|min:0',
            'valor_desconto' => 'sometimes|numeric|min:0',
            'valor_acrescimo' => 'sometimes|numeric|min:0',
            'data_vencimento' => 'sometimes|date',
            'observacoes' => 'sometimes|string|max:1000',
        ]);

        // Recalcular valor final se algum valor foi alterado
        if (isset($validated['valor_nominal']) || 
            isset($validated['valor_desconto']) || 
            isset($validated['valor_acrescimo'])) {
            
            $valorNominal = $validated['valor_nominal'] ?? $fatura->valor_nominal;
            $valorDesconto = $validated['valor_desconto'] ?? $fatura->valor_desconto;
            $valorAcrescimo = $validated['valor_acrescimo'] ?? $fatura->valor_acrescimo;
            
            $validated['valor_final'] = $valorNominal - $valorDesconto + $valorAcrescimo;
        }

        $fatura->update($validated);
        $fatura->load(['assinatura.plano', 'clinica']);

        // Log da ação
        $this->logUpdateAction($fatura, $clinica, $request, 
            "Updated fatura for clinica: {$clinica->nome}");

        return response()->json($fatura);
    }

    /**
     * Remove the specified fatura.
     */
    public function destroy($clinicaId, $id): JsonResponse
    {
        $clinica = Clinica::findOrFail($clinicaId);
        $fatura = $clinica->faturasClinica()->findOrFail($id);

        // Não permitir exclusão de faturas pagas
        if ($fatura->isPaga()) {
            return response()->json([
                'message' => 'Não é possível excluir uma fatura já paga.'
            ], 422);
        }

        // Log da ação antes de deletar
        $this->logDeleteAction($fatura, $clinica, request(), 
            "Deleted fatura {$fatura->numero_fatura} for clinica: {$clinica->nome}");

        $fatura->delete();

        return response()->json([
            'message' => 'Fatura excluída com sucesso.'
        ]);
    }

    /**
     * Mark fatura as paid.
     */
    public function pagar(Request $request, $clinicaId, $id): JsonResponse
    {
        $clinica = Clinica::findOrFail($clinicaId);
        $fatura = $clinica->faturasClinica()->findOrFail($id);

        if ($fatura->isPaga()) {
            return response()->json([
                'message' => 'Fatura já está paga.'
            ], 422);
        }

        $validated = $request->validate([
            'forma_pagamento' => ['required', Rule::in([
                'dinheiro', 'cartao_credito', 'cartao_debito', 
                'pix', 'transferencia', 'boleto'
            ])],
            'data_pagamento' => 'sometimes|date',
            'observacoes' => 'sometimes|string|max:1000',
        ]);

        $dataPagamento = $validated['data_pagamento'] ?? Carbon::now()->toDateString();
        
        $fatura->marcarComoPaga($validated['forma_pagamento'], $dataPagamento);
        
        if (isset($validated['observacoes'])) {
            $fatura->observacoes = ($fatura->observacoes ? $fatura->observacoes . "\n\n" : '') . 
                                  "Pago em " . Carbon::parse($dataPagamento)->format('d/m/Y') . ": " . 
                                  $validated['observacoes'];
            $fatura->save();
        }

        $fatura->load(['assinatura.plano', 'clinica']);

        // Log da ação
        $this->logUpdateAction($fatura, $clinica, $request, 
            "Marked fatura {$fatura->numero_fatura} as paid for clinica: {$clinica->nome}");

        return response()->json($fatura);
    }

    /**
     * Cancel fatura.
     */
    public function cancelar(Request $request, $clinicaId, $id): JsonResponse
    {
        $clinica = Clinica::findOrFail($clinicaId);
        $fatura = $clinica->faturasClinica()->findOrFail($id);

        if ($fatura->isPaga()) {
            return response()->json([
                'message' => 'Não é possível cancelar uma fatura já paga.'
            ], 422);
        }

        $validated = $request->validate([
            'motivo' => 'required|string|max:1000',
        ]);

        $fatura->cancelar($validated['motivo']);
        $fatura->load(['assinatura.plano', 'clinica']);

        // Log da ação
        $this->logUpdateAction($fatura, $clinica, $request, 
            "Cancelled fatura {$fatura->numero_fatura} for clinica: {$clinica->nome}");

        return response()->json($fatura);
    }

    /**
     * Get faturas statistics for a clinica.
     */
    public function estatisticas($clinicaId): JsonResponse
    {
        $clinica = Clinica::findOrFail($clinicaId);
        
        $estatisticas = [
            'total_faturas' => $clinica->faturasClinica()->count(),
            'faturas_pagas' => $clinica->faturasClinica()->pagas()->count(),
            'faturas_pendentes' => $clinica->faturasClinica()->pendentes()->count(),
            'faturas_vencidas' => $clinica->faturasClinica()->vencidas()->count(),
            'valor_total_pago' => $clinica->faturasClinica()->pagas()->sum('valor_final'),
            'valor_total_pendente' => $clinica->faturasClinica()->pendentes()->sum('valor_final'),
            'valor_total_vencido' => $clinica->faturasClinica()->vencidas()->sum('valor_final'),
        ];

        return response()->json($estatisticas);
    }
}
