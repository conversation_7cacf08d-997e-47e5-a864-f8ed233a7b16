v2:
    ☐ Conferir sistema de orçamentos (clinicorp)
    ☐ Aba "Planejamento" e "Tratamento": quando não tiver o módulo para ortodontistas, vamos retirar a "Planejamento" e alterar "Tratamento" para "Histórico" ou "Consultas"
    ☐ Afinar mais ainda a navbar dos steps (talvez diminuir mais o ícone/padding-y?)
    Financeiro a receber:
        ☐ Implementar odontograma
    ☐ CORRIGIR: verificar a expiração do token no front-end, em vez de aguardar a resposta do servidor
    Melhorias de layout:
        Agenda (tela inicial! - tem que ser robusta):
            ☐ Fazer um calendário robusto no canto superior direito (talvez aumentar a largura da sidenav para esta tela)
        ☐ Adicionar texto "Iniciar" ao botão Play das análises
        ☐ Deixar mais elegante o estilo da seta de voltar do paciente para /pacientes  
        ☐ Melhorar layout dos campos de início do tratamento e meses previstos
        Visualização das imagens:
            ☐ Fazer espécie de "lupa" - e talvez aumentar o tamanho da imagem base na tela
            ☐ Tirar "X" de excluir as imagens do efeito hover -  fazer botão pequeno só quando ela tiver aberta em fullscreen
        Badge Paciente/Tratamento/"próxima consulta":
            ☐ Fazer ficar verdinha e com ícone de calendar-check quando já tiver marcada
            ☐ Fazer ficar default e com ícone calendar-day (sem check) quando não tiver marcada
    Logs do Laravel:
        ☐ Traduzir
        ☐ Nos updates, salvar apenas os campos que foram alterados (isso gera a possibilidade de implementarmos um sistema de rollback das alterações - que seja "travado" de forma que seja possível restaurar apenas o último passo - verificar se é melhor apenas excluir a alteração anterior, ou registrar uma nova versão - seguir lógica do git??)

    ☐ Fazer sistema robusto de busca para pacientes e para ortodontistas
    Fazer sistema de cobrança da mensalidade dos ortodontistas:
        ☐ Sistema interno do app para cobrança da própria mensalidade dos usuários (verificar se por segurança é obrigatório fazer isso em outro painel - um app dedicado a admins)
    ☐ Fazer função de reagendar consultas
    ☐ Sistema de tickets
    ☐ Passar as metas terapêuticas para a parte do tratamento
    ☐ Tratamento (planejamento): remover os 4 quadrantes de metas terapêuticas e deixar só a opção para adicionar (e também "depende de outra área")
    Dentista > Pacientes:
        ☐ Exibir listagem de pacientes (com busca simples):
            ☐ Ao clicar, envia para a tela do Paciente
    Dentista > Consultas:
        ☐ Exibir listagem de consultas (com filtro simples)
    ☐ V3: Fazer dashboards analíticos de BI
-- ---------------------------------------------------------
-- ---------------------------------------------------------
☐ Criar testes automatizados para a API
☐ Criar versão dev do site (com tela chique de autenticação - "administração")
☐ Fazer carousel ficar "sticky" (preso ao topo quando rolar a tela)
☐ Consultas: criar uma aba para o histórico de consultas - nela aparecem os estados das metas terapêuticas (e permite adicionar?)
☐ Remover observações do tratamento recomendado
Header do tratamento:
    ☐ Manter o header fixo ao rolar a lista de consultas
Funcionalidades:
    ☐ Fazer filtro na agenda para alternar entre "Exibir canceladas"
    ☐ Dentista > Pacientes: marcar os que estiverem com mentoria solicitada
    ☐ Criar lista de funcionalidades do sistema
-- ----------------------------------------------------------------------------------------------------
Later?:
    ☐ Refinar layout para telas entre md e lg? ou telas lg? (na faixa de 768 a 1024p de largura)
    ☐ Adicionar opção para gerar carta de encaminhamento (PDF)
    ☐ Implementar solicitações de reagendamentos (para os funcionários receberem e reagendarem)
    ☐ Fazer último paciente acessado ficar no state global do app, para retornar à ele quando alternar entre as abas
    ☐ Opção para excluir/arquivar paciente
    ☐ Opção para excluir/arquivar ortodontista
-- ----------------------------------------------------------------------------------------------------
-- ----------------------------------------------------------------------------------------------------
-- ----------------------------------------------------------------------------------------------------
-- ----------------------------------------------------------------------------------------------------
-- ----------------------------------------------------------------------------------------------------

☐ V2/V3?: Fazer dashboards analíticos de marketing
Formulário:
    ☐ Adicionar informação (única/múltipla escolha)
    ou
    ☐ Permitir? Adicionar informação (única/múltipla escolha)
    ☐ Permitir alterar respostas do questionário após respondido
Status do progresso:
    ☐ Colocar status do progresso:
        ☐ Vermelho - após atingir a data
Agenda de consultas:
    ☐ Fazer funcionar impressão
    Fazer funcionar botão de atestado:
        ☐ Criar modal de Novo Atestado
        ☐ No modal, exibir as últimas X consultas (em formato de cartão or something like that), para facilitar selecionar o paciente, ou então escolhe pesquisando mesmo...
        ☐ Gerar atestado em PDF
Paciente > Perfil:
    ☐ Adicionar campo "o paciente é o responsável"
Tratamento recomendado:
    □ Adicionar botões para preencher automaticamente a aparatologia de acordo com o tratamento recomendado
Dentistas:
    ☐ Criar modal:
        ☐ Criar listagem de clínicas
        ☐ Criar opção para adicionar
        ☐ Criar opção para excluir
        ☐ Quando clicar na clínica, exibir info dela:
            ☐ Nome
            ☐ Data de cadastro
            ☐ Total de dentistas
            ☐ Total de pacientes
            ☐ Listagem de dentistas
Fazer modais globais:
    ☐ Fazer modal global de buscar clínica
    ☐ Fazer modal global de buscar paciente
    ☐ Fazer modal global de buscar ortodontista

Paciente > Perfil:
    ☐ Permitir editar meios de contato
Layout:
    Melhorias na responsividade:
        Paciente > Perfil:
            ☐ Pra telas pequenas (s), fazer tabs das seções (informações pessoais, endereço, ficha)
        ☐ Passar botões das abas para baixo do nome do paciente, quando a tela for pequena
        ☐ Verificar outras melhorias
    ☐ Implementar botões de CRUD em todas as entidades (revisar)
    ☐ Corrigir: padding está comendo em baixo, algum bug com a altura geral da página
    ☐ Corrigir: botão de fechar modal está oculto/transparente
    ☐ Corrigir: A sidenav não está chegando até o final da tela
    ☐ Corrigir: ajustar largura da tela do Tratamento
    ☐ Melhorar layout mobile
☐ Ao pesquisar o paciente, parece que precisará dar um delay entre o pressionar das teclas para esperar concluir a palavra
Tela de Plano de tratamento:
    ☐ Gerar as metas terapêuticas do diagnóstico automático realizado (mas ainda possibilitando adicionar novas, manualmente)
    Seção "Necessidade de encaminhamentos":
        ☐ Gerar automaticamente a partir da análise
        ☐ Exibir de qual item foi gerada a necessidade de encaminhamento
