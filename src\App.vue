<!--
=========================================================
* Vue Material Dashboard 2 - v3.1.0
=========================================================

* Product Page: https://creative-tim.com/product/vue-material-dashboard-2
* Copyright 2023 Creative Tim (https://www.creative-tim.com)

Coded by www.creative-tim.com

 =========================================================

* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
-->
<style>
body {
  background-color: #00F !important;
}

/* Estilos para o loading de verificação de autenticação */
.auth-loading-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1B4464 0%, #56809F 100%);
}

.auth-loading-content {
  text-align: center;
  color: white;
}

.auth-loading-logo {
  margin-bottom: 2rem;
  animation: elegantPulse 3s ease-in-out infinite;
}

.auth-loading-logo img {
  width: 72px;
  height: 72px;
  border-radius: 16px;
  box-shadow: 0 12px 40px rgba(27, 68, 100, 0.4);
  filter: brightness(1.1);
}

.auth-loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
}

.auth-loading-spinner .spinner-border {
  width: 2rem;
  height: 2rem;
  border-width: 0.25em;
  color: white;
  opacity: 0.9;
}

@keyframes elegantPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.03);
    opacity: 0.9;
  }
}
</style>
<template>
  <!-- Loading inicial durante verificação de autenticação -->
  <div v-if="isCheckingAuth" class="auth-loading-container">
    <div class="auth-loading-content">
      <div class="auth-loading-logo">
        <img src="/favicon.png" alt="LUMI Vision" />
      </div>
      <div class="auth-loading-spinner">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Verificando autenticação...</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Página de login -->
  <entrar v-else-if="($route.meta.requiresAuth && !isAuthenticated) || $route.name === 'Entrar'"></entrar>

  <!-- Conteúdo principal da aplicação -->
  <div class="user-access" v-else>
    <tab-navigation class="bg-gradient-primary" v-if="isAuthenticated && $route.name !== 'WelcomeForm' && $route.name !== 'Inicio'" />
    <main class="main-content position-relative max-height-vh-100 h-100 overflow-x-hidden">
      <router-view />
      <app-footer v-show="showFooter && $route.name !== 'Inicio'" />
      <fab-search
        v-if="!isPacienteRoute && $route.name !== 'Inicio'"
        :toggle="toggleConfigurator"
        :class="[showConfig ? 'show' : '', hideConfigButton ? 'd-none' : '']"
      />

      <!-- Sistema global de drafts -->
      <global-drafts-manager v-if="isAuthenticated" />
    </main>
  </div>
</template>
<script>
import FabSearch from "@/views/components/FabSearch.vue";
import Navbar from "@/examples/Navbars/Navbar.vue";
import AppFooter from "@/examples/Footer.vue";
import { mapMutations, mapState } from "vuex";
import TabNavigation from "./views/components/TabNavigation.vue"
import Entrar from "./views/Entrar.vue"
import usuariosService from '@/services/usuariosService'
import GlobalDraftsManager from "@/components/GlobalDraftsManager.vue";

export default {
  name: "App",
  components: {
    FabSearch,
    Navbar,
    AppFooter,
    TabNavigation,
    Entrar,
    GlobalDraftsManager
  },
  data() {
    return {
      resizeTimeout: null,
      isCheckingAuth: true
    };
  },
  methods: {
    ...mapMutations(["toggleConfigurator", "navbarMinimize"]),

    // Método para verificar o redimensionamento da tela e ajustar a sidenav
    handleResize() {
      // Implementar debounce para melhorar performance
      clearTimeout(this.resizeTimeout);
      this.resizeTimeout = setTimeout(() => {
        const sidenav = document.getElementsByClassName("g-sidenav-show")[0];
        if (!sidenav) return;

        // Breakpoint para telas pequenas (sm) - 768px é o breakpoint md
        if (window.innerWidth < 768) {
          // Se estiver em tela pequena, certifique-se de que a sidenav esteja fechada
          if (sidenav.classList.contains("g-sidenav-pinned")) {
            sidenav.classList.remove("g-sidenav-pinned");
            this.$store.state.isPinned = true;
          }
        } else if (window.innerWidth > 1200) {
          // Em telas grandes, mantenha o comportamento atual (sidenav aberta)
          if (!sidenav.classList.contains("g-sidenav-pinned")) {
            sidenav.classList.add("g-sidenav-pinned");
            this.$store.state.isPinned = false;
          }
        }
      }, 150); // 150ms de debounce
    }
  },
  computed: {
    isAuthenticated: usuariosService.isAuthenticated,
    isPacienteRoute() {
      // Verifica se estamos em qualquer rota relacionada ao Paciente
      const routeName = this.$route.name;
      const routePath = this.$route.path;
      return routeName === 'PacientePadrao' || routeName === 'PacienteClinica' || routePath.includes('/paciente/');
    },
    ...mapState([
      "isRTL",
      "color",
      "isAbsolute",
      "isNavFixed",
      "navbarFixed",
      "absolute",
      "showSidenav",
      "showNavbar",
      "showFooter",
      "showConfig",
      "hideConfigButton",
    ]),
  },
  async beforeMount() {
    // Aguardar verificação de autenticação antes de renderizar o conteúdo
    try {
      await usuariosService.refreshAuth();
    } catch (error) {
      console.error('Erro na verificação inicial de autenticação:', error);
    } finally {
      // Sempre definir isCheckingAuth como false, mesmo em caso de erro
      this.isCheckingAuth = false;
    }

    this.$store.state.isTransparent = "bg-transparent";
    const body = document.getElementsByTagName("body")[0];

    const sidenav = document.getElementsByClassName("g-sidenav-show")[0];

    if (window.innerWidth > 1200) {
      sidenav.classList.add("g-sidenav-pinned");
    } else if (window.innerWidth < 768) {
      // Certifique-se de que a sidenav esteja fechada em telas pequenas no carregamento inicial
      if (sidenav && sidenav.classList.contains("g-sidenav-pinned")) {
        sidenav.classList.remove("g-sidenav-pinned");
        this.$store.state.isPinned = true;
      }
    }

    body.classList.remove("bg-gray-200");

    // Adicionar event listener para redimensionamento da tela
    window.addEventListener('resize', this.handleResize);
  },

  unmounted() {
    // Remover event listener quando o componente for destruído
    window.removeEventListener('resize', this.handleResize);
  },
};
</script>
