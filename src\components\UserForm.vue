<template>
  <teleport to="body">
    <div class="modal fade" id="userFormModal" tabindex="-1">
      <div class="modal-dialog modal-dialog-centered" style="max-width: 500px;">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              {{ isEditing ? 'Editar Usuário' : 'Novo Usuário' }}
            </h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              @click="resetForm"
            ></button>
          </div>

          <div class="modal-body">
            <form @submit.prevent="submitForm">
              <!-- Nome -->
              <div class="mb-3">
                <label for="userName" class="form-label">Nome Completo *</label>
                <input
                  id="userName"
                  ref="nameInput"
                  v-model="form.name"
                  type="text"
                  class="form-control form-control-sm"
                  :class="{ 'is-invalid': errors.name }"
                  placeholder="Digite o nome completo"
                  required
                />
                <div v-if="errors.name" class="invalid-feedback d-block">
                  {{ errors.name }}
                </div>
              </div>

              <!-- Email e Senha lado a lado -->
              <div class="row">
                <div class="col-7 mb-3">
                  <label for="userEmail" class="form-label">Email *</label>
                  <input
                    id="userEmail"
                    v-model="form.email"
                    type="email"
                    class="form-control form-control-sm"
                    :class="{ 'is-invalid': errors.email }"
                    placeholder="Digite o email"
                    required
                  />
                  <div v-if="errors.email" class="invalid-feedback d-block">
                    {{ errors.email }}
                  </div>
                </div>
                <div class="col-5 mb-3">
                  <label for="userPassword" class="form-label">
                    {{ isEditing ? 'Nova senha (opcional)' : 'Senha *' }}
                  </label>
                  <div class="input-group input-group-sm">
                    <input
                      id="userPassword"
                      v-model="form.password"
                      type="text"
                      class="form-control"
                      :class="{ 'is-invalid': errors.password }"
                      :required="!isEditing"
                      placeholder="Senha"
                    />
                    <button
                      v-if="!isEditing"
                      type="button"
                      class="btn btn-outline-secondary"
                      @click="generatePassword"
                      title="Gerar senha aleatória"
                    >
                      <i class="fas fa-random"></i>
                    </button>
                  </div>
                  <div v-if="errors.password" class="invalid-feedback d-block">
                    {{ errors.password }}
                  </div>
                </div>
              </div>

              <!-- Tipo de Usuário (Colaborador/Profissional) - Apenas para novo usuário -->
              <div v-if="!isEditing" class="mb-3">
                <label class="form-label">Tipo de Usuário *</label>
                <div class="d-flex gap-2">
                  <button
                    type="button"
                    class="btn flex-grow-1"
                    :class="form.userType === 'colaborador' ? 'btn-info' : 'btn-outline-info'"
                    @click="form.userType = 'colaborador'"
                  >
                    <i class="fas fa-user me-2"></i>Colaborador
                  </button>
                  <button
                    type="button"
                    class="btn flex-grow-1 btn-teal"
                    :class="form.userType === 'profissional' ? 'active' : ''"
                    @click="form.userType = 'profissional'"
                  >
                    <i class="fas fa-stethoscope me-2"></i>Profissional da Saúde
                  </button>
                </div>
              </div>

              <!-- Permissão de Admin - Toggle estilo PlanoModal -->
              <div class="mb-3 admin-section">
                <label class="form-label text-center d-block">Administrador da Clínica</label>
                <div class="admin-toggle-container">
                  <div class="form-check form-switch d-flex align-items-center justify-content-between">
                    <label class="form-check-label flex-grow-1" for="userClinicaAdmin">
                      <i v-if="form.clinica_admin" class="fas fa-shield-alt text-success me-2"></i>
                      <i v-else class="fas fa-user text-muted me-2"></i>
                      <strong>{{ form.clinica_admin ? 'Administrador' : 'Usuário Comum' }}</strong>
                      <small class="text-muted d-block">{{ form.clinica_admin ? 'Acesso total' : 'Acesso limitado' }}</small>
                    </label>
                    <input
                      id="userClinicaAdmin"
                      v-model="form.clinica_admin"
                      type="checkbox"
                      class="form-check-input custom-admin-switch ms-3"
                      :disabled="isEditingOwnUser"
                      role="switch"
                    />
                  </div>
                  <small v-if="isEditingOwnUser" class="d-block text-muted mt-2">
                    Você não pode alterar sua própria permissão de admin
                  </small>
                </div>
              </div>

              <!-- Erros gerais -->
              <div v-if="errors.general" class="alert alert-danger mb-3">
                {{ errors.general }}
              </div>
            </form>
          </div>

          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              data-bs-dismiss="modal"
              @click="resetForm"
            >
              Cancelar
            </button>
            <button
              type="button"
              class="btn btn-primary"
              :disabled="isSaving"
              @click="submitForm"
            >
              <span v-if="isSaving" class="spinner-border spinner-border-sm me-2"></span>
              {{ isEditing ? 'Atualizar' : 'Criar' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </teleport>
</template>

<script>
import usuariosService from '@/services/usuariosService';
import cSwal from '@/utils/cSwal';
import { closeModal } from '@/utils/modalHelper';

export default {
  name: 'UserForm',
  props: {
    clinicaId: {
      type: Number,
      required: true
    },
    usuario: {
      type: Object,
      default: null
    }
  },
  emits: ['usuario-saved'],
  data() {
    return {
      form: {
        name: '',
        email: '',
        password: '',
        passwordConfirm: '',
        clinica_admin: false,
        userType: 'colaborador' // 'colaborador' ou 'profissional'
      },
      errors: {},
      isSaving: false,
      isEditing: false,
      currentUserId: null
    };
  },
  computed: {
    isEditingOwnUser() {
      return this.isEditing && this.usuario?.id === this.currentUserId;
    }
  },
  watch: {
    usuario(newVal) {
      if (newVal) {
        this.isEditing = true;
        this.form = {
          name: newVal.name,
          email: newVal.email || '',
          password: '',
          passwordConfirm: '',
          clinica_admin: Boolean(newVal.clinica_admin),
          userType: newVal.user_type || (newVal.dentista || newVal.dentista_id ? 'profissional' : 'colaborador')
        };
      } else {
        this.isEditing = false;
        this.resetForm();
        // Gerar senha automaticamente para novo usuário
        this.generatePassword();
        // Focar no input de nome após o modal abrir (apenas em desktop)
        this.$nextTick(() => {
          if (window.innerWidth >= 768) {
            setTimeout(() => {
              this.$refs.nameInput?.focus();
            }, 300);
          }
        });
      }
    }
  },
  mounted() {
    const decoded = usuariosService.decodedToken();
    this.currentUserId = decoded?.sub;
    // Gerar senha inicial ao montar o componente
    this.generatePassword();
  },
  methods: {
    generatePassword() {
      // Caracteres permitidos: letras maiúsculas e números, excluindo I, O, 0, 1 para evitar confusão
      const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
      let password = '';
      for (let i = 0; i < 6; i++) {
        password += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      this.form.password = password;
    },
    async submitForm() {
      this.errors = {};

      // Validações
      if (!this.form.name.trim()) {
        this.errors.name = 'Nome é obrigatório';
      }
      if (!this.form.email.trim()) {
        this.errors.email = 'Email é obrigatório';
      }
      if (!this.isEditing && !this.form.password) {
        this.errors.password = 'Senha é obrigatória';
      }

      if (Object.keys(this.errors).length > 0) return;

      this.isSaving = true;
      try {
        const data = { ...this.form };

        // Remover userType do payload (será usado apenas no frontend)
        const userType = data.userType;
        delete data.userType;

        // Username será preenchido com o email no backend
        data.username = data.email;

        // Converter clinica_admin para 0 ou 1 (o backend espera inteiro)
        data.clinica_admin = data.clinica_admin ? 1 : 0;

        // Se editando e senha vazia, remover do payload
        if (this.isEditing && !data.password) {
          delete data.password;
        }

        if (this.isEditing) {
          await usuariosService.updateClinicaUsuario(
            this.clinicaId,
            this.usuario.id,
            data
          );
          cSwal.cSuccess('Usuário atualizado com sucesso');
        } else {
          // Adicionar userType ao payload para novo usuário
          data.user_type = userType;
          await usuariosService.createClinicaUsuario(this.clinicaId, data);
          cSwal.cSuccess('Usuário criado com sucesso');
        }

        this.$emit('usuario-saved');
        this.resetForm();

        // Fechar modal
        closeModal('userFormModal');
      } catch (error) {
        console.error('Erro ao salvar usuário:', error);
        this.errors.general = error.response?.data?.message || 'Erro ao salvar usuário';
      } finally {
        this.isSaving = false;
      }
    },
    resetForm() {
      this.form = {
        name: '',
        email: '',
        password: '',
        passwordConfirm: '',
        clinica_admin: false,
        userType: 'colaborador'
      };
      this.errors = {};
      this.isEditing = false;
    }
  }
};
</script>

<style scoped>
.modal-body {
  max-height: 70vh;
  overflow-y: auto;
  padding: 1.5rem;
}

.form-check-input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Toggle customizado para Administrador */
.custom-admin-switch {
  background-color: #dee2e6 !important;
  border-color: #dee2e6 !important;
  width: 2.5em;
  height: 1.25em;
  cursor: pointer;
}

.custom-admin-switch:checked {
  background-color: #28a745 !important;
  border-color: #28a745 !important;
}

.custom-admin-switch:focus {
  box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.25) !important;
}

.admin-toggle-container {
  text-align: center;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.admin-toggle-container:hover {
  background: rgba(0, 123, 255, 0.05);
}

.admin-toggle-container .form-check-label {
  cursor: pointer;
  margin-bottom: 0;
}

.admin-toggle-container .form-check-label small {
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.admin-section {
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #e9ecef;
}

.form-control-sm {
  font-size: 0.875rem;
  padding: 0.375rem 0.75rem;
  height: auto;
}

.form-label {
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

/* Botão Teal customizado */
.btn-teal {
  background-color: white;
  border: 1px solid #20c997;
  color: #20c997;
  transition: all 0.15s ease-in-out;
}

.btn-teal:hover {
  background-color: #1ab386;
  border-color: #1ab386;
  color: white;
}

.btn-teal.active {
  background-color: #20c997;
  border-color: #20c997;
  color: white;
}
</style>

