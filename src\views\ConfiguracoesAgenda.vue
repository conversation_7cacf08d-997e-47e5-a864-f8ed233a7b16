<template>
  <div class="main-page-content">
    <div class="px-3 pt-0 pb-3 container-fluid">
      <div class="row justify-content-center">
        <div class="col-lg-10 position-relative py-2">

          <!-- Se<PERSON><PERSON> de Consultórios -->
          <ConsultorioSelector
            :consultorios="consultorios"
            :selected-consultorio-id="selectedConsultorioId"
            :is-loading="isLoadingConsultorios"
            @consultorio-selected="onConsultorioSelected"
            @add-consultorio="openConsultorioModal()"
            @edit-consultorio="openConsultorioModal"
          />



          <!-- Loading State -->
          <div v-if="isLoading" class="text-center py-3">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Carregando...</span>
            </div>
            <small class="text-muted mt-1 d-block">Carregando configurações...</small>
          </div>

          <!-- No Consultorio State -->
          <div v-else-if="!selectedConsultorioId && consultorios.length === 0" class="text-center py-5">
            <div class="mb-3">
              <i class="fas fa-clinic-medical text-muted" style="font-size: 3rem;"></i>
            </div>
            <h5 class="text-muted mb-3">Nenhum consultório encontrado</h5>
            <p class="text-muted mb-4">
              Para configurar a agenda, você precisa primeiro criar um consultório.
            </p>
            <button
              type="button"
              class="btn btn-primary"
              @click="openConsultorioModal()"
            >
              <i class="fas fa-plus me-2"></i>
              Criar Primeiro Consultório
            </button>
          </div>

          <!-- No Selected Consultorio State -->
          <div v-else-if="!selectedConsultorioId && consultorios.length > 0" class="text-center py-5">
            <div class="mb-3">
              <i class="fas fa-hand-pointer text-muted" style="font-size: 3rem;"></i>
            </div>
            <h5 class="text-muted mb-3">Selecione um consultório</h5>
            <p class="text-muted mb-4">
              Escolha um consultório acima para configurar sua agenda.
            </p>
          </div>

          <!-- Main Form -->
          <form v-else @submit.prevent="salvarConfiguracoes" class="agenda-config-form">

            <!-- Configurações da Agenda - Card Único -->
            <div class="config-card mb-4">
              <div class="config-card-body">

                <!-- Configurações do Consultório -->
                <div class="config-section mb-4">
                  <div class="config-header-mobile mb-3 mb-md-none">
                    <h6 class="section-label mb-3 mb-md-0">
                      <i class="fas fa-cog text-primary flex-shrink-0"></i>
                      <span>Configurações do consultório&nbsp;<span :style="{ color: selectedConsultorio?.cor || '#007bff' }">{{ selectedConsultorio?.nome || 'Consultório' }}</span></span>
                    </h6>
                    <button
                      type="submit"
                      class="btn btn-primary btn-sm config-save-btn d-none d-md-block"
                      :disabled="isSaving"
                    >
                      <span v-if="isSaving" class="spinner-border spinner-border-sm me-2" role="status"></span>
                      <i v-else class="fas fa-save me-2"></i>
                      {{ isSaving ? 'Salvando...' : 'Salvar Configurações' }}
                    </button>
                  </div>
                  <div class="d-flex flex-wrap gap-4 align-items-end justify-content-center">
                    <div class="flex-shrink-0">
                      <label class="form-label fw-bold mb-1">Duração da consulta:</label>
                      <select
                        v-model="formData.duracao_padrao_consulta"
                        class="form-select form-select-sm"
                        :class="{ 'is-invalid': errors.duracao_padrao_consulta }"
                        style="width: auto; min-width: 160px; text-align: center;"
                      >
                        <option value="15">15 min</option>
                        <option value="20">20 min</option>
                        <option value="30">30 min</option>
                        <option value="45">45 min</option>
                        <option value="60">1 hora</option>
                        <option value="90">1h 30min</option>
                        <option value="120">2 horas</option>
                      </select>
                      <div v-if="errors.duracao_padrao_consulta" class="invalid-feedback">{{ errors.duracao_padrao_consulta }}</div>
                    </div>
                    <div class="flex-shrink-0">
                      <div class="custom-switch-wrapper">
                        <input
                          class="custom-switch-input"
                          type="checkbox"
                          id="permitir_duracao_personalizada"
                          v-model="formData.permitir_duracao_personalizada"
                        >
                        <label class="custom-switch-label" for="permitir_duracao_personalizada">
                          <span class="custom-switch-slider"></span>
                          <span class="custom-switch-text">Permitir duração personalizada</span>
                        </label>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Dias de funcionamento -->
                <div class="config-section mb-4">
                  <h6 class="section-label mb-3">
                    <i class="fas fa-calendar-week me-2 text-primary"></i>
                    Dias de funcionamento
                  </h6>
                  <div class="days-selector">
                    <div
                      v-for="dia in diasSemana"
                      :key="dia.value"
                      class="day-container"
                    >
                      <div
                        class="day-option"
                        :class="{
                          active: formData.dias_semana.includes(dia.value),
                          selected: selectedDay === dia.value
                        }"
                        @click="selectDay(dia.value)"
                      >
                        <div class="day-icon">{{ dia.icon }}</div>
                        <div class="day-name">{{ dia.label }}</div>
                        <!-- Indicador de seleção - setinha embaixo -->
                        <div v-if="selectedDay === dia.value" class="selected-arrow">
                          <i class="fas fa-caret-down"></i>
                        </div>
                      </div>
                      <!-- Toggle abaixo do dia -->
                      <div class="day-toggle-wrapper">
                        <div class="custom-switch-wrapper-small">
                          <input
                            class="custom-switch-input"
                            type="checkbox"
                            :id="`day-toggle-${dia.value}`"
                            v-model="formData.dias_semana"
                            :value="dia.value"
                            @click.stop
                          >
                          <label class="custom-switch-label" :for="`day-toggle-${dia.value}`" @click.stop>
                            <span class="custom-switch-slider-small"></span>
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="errors.dias_semana" class="text-danger mt-2">{{ errors.dias_semana }}</div>
                </div>

                <!-- Configurações por Dia -->
                <div v-if="selectedDay" class="config-section mb-4">
                  <h6 class="section-label mb-3">
                    <i class="fas fa-clock me-2 text-primary"></i>
                    Configurações para&nbsp;<span class="text-primary">{{ getDayLabelFull(selectedDay) }}</span>
                  </h6>
                  
                  <!-- Mensagem de dia desativado -->
                  <div v-if="!formData.dias_semana.includes(selectedDay)" class="day-disabled-message">
                    Este dia está desativado
                  </div>
                  
                  <!-- Grid de configurações do dia -->
                  <div v-else class="day-config-grid">
                    <!-- Horários principais -->
                    <div class="day-config-row">
                    <div class="day-config-item">
                      <label class="form-label fw-bold mb-1">Horário de início:</label>
                      <input
                        type="time"
                        v-model="currentDayConfig.horario_inicio"
                        class="form-control form-control-sm"
                        :class="{ 'is-invalid': errors[`${selectedDay}_horario_inicio`], 'opacity-50': !formData.dias_semana.includes(selectedDay) }"
                        :disabled="!formData.dias_semana.includes(selectedDay)"
                        style="text-align: center; padding: 0.25rem 0.5rem;"
                      >
                      <div v-if="errors[`${selectedDay}_horario_inicio`]" class="invalid-feedback">{{ errors[`${selectedDay}_horario_inicio`] }}</div>
                    </div>
                      
                    <div class="day-config-item">
                      <label class="form-label fw-bold mb-1">Horário de término:</label>
                      <input
                        type="time"
                        v-model="currentDayConfig.horario_fim"
                        class="form-control form-control-sm"
                        :class="{ 'is-invalid': errors[`${selectedDay}_horario_fim`], 'opacity-50': !formData.dias_semana.includes(selectedDay) }"
                        :disabled="!formData.dias_semana.includes(selectedDay)"
                        style="text-align: center; padding: 0.25rem 0.5rem;"
                      >
                      <div v-if="errors[`${selectedDay}_horario_fim`]" class="invalid-feedback">{{ errors[`${selectedDay}_horario_fim`] }}</div>
                    </div>
                    </div>

                    <!-- Toggle de horário de almoço -->
                    <div class="day-config-row mt-3">
                      <div class="day-config-item-full">
                        <div class="custom-switch-wrapper">
                          <input
                            class="custom-switch-input"
                            type="checkbox"
                            :id="`tem_horario_almoco_${selectedDay}`"
                            v-model="currentDayConfig.tem_horario_almoco"
                          >
                          <label class="custom-switch-label" :for="`tem_horario_almoco_${selectedDay}`">
                            <span class="custom-switch-slider"></span>
                            <span class="custom-switch-text">
                              <i class="fas fa-utensils me-2 text-primary"></i>
                              Horário de almoço
                            </span>
                          </label>
                        </div>
                      </div>
                    </div>

                    <!-- Horários de almoço (se habilitado) -->
                    <div v-if="currentDayConfig.tem_horario_almoco" class="day-config-row mt-3">
                    <div class="day-config-item">
                      <label class="form-label fw-bold mb-1">Início do almoço:</label>
                      <input
                        type="time"
                        v-model="currentDayConfig.horario_almoco_inicio"
                        class="form-control form-control-sm"
                        :class="{ 'is-invalid': errors[`${selectedDay}_horario_almoco_inicio`], 'opacity-50': !formData.dias_semana.includes(selectedDay) }"
                        :disabled="!formData.dias_semana.includes(selectedDay) || !currentDayConfig.tem_horario_almoco"
                        style="text-align: center; padding: 0.25rem 0.5rem;"
                      >
                      <div v-if="errors[`${selectedDay}_horario_almoco_inicio`]" class="invalid-feedback">{{ errors[`${selectedDay}_horario_almoco_inicio`] }}</div>
                    </div>
                      
                    <div class="day-config-item">
                      <label class="form-label fw-bold mb-1">Fim do almoço:</label>
                      <input
                        type="time"
                        v-model="currentDayConfig.horario_almoco_fim"
                        class="form-control form-control-sm"
                        :class="{ 'is-invalid': errors[`${selectedDay}_horario_almoco_fim`], 'opacity-50': !formData.dias_semana.includes(selectedDay) }"
                        :disabled="!formData.dias_semana.includes(selectedDay) || !currentDayConfig.tem_horario_almoco"
                        style="text-align: center; padding: 0.25rem 0.5rem;"
                      >
                      <div v-if="errors[`${selectedDay}_horario_almoco_fim`]" class="invalid-feedback">{{ errors[`${selectedDay}_horario_almoco_fim`] }}</div>
                    </div>
                    </div>
                  </div>
                </div>

                <!-- Configurações Adicionais -->
                <!-- <div class="config-section">
                  <h6 class="section-label mb-3">
                    <i class="fas fa-cog me-2 text-primary"></i>
                    Configurações Adicionais
                  </h6>
                  <div class="row">
                  </div>
                </div> -->

              </div>
            </div>

            <!-- Configurações Avançadas -->
            <!--
            <div class="config-card mb-4">
              <div class="config-card-header">
                <h4 class="config-card-title">
                  <i class="fas fa-cogs me-2"></i>
                  Configurações Avançadas
                </h4>
              </div>
              <div class="config-card-body">
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label class="form-label fw-bold">Antecedência Mínima (horas):</label>
                    <select
                      v-model="formData.antecedencia_minima_agendamento"
                      class="form-select form-select-lg"
                    >
                      <option value="0">Sem antecedência</option>
                      <option value="1">1 hora</option>
                      <option value="2">2 horas</option>
                      <option value="4">4 horas</option>
                      <option value="8">8 horas</option>
                      <option value="24">1 dia</option>
                      <option value="48">2 dias</option>
                    </select>
                  </div>
                  <div class="col-md-6 mb-3">
                    <label class="form-label fw-bold">Antecedência Máxima (dias):</label>
                    <select
                      v-model="formData.antecedencia_maxima_agendamento"
                      class="form-select form-select-lg"
                    >
                      <option value="168">7 dias</option>
                      <option value="336">14 dias</option>
                      <option value="720">30 dias</option>
                      <option value="1440">60 dias</option>
                      <option value="2160">90 dias</option>
                    </select>
                  </div>
                </div>

                <div class="form-check form-switch mb-3">
                  <input
                    class="form-check-input"
                    type="checkbox"
                    id="permitir_agendamento_feriados"
                    v-model="formData.permitir_agendamento_feriados"
                  >
                  <label class="form-check-label fw-bold" for="permitir_agendamento_feriados">
                    Permitir agendamentos em feriados
                  </label>
                </div>
              </div>
            </div>
            -->

            <!-- Botão de Salvar (Mobile) -->
            <div class="action-buttons-mobile d-md-none">
              <button
                type="submit"
                class="btn btn-primary btn-sm w-100"
                :disabled="isSaving"
              >
                <span v-if="isSaving" class="spinner-border spinner-border-sm me-2" role="status"></span>
                <i v-else class="fas fa-save me-2"></i>
                {{ isSaving ? 'Salvando...' : 'Salvar Configurações' }}
              </button>
            </div>

            <!-- Botão de Restaurar Padrão -->
            <div class="action-buttons">
              <button
                type="button"
                class="btn btn-outline-secondary btn-sm"
                @click="restaurarPadrao"
                :disabled="isSaving"
              >
                <i class="fas fa-undo me-1"></i>
                Restaurar Padrão
              </button>
            </div>

          </form>

          <!-- Modal de Consultório -->
          <teleport to="body">
            <ConsultorioModal
              modal-id="consultorioModal"
              :consultorio="selectedConsultorioForEdit"
              :is-loading="isSavingConsultorio"
              @save="handleSaveConsultorio"
              @close="closeConsultorioModal"
            />
          </teleport>

        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapMutations, mapState } from "vuex";
import agendaConfigService from "@/services/agendaConfigService.js";
import consultorioService from "@/services/consultorioService.js";
import ConsultorioSelector from "@/components/ConsultorioSelector.vue";
import ConsultorioModal from "@/components/ConsultorioModal.vue";
import { openModal, closeModal } from "@/utils/modalHelper.js";
import cSwal from "@/utils/cSwal.js";

export default {
  name: "configuracoes-agenda",
  components: {
    ConsultorioSelector,
    ConsultorioModal
  },
  data() {
    return {
      // Estados de carregamento
      isLoading: true,
      isSaving: false,
      isLoadingConsultorios: true,
      isSavingConsultorio: false,

      // Dados dos consultórios
      consultorios: [],
      selectedConsultorioId: null,
      selectedConsultorioForEdit: null,

      // Dia selecionado para configuração
      selectedDay: null,

      // Erros e validações
      errors: {},
      formData: {
        dias_semana: ['segunda', 'terca', 'quarta', 'quinta', 'sexta'],
        duracao_padrao_consulta: 30,
        permitir_duracao_personalizada: true,
        intervalo_entre_consultas: 0,
        permitir_agendamento_feriados: false,
        antecedencia_minima_agendamento: 0,
        antecedencia_maxima_agendamento: 720,
        // Configurações por dia
        configuracoes_por_dia: {}
      },
      diasSemana: [
        { value: 'segunda', label: 'Segunda', labelFull: 'Segunda-feira', icon: 'S' },
        { value: 'terca', label: 'Terça', labelFull: 'Terça-feira', icon: 'T' },
        { value: 'quarta', label: 'Quarta', labelFull: 'Quarta-feira', icon: 'Q' },
        { value: 'quinta', label: 'Quinta', labelFull: 'Quinta-feira', icon: 'Q' },
        { value: 'sexta', label: 'Sexta', labelFull: 'Sexta-feira', icon: 'S' },
        { value: 'sabado', label: 'Sábado', labelFull: 'Sábado', icon: 'S' },
        { value: 'domingo', label: 'Domingo', labelFull: 'Domingo', icon: 'D' },
      ]
    };
  },
  computed: {
    ...mapState([
      "showSidenav",
    ]),

    selectedConsultorio() {
      return this.consultorios.find(c => c.id === this.selectedConsultorioId) || null;
    },

    currentDayConfig() {
      if (!this.selectedDay) return {};
      return this.formData.configuracoes_por_dia[this.selectedDay] || {};
    }
  },
  async created() {
    await this.carregarConsultorios();

    // Selecionar o primeiro dia ativo por padrão
    if (this.formData.dias_semana.length > 0) {
      this.selectedDay = this.formData.dias_semana[0];
      this.ensureDayConfig(this.selectedDay);
    }
  },
  methods: {
    ...mapMutations(['setToken']),

    // === MÉTODOS DE CONSULTÓRIOS ===
    async carregarConsultorios() {
      try {
        this.isLoadingConsultorios = true;
        const consultorios = await consultorioService.getConsultorios();

        this.consultorios = consultorios || [];

        // Selecionar o primeiro consultório automaticamente
        if (this.consultorios.length > 0 && !this.selectedConsultorioId) {
          this.selectedConsultorioId = this.consultorios[0].id;
          await this.carregarConfiguracoes();
        } else {
          // Se não há consultórios, parar o loading das configurações
          this.isLoading = false;
        }
      } catch (error) {
        console.error('Erro ao carregar consultórios:', error);
        cSwal.cError('Erro ao carregar consultórios.');
        // Em caso de erro, também parar o loading das configurações
        this.isLoading = false;
      } finally {
        this.isLoadingConsultorios = false;
      }
    },

    async onConsultorioSelected(consultorio) {
      if (this.selectedConsultorioId !== consultorio.id) {
        this.selectedConsultorioId = consultorio.id;
        await this.carregarConfiguracoes();
      }
    },

    openConsultorioModal(consultorio = null) {
      this.selectedConsultorioForEdit = consultorio;
      openModal('consultorioModal');
    },

    closeConsultorioModal() {
      this.selectedConsultorioForEdit = null;
    },

    async handleSaveConsultorio(consultorioData) {
      try {
        this.isSavingConsultorio = true;

        let savedConsultorio;
        if (this.selectedConsultorioForEdit) {
          // Editando consultório existente
          savedConsultorio = await consultorioService.updateConsultorio(
            this.selectedConsultorioForEdit.id,
            consultorioData
          );
          cSwal.cSuccess('Consultório atualizado com sucesso!');
        } else {
          // Criando novo consultório
          savedConsultorio = await consultorioService.createConsultorio(consultorioData);
          cSwal.cSuccess('Consultório criado com sucesso!');
        }

        if (savedConsultorio) {
          await this.carregarConsultorios();

          // Se foi criado um novo consultório, selecioná-lo
          if (!this.selectedConsultorioForEdit) {
            this.selectedConsultorioId = savedConsultorio.id;
            await this.carregarConfiguracoes();
          }
        }

        // Fechar modal
        closeModal('consultorioModal');

      } catch (error) {
        console.error('Erro ao salvar consultório:', error);

        if (error.response && error.response.data && error.response.data.errors) {
          // Mostrar erros de validação específicos
          const errors = error.response.data.errors;
          const errorMessages = Object.values(errors).flat().join('\n');
          cSwal.cError(`Erro de validação:\n${errorMessages}`);
        } else {
          cSwal.cError('Erro ao salvar consultório.');
        }
      } finally {
        this.isSavingConsultorio = false;
      }
    },

    // === MÉTODOS DE CONFIGURAÇÕES ===
    async carregarConfiguracoes() {
      if (!this.selectedConsultorioId) {
        this.isLoading = false;
        return;
      }

      try {
        this.isLoading = true;
        const config = await agendaConfigService.getAgendaConfig(this.selectedConsultorioId);

        if (config) {
          this.formData = { ...this.formData, ...config };

          // Inicializar configurações por dia se não existirem
          if (!this.formData.configuracoes_por_dia) {
            this.formData.configuracoes_por_dia = {};
          }

          // Garantir que todos os dias ativos tenham configuração
          this.formData.dias_semana.forEach(dia => {
            this.ensureDayConfig(dia);
          });

          // Selecionar o primeiro dia ativo se nenhum estiver selecionado
          if (!this.selectedDay && this.formData.dias_semana.length > 0) {
            this.selectedDay = this.formData.dias_semana[0];
          }


        } else {
          // Se não há configuração, usar dados padrão
          this.formData = this.getDefaultFormData();

          // Inicializar configurações para todos os dias padrão
          this.formData.dias_semana.forEach(dia => {
            this.ensureDayConfig(dia);
          });

          // Selecionar o primeiro dia
          if (this.formData.dias_semana.length > 0) {
            this.selectedDay = this.formData.dias_semana[0];
          }


        }
      } catch (error) {
        console.error('Erro ao carregar configurações:', error);
        cSwal.cError('Erro ao carregar configurações da agenda.');
      } finally {
        this.isLoading = false;
      }
    },

    getDefaultFormData() {
      return {
        dias_semana: ['segunda', 'terca', 'quarta', 'quinta', 'sexta'],
        duracao_padrao_consulta: 30,
        permitir_duracao_personalizada: true,
        intervalo_entre_consultas: 0,
        permitir_agendamento_passado: false,
        permitir_agendamento_feriados: false,
        antecedencia_minima_agendamento: 0,
        antecedencia_maxima_agendamento: 720,
        configuracoes_por_dia: {}
      };
    },

    getDefaultDayConfig() {
      return {
        horario_inicio: '08:00',
        horario_fim: '18:00',
        tem_horario_almoco: false,
        horario_almoco_inicio: '12:00',
        horario_almoco_fim: '13:00'
      };
    },

    selectDay(dia) {
      // Apenas selecionar o dia (não ativar automaticamente)
      this.selectedDay = dia;
      this.ensureDayConfig(dia);
    },

    ensureDayConfig(dia) {
      // Inicializar configuração do dia se não existir
      if (!this.formData.configuracoes_por_dia[dia]) {
        this.formData.configuracoes_por_dia[dia] = this.getDefaultDayConfig();
      }
    },

    getDayLabel(dia) {
      const diaObj = this.diasSemana.find(d => d.value === dia);
      return diaObj ? diaObj.label : dia;
    },

    getDayLabelFull(dia) {
      const diaObj = this.diasSemana.find(d => d.value === dia);
      return diaObj ? diaObj.labelFull : dia;
    },

    async salvarConfiguracoes() {
      if (!this.selectedConsultorioId) {
        cSwal.cError('Selecione um consultório para salvar as configurações.');
        return;
      }

      try {
        this.isSaving = true;
        this.errors = {};

        // Validar configurações antes de salvar
        const validation = agendaConfigService.validateConfig(this.formData);
        if (!validation.isValid) {
          this.errors = validation.errors;
          cSwal.cError('Por favor, corrija os erros no formulário.');
          return;
        }

        // Preparar dados para envio - apenas os campos necessários
        const dataToSend = {
          dias_semana: this.formData.dias_semana,
          configuracoes_por_dia: this.formData.configuracoes_por_dia,
          duracao_padrao_consulta: this.formData.duracao_padrao_consulta,
          permitir_duracao_personalizada: this.formData.permitir_duracao_personalizada,
          intervalo_entre_consultas: this.formData.intervalo_entre_consultas,
          permitir_agendamento_passado: this.formData.permitir_agendamento_passado,
          permitir_agendamento_feriados: this.formData.permitir_agendamento_feriados,
          antecedencia_minima_agendamento: this.formData.antecedencia_minima_agendamento,
          antecedencia_maxima_agendamento: this.formData.antecedencia_maxima_agendamento
        };





        const config = await agendaConfigService.updateAgendaConfig(
          this.selectedConsultorioId,
          dataToSend
        );

        if (config) {
          cSwal.cSuccess('Configurações da agenda salvas com sucesso!');

          // Atualizar o store com as novas configurações
          await this.$store.dispatch('agendaConfig/loadAgendaConfig', config);
          this.$store.dispatch('agendaConfig/generateTimeSlots');

          // Atualizar o token para incluir as novas configurações
          try {
            const usuariosService = (await import('@/services/usuariosService.js')).default;
            await usuariosService.refreshAuth({ force: true });
          } catch (refreshError) {
            console.error('Erro ao atualizar token:', refreshError);
          }
        }
      } catch (error) {
        console.error('Erro ao salvar configurações:', error);

        if (error.response && error.response.data && error.response.data.errors) {
          this.errors = error.response.data.errors;
          cSwal.cError('Por favor, corrija os erros no formulário.');
        } else {
          cSwal.cError('Erro ao salvar configurações da agenda.');
        }
      } finally {
        this.isSaving = false;
      }
    },

    async restaurarPadrao() {
      if (!this.selectedConsultorioId) {
        cSwal.cError('Selecione um consultório para restaurar as configurações.');
        return;
      }

      try {
        const result = await cSwal.cConfirm(
          'Tem certeza que deseja restaurar as configurações padrão?',
          'Esta ação não pode ser desfeita.'
        );

        if (result.isConfirmed) {
          this.isLoading = true;
          const config = await agendaConfigService.resetAgendaConfig(this.selectedConsultorioId);

          if (config) {
            this.formData = { ...this.formData, ...config };

            // Atualizar o store com as configurações padrão
            await this.$store.dispatch('agendaConfig/loadAgendaConfig', config);
            this.$store.dispatch('agendaConfig/generateTimeSlots');

            cSwal.cSuccess('Configurações restauradas para os valores padrão!');
          }
        }
      } catch (error) {
        console.error('Erro ao restaurar configurações:', error);
        cSwal.cError('Erro ao restaurar configurações padrão.');
      } finally {
        this.isLoading = false;
      }
    },

    toggleDiaSemana(dia) {
      const index = this.formData.dias_semana.indexOf(dia);
      if (index > -1) {
        // Se já está selecionado, remove (mas mantém pelo menos um dia)
        if (this.formData.dias_semana.length > 1) {
          this.formData.dias_semana.splice(index, 1);

          // Se o dia removido era o selecionado, selecionar outro
          if (this.selectedDay === dia) {
            this.selectedDay = this.formData.dias_semana[0] || null;
          }

          // Remover configuração do dia
          if (this.formData.configuracoes_por_dia[dia]) {
            delete this.formData.configuracoes_por_dia[dia];
          }
        } else {
          cSwal.cWarning('Você deve manter pelo menos um dia da semana ativo.');
        }
      } else {
        // Se não está selecionado, adiciona
        this.formData.dias_semana.push(dia);

        // Inicializar configuração do dia
        this.ensureDayConfig(dia);

        // Se não há dia selecionado, selecionar este
        if (!this.selectedDay) {
          this.selectedDay = dia;
        }
      }
    },

    handleSidenavAction(action) {
      switch (action) {
        case 'goToAgenda':
          this.$router.push('/agenda');
          break;
        case 'restaurarPadrao':
          this.restaurarPadrao();
          break;
        case 'salvar':
          this.salvarConfiguracoes();
          break;
      }
    }
  }
};
</script>

<style scoped>
/* Garantir que não haja conflitos de z-index na página */
.main-page-content {
  position: relative;
  z-index: 1;
}

.agenda-config-form {
  max-width: 100%;
}

/* Config Cards */
.config-card {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s ease;
}

.config-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.config-card-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 0.75rem 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.config-card-title {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #495057;
  display: flex;
  align-items: center;
}

.config-card-title i {
  color: #007bff;
  font-size: 0.9rem;
}

.config-card-body {
  padding: 1rem;
}

/* Form Controls */
.form-control, .form-select {
  border-radius: 10px;
  border: 2px solid #e9ecef;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  transition: all 0.3s ease;
  background-color: #ffffff;
}

.form-control:focus, .form-select:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
  background-color: #ffffff;
}

.form-control-sm, .form-select-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  border-radius: 8px;
}

.form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.75rem;
  font-size: 0.95rem;
}

/* Config Header Mobile */
.config-header-mobile {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: stretch;
}

@media (min-width: 769px) {
  .config-header-mobile {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
  }
}

.config-save-btn {
  align-self: flex-end;
  min-width: 120px;
  white-space: nowrap;
  flex-shrink: 0;
}

@media (max-width: 768px) {
  .config-save-btn {
    align-self: center;
    width: auto;
    margin-bottom: 2rem;
  }
}

@media (max-width: 576px) {
  .config-save-btn {
    margin-bottom: 0.75rem;
  }
}

@media (max-width: 576px) {
  .config-header-mobile {
    gap: 0.75rem;
  }
}

/* Section Labels */
.section-label {
  font-size: 1rem;
  font-weight: 600;
  color: #495057;
  display: flex;
  align-items: flex-start;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e9ecef;
  word-break: break-word;
  hyphens: auto;
  line-height: 1.3;
  gap: 0.5rem;
}

.section-label i {
  font-size: 0.9rem;
  flex-shrink: 0;
  align-self: flex-start;
}

.section-label span {
  flex: 1;
  min-width: 0;
}

.config-section {
  position: relative;
}

.config-section:not(:last-child)::after {
  content: '';
  position: absolute;
  bottom: -1rem;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, #e9ecef 20%, #e9ecef 80%, transparent 100%);
}

/* Days Selector */
.days-selector {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
  margin: 0.5rem 0;
}

.day-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.day-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 70px;
  height: 65px;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #ffffff;
  user-select: none;
  position: relative;
  padding: 0.25rem;
}

.day-option:hover {
  border-color: #007bff;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.2);
}

.day-option.active {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border-color: #007bff;
  color: white;
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.day-option.selected:not(.active) {
  border-color: #495057;
  background: #ffffff;
  color: #495057;
  box-shadow: 0 4px 15px rgba(73, 80, 87, 0.2);
  transform: translateY(-2px);
}

.day-option.selected.active {
  border-color: #007bff;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4), 0 0 0 4px #ffffff, 0 0 0 6px rgba(0, 123, 255, 0.6);
  transform: translateY(-2px);
}

.day-icon {
  font-size: 1.1rem;
  font-weight: bold;
  margin-bottom: 0.15rem;
  margin-top: 0.5rem;
}

.day-name {
  font-size: 0.65rem;
  font-weight: 600;
  text-align: center;
  line-height: 1;
  margin-bottom: 0.25rem;
}

.selected-arrow {
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 1rem;
  animation: selectedBounce 1.5s infinite;
}

.day-option.selected:not(.active) .selected-arrow {
  color: #495057;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.day-option.selected.active .selected-arrow {
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

@keyframes selectedBounce {
  0%, 100% { transform: translateX(-50%) translateY(0); }
  50% { transform: translateX(-50%) translateY(-3px); }
}

/* Custom Switch Styles */
.custom-switch-wrapper {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
}

.custom-switch-input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.custom-switch-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  margin: 0;
}

.custom-switch-slider {
  position: relative;
  display: inline-block;
  width: 3rem;
  height: 1.5rem;
  background-color: #cbd5e0;
  border-radius: 1rem;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.custom-switch-slider::before {
  content: '';
  position: absolute;
  top: 0.125rem;
  left: 0.125rem;
  width: 1.25rem;
  height: 1.25rem;
  background-color: #ffffff;
  border-radius: 50%;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.custom-switch-input:checked + .custom-switch-label .custom-switch-slider {
  background-color: #007bff;
}

.custom-switch-input:checked + .custom-switch-label .custom-switch-slider::before {
  transform: translateX(1.5rem);
}

.custom-switch-input:focus + .custom-switch-label .custom-switch-slider {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
}

.custom-switch-text {
  font-weight: 600;
  color: #495057;
  font-size: 0.95rem;
  user-select: none;
}

/* Day Toggle Wrapper */
.day-toggle-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 24px;
}

.custom-switch-wrapper-small {
  display: inline-flex;
  align-items: center;
}

.custom-switch-slider-small {
  position: relative;
  display: inline-block;
  width: 2.5rem;
  height: 1.25rem;
  background-color: #cbd5e0;
  border-radius: 1rem;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.custom-switch-slider-small::before {
  content: '';
  position: absolute;
  top: 0.125rem;
  left: 0.125rem;
  width: 1rem;
  height: 1rem;
  background-color: #ffffff;
  border-radius: 50%;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.custom-switch-input:checked + .custom-switch-label .custom-switch-slider-small {
  background-color: #007bff;
}

.custom-switch-input:checked + .custom-switch-label .custom-switch-slider-small::before {
  transform: translateX(1.25rem);
}

.custom-switch-input:focus + .custom-switch-label .custom-switch-slider-small {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
}

/* Day Config Grid */
.day-config-grid {
  max-width: 600px;
  margin: 0 auto;
}

.day-config-row {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  align-items: start;
}

.day-config-item {
  display: flex;
  flex-direction: column;
  width: auto;
  min-width: 0;
}

.day-config-item-full {
  grid-column: 1 / -1;
  display: flex;
  justify-content: center;
}

.day-config-item .form-control {
  width: 120px;
}

/* Day Disabled Message */
.day-disabled-message {
  text-align: center;
  color: #adb5bd;
  font-size: 0.9rem;
  font-weight: 500;
  font-style: italic;
  padding: 1rem;
  max-width: 600px;
  margin: 0 auto;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 1.5rem;
  padding: 1rem 0;
}

.btn-lg {
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 12px;
  transition: all 0.3s ease;
  min-width: 200px;
}

.btn-primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border: none;
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

.btn-outline-secondary {
  border: 2px solid #6c757d;
  color: #6c757d;
  background: transparent;
}

.btn-outline-secondary:hover {
  background: #6c757d;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

/* Invalid Feedback */
.invalid-feedback {
  font-size: 0.875rem;
  font-weight: 500;
  margin-top: 0.5rem;
}

/* Loading States */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .config-card-body {
    padding: 1.5rem;
  }

  .config-card-header {
    padding: 1rem;
  }

  .days-selector {
    gap: 0.75rem;
  }

  .day-container {
    gap: 0.4rem;
  }

  .day-option {
    width: 60px;
    height: 60px;
    padding: 0.2rem;
  }

  .day-icon {
    font-size: 1rem;
    margin-top: 0.3rem;
    margin-bottom: 0.1rem;
  }

  .day-name {
    font-size: 0.6rem;
  }

  .section-label {
    font-size: 0.9rem;
  }

  .day-config-row {
    grid-template-columns: 1fr;
  }

  .btn-lg {
    width: 100%;
    min-width: auto;
  }

  .custom-switch-slider-small {
    width: 2.25rem;
    height: 1.125rem;
  }

  .custom-switch-slider-small::before {
    width: 0.875rem;
    height: 0.875rem;
  }

  .custom-switch-input:checked + .custom-switch-label .custom-switch-slider-small::before {
    transform: translateX(1.125rem);
  }
}

@media (max-width: 576px) {
  .days-selector {
    gap: 0.5rem;
  }

  .day-container {
    gap: 0.3rem;
  }

  .day-option {
    width: 55px;
    height: 55px;
    padding: 0.15rem;
  }

  .day-icon {
    font-size: 0.9rem;
    margin-top: 0.2rem;
    margin-bottom: 0.05rem;
  }

  .day-name {
    font-size: 0.55rem;
  }

  .custom-switch-slider-small {
    width: 2rem;
    height: 1rem;
  }

  .custom-switch-slider-small::before {
    width: 0.75rem;
    height: 0.75rem;
  }

  .custom-switch-input:checked + .custom-switch-label .custom-switch-slider-small::before {
    transform: translateX(1rem);
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.config-card {
  animation: fadeIn 0.5s ease-out;
}

.config-card:nth-child(1) { animation-delay: 0.1s; }
.config-card:nth-child(2) { animation-delay: 0.2s; }
.config-card:nth-child(3) { animation-delay: 0.3s; }
.config-card:nth-child(4) { animation-delay: 0.4s; }
.config-card:nth-child(5) { animation-delay: 0.5s; }
</style>
