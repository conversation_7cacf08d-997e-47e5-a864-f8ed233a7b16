<?php

namespace App\Http\Controllers;

use App\Models\Aparatologia;
use App\Models\Paciente;
use App\Models\HistoricoPaciente;
use Illuminate\Http\Request;

class AparatologiaController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = auth()->payload();

        $query = Aparatologia::with('paciente');

        // Se não for admin do sistema, filtra apenas os registros dos pacientes da clínica do usuário
        if (!$user['system_admin']) {
            $query->whereHas('paciente', function ($q) use ($user) {
                $q->where('clinica_id', $user['clinica']['id']);
            });
        }

        $aparatologias = $query->get();

        return response()->json($aparatologias);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $user = auth()->payload();
            $body = $request->all();

            // Validação dos dados
            $request->validate([
                'paciente_id' => 'exists:pacientes,id',
                'aparelho_utilizado' => 'sometimes|nullable|string',
                'aparelho_prescricao' => 'sometimes|nullable|string',
                'tipo_colagem' => 'sometimes|nullable|string',
                'contencao_superior' => 'sometimes|nullable|string',
                'contencao_inferior' => 'sometimes|nullable|string',
                'exercicios_miofuncionais' => 'sometimes|nullable|string',
                'observacoes' => 'sometimes|nullable|string',
                'alinhador_tipo' => 'sometimes|nullable|string',
                'alinhador_observacoes' => 'sometimes|nullable|string',
                'ortopedia_tipo_aparelho' => 'sometimes|nullable|string',
                'ortopedia_laboratorio' => 'sometimes|nullable|string',
                'miofuncional' => 'sometimes|nullable|string',
                'outro_aparelho' => 'sometimes|nullable|string',
            ]);

            // Verifica se o usuário tem permissão para criar um registro para este paciente
            if (!$user['system_admin']) {
                $paciente = Paciente::find($body['paciente_id']);

                if (!$paciente || $paciente->clinica_id !== $user['clinica']['id']) {
                    return responseError([
                        'message' => 'Você não tem permissão para criar um registro para este paciente.'
                    ]);
                }
            }

            // Cria o registro
            $aparatologia = new Aparatologia();
            $aparatologia->paciente_id = $body['paciente_id'];
            $aparatologia->aparelho_utilizado = $body['aparelho_utilizado'] ?? null;
            $aparatologia->aparelho_prescricao = $body['aparelho_prescricao'] ?? null;
            $aparatologia->tipo_colagem = $body['tipo_colagem'] ?? null;
            $aparatologia->contencao_superior = $body['contencao_superior'] ?? null;
            $aparatologia->contencao_inferior = $body['contencao_inferior'] ?? null;
            $aparatologia->exercicios_miofuncionais = $body['exercicios_miofuncionais'] ?? null;
            $aparatologia->observacoes = $body['observacoes'] ?? null;
            $aparatologia->alinhador_tipo = $body['alinhador_tipo'] ?? null;
            $aparatologia->alinhador_observacoes = $body['alinhador_observacoes'] ?? null;
            $aparatologia->ortopedia_tipo_aparelho = $body['ortopedia_tipo_aparelho'] ?? null;
            $aparatologia->ortopedia_laboratorio = $body['ortopedia_laboratorio'] ?? null;
            $aparatologia->miofuncional = $body['miofuncional'] ?? null;
            $aparatologia->outro_aparelho = $body['outro_aparelho'] ?? null;
            $aparatologia->save();

            return responseSuccess();
        } catch (\Exception $e) {
            return responseError([
                'message' => $e->getMessage(),
                'stacktrace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $user = auth()->payload();

        $aparatologia = Aparatologia::with('paciente')->find($id);

        if (!$aparatologia) {
            return response()->json(['error' => 'Registro de aparatologia não encontrado.'], 404);
        }

        // Verifica se o usuário tem permissão para visualizar este registro
        if (!$user['system_admin'] && $aparatologia->paciente->clinica_id !== $user['clinica']['id']) {
            return response()->json(['error' => 'Você não tem permissão para visualizar este registro.'], 403);
        }

        return response()->json($aparatologia);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Aparatologia $aparatologia)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $user = auth()->payload();
            $body = $request->all();

            $aparatologia = Aparatologia::with('paciente')->find($id);

            if (!$aparatologia) {
                return response()->json(['error' => 'Registro de aparatologia não encontrado.'], 404);
            }

            // Verifica se o usuário tem permissão para atualizar este registro
            if (!$user['system_admin'] && $aparatologia->paciente->clinica_id !== $user['clinica']['id']) {
                return response()->json(['error' => 'Você não tem permissão para atualizar este registro.'], 403);
            }

            // Salvar os dados antigos antes de atualizar
            $dadosAntigos = $aparatologia->toArray();

            // Atualiza os campos
            $aparatologia->fill($body);
            $aparatologia->save();

            return responseSuccess();
        } catch (\Exception $e) {
            return responseError([
                'message' => $e->getMessage(),
                'stacktrace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $user = auth()->payload();

            $aparatologia = Aparatologia::with('paciente')->find($id);

            if (!$aparatologia) {
                return response()->json(['error' => 'Registro de aparatologia não encontrado.'], 404);
            }

            // Verifica se o usuário tem permissão para excluir este registro
            if (!$user['system_admin'] && $aparatologia->paciente->clinica_id !== $user['clinica']['id']) {
                return response()->json(['error' => 'Você não tem permissão para excluir este registro.'], 403);
            }

            // Salvar os dados antes de excluir
            $dadosAparatologia = $aparatologia->toArray();
            $pacienteId = $aparatologia->paciente_id;

            $aparatologia->delete();

            return responseSuccess();
        } catch (\Exception $e) {
            return responseError([
                'message' => $e->getMessage(),
                'stacktrace' => $e->getTraceAsString()
            ]);
        }
    }
}

