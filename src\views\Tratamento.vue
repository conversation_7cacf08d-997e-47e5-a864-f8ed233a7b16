<template>
  <div>
    <!-- Stats Header - Exibido independente da tab selecionada -->
    <ConsultasStats :paciente="paciente" class="mb-2" @pacienteChange="pacienteChange" @abrirModalConsulta="abrirModalNovaConsulta" />

    <div class="p-horizontal-divider light my-1"></div>

    <!-- Elegant Tab Navigation -->
    <div class="elegant-tab-container">
      <div class="elegant-tab-wrapper">
        <div class="tab-indicator" :style="indicatorStyle"></div>

        <button
          class="elegant-tab-button"
          :class="{ 'active': activeTab === 'consultas' }"
          @click="selectTab('consultas')"
          role="tab"
          :aria-selected="activeTab === 'consultas' ? 'true' : 'false'"
        >
          <div class="tab-content">
            <font-awesome-icon :icon="['fas', 'calendar-alt']" class="tab-icon" />
            <span class="tab-label">CONSULTAS E HISTÓRICO</span>
            <div class="tab-ripple"></div>
          </div>
        </button>

        <button
          class="elegant-tab-button"
          :class="{ 'active': activeTab === 'imagens' }"
          @click="selectTab('imagens')"
          role="tab"
          :aria-selected="activeTab === 'imagens' ? 'true' : 'false'"
        >
          <div class="tab-content">
            <font-awesome-icon :icon="['fas', 'images']" class="tab-icon" />
            <span class="tab-label">IMAGENS</span>
            <div class="tab-ripple"></div>
          </div>
        </button>
      </div>
    </div>

    <Transition>
      <div v-show="activeTab === 'consultas'" class="tab-pane fade show active">
        <ConsultasHistoricoTimeline ref="consultasHistoricoTimeline" :paciente="paciente" :dentistas="dentistas"
          @update:consultas="updateConsultas" @editar-consulta="editarConsulta" @ver-historico="verHistoricoConsulta" />
      </div>
    </Transition>

    <Transition>
      <div v-show="activeTab === 'imagens'" class="tab-pane fade show active">
        <Imagens
          :paciente="paciente"
          @pacienteChange="pacienteChange"
          :hideAnalyseButton="true"
          mode="regular"
        />
      </div>
    </Transition>

    <!-- Modais globais -->
    <ConsultaModal ref="consultaModal" :paciente-id="paciente.id" @consulta-salva="recarregarConsultas" />
    <HistoricoConsultaModal ref="historicoModal" />
  </div>
</template>

<script>
import ConsultasHistoricoTimeline from "@/views/components/ConsultasHistoricoTimeline.vue";
import ConsultasStats from "@/views/components/ConsultasStats.vue";
import Imagens from "@/views/Planejamento/components/Imagens.vue";
import ConsultaModal from "@/components/ConsultaModal.vue";
import HistoricoConsultaModal from "@/components/HistoricoConsultaModal.vue";
import setNavPills from "@/assets/js/nav-pills.js";

export default {
  name: "Tratamento",
  components: {
    ConsultasHistoricoTimeline,
    ConsultasStats,
    Imagens,
    ConsultaModal,
    HistoricoConsultaModal
  },
  props: {
    paciente: {
      type: Object,
      required: true
    },
    dentistas: {
      type: Array,
      default: () => []
    }
  },
  emits: ['pacienteChange', 'update:consultas', 'editar-consulta', 'ver-historico'],
  data() {
    return {
      activeTab: 'consultas',
      indicatorStyle: {
        transform: 'translateX(0%)',
        width: '50%'
      }
    };
  },
  methods: {
    selectTab(tab) {
      // Atualizar a variável de estado
      this.activeTab = tab;

      // Atualizar posição do indicador
      this.updateIndicator();
    },
    updateIndicator() {
      const translateX = this.activeTab === 'consultas' ? '0%' : '100%';
      this.indicatorStyle = {
        transform: `translateX(${translateX})`,
        width: '50%'
      };
    },
    updateConsultas(consultas) {
      this.$emit('update:consultas', consultas);
    },
    editarConsulta(id) {
      this.$refs.consultaModal.abrirModalEditarConsulta(id);
    },
    verHistoricoConsulta(id) {
      this.$refs.historicoModal.abrirModal(id);
    },
    recarregarConsultas() {
      if (this.$refs.consultasHistoricoTimeline) {
        this.$refs.consultasHistoricoTimeline.fetchConsultas();
      }
    },
    pacienteChange() {
      this.$emit('pacienteChange');
    },
    abrirModalNovaConsulta() {
      this.$refs.consultaModal.abrirModalNovaConsulta();
    }
  },
  mounted() {
    this.$nextTick(() => {
      // Garantir que a tab inicial (consultas) esteja ativa
      this.activeTab = 'consultas';

      // Inicializar posição do indicador
      this.updateIndicator();
    });
  }
};
</script>

<style scoped>
/* Transition effects for content */
.v-enter-active,
.v-leave-active {
  transition: opacity 0.3s ease;
}

.v-enter-from,
.v-leave-to {
  opacity: 0;
}

/* Elegant Tab Container */
.elegant-tab-container {
  display: flex;
  justify-content: center;
  margin-bottom: 0.5rem;
  padding: 0 1rem;
}

.elegant-tab-wrapper {
  position: relative;
  display: flex;
  width: 100%;
  max-width: 800px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  padding: 4px;
  box-shadow:
    0 6px 24px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.8),
    inset 0 -1px 0 rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

/* Animated Indicator */
.tab-indicator {
  position: absolute;
  top: 4px;
  left: 4px;
  bottom: 4px;
  background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
  border-radius: 8px;
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow:
    0 3px 16px rgba(13, 110, 253, 0.3),
    0 1px 6px rgba(13, 110, 253, 0.2);
  z-index: 1;
}

/* Tab Buttons */
.elegant-tab-button {
  position: relative;
  flex: 1;
  background: transparent;
  border: none;
  border-radius: 12px;
  padding: 0;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 2;
  overflow: hidden;
}

.elegant-tab-button .tab-content {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.6rem;
  padding: 10px 20px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
  width: 100%;
}

.elegant-tab-button .tab-icon {
  font-size: 0.95rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: #6c757d;
  flex-shrink: 0;
}

.elegant-tab-button .tab-label {
  font-size: 0.75rem;
  font-weight: 600;
  letter-spacing: 0.4px;
  text-transform: uppercase;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: #495057;
  white-space: nowrap;
  text-align: center;
}

/* Active State */
.elegant-tab-button.active .tab-icon {
  color: white;
  transform: scale(1.1);
}

.elegant-tab-button.active .tab-label {
  color: white;
  font-weight: 700;
}

/* Hover Effects */
.elegant-tab-button:not(.active):hover .tab-content {
  transform: translateY(-1px);
}

.elegant-tab-button:not(.active):hover .tab-icon {
  color: #0d6efd;
  transform: scale(1.05);
}

.elegant-tab-button:not(.active):hover .tab-label {
  color: #0d6efd;
}

/* Ripple Effect */
.tab-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.6s ease;
  pointer-events: none;
}

.elegant-tab-button:active .tab-ripple {
  width: 200px;
  height: 200px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .elegant-tab-container {
    padding: 0 0.5rem;
    margin-bottom: 0.5rem;
  }

  .elegant-tab-button .tab-label {
    font-size: 0.7rem;
  }

  .elegant-tab-button .tab-content {
    padding: 9px 18px;
    gap: 0.5rem;
  }

  .elegant-tab-button .tab-icon {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .elegant-tab-container {
    padding: 0 0.25rem;
    margin-bottom: 0.4rem;
  }

  .elegant-tab-button .tab-label {
    font-size: 0.65rem;
    letter-spacing: 0.3px;
  }

  .elegant-tab-button .tab-content {
    padding: 8px 14px;
    gap: 0.4rem;
  }

  .elegant-tab-button .tab-icon {
    font-size: 0.85rem;
  }
}
</style>
