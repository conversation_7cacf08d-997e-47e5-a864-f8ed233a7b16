<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('consultorios', function (Blueprint $table) {
            $table->id();
            $table->foreignId('clinica_id')->constrained()->onDelete('cascade');
            
            // Informações básicas do consultório
            $table->string('nome'); // Ex: "Consultório 1", "Sala A", "Cadeira Principal"
            $table->string('descricao')->nullable(); // Descrição opcional
            $table->string('cor', 7)->default('#007bff'); // Cor para identificação visual (hex)
            $table->string('icone')->default('fas fa-tooth'); // Ícone FontAwesome
            
            // Status e ordem
            $table->boolean('ativo')->default(true);
            $table->integer('ordem')->default(0); // Para ordenação na interface
            
            $table->timestamps();
            
            // Índices
            $table->index(['clinica_id', 'ativo']);
            $table->index(['clinica_id', 'ordem']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('consultorios');
    }
};
