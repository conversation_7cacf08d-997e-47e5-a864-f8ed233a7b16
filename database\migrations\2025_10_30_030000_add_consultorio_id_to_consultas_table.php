<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('consultas', function (Blueprint $table) {
            // Adicionar campo consultorio_id após dentista_id
            $table->foreignId('consultorio_id')->nullable()->after('dentista_id');
            
            // Criar foreign key com SET NULL on delete
            $table->foreign('consultorio_id')
                  ->references('id')
                  ->on('consultorios')
                  ->onDelete('set null');
            
            // Adicionar índice para performance
            $table->index('consultorio_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('consultas', function (Blueprint $table) {
            // Remover foreign key e índice
            $table->dropForeign(['consultorio_id']);
            $table->dropIndex(['consultorio_id']);
            
            // Remover coluna
            $table->dropColumn('consultorio_id');
        });
    }
};

