<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('planos', function (Blueprint $table) {
            $table->id();
            
            // Informações básicas do plano
            $table->string('nome');
            $table->text('descricao')->nullable();
            $table->string('cor', 7)->default('#007bff'); // Cor hexadecimal para UI
            $table->boolean('ativo')->default(true);
            
            // Módulos disponíveis
            $table->boolean('modulo_clinica')->default(true); // Sempre obrigatório
            $table->boolean('modulo_ortodontia')->default(false); // Opcional
            
            // Limites do plano (null = ilimitado)
            $table->integer('quantidade_usuarios')->nullable();
            $table->integer('quantidade_ortodontistas')->nullable();
            $table->integer('quantidade_agendas')->nullable();
            $table->integer('quantidade_cadeiras')->nullable();
            
            // Configurações de contrato
            $table->integer('meses_fidelidade_minima')->default(0);
            
            // Mentorias (só disponível se módulo ortodontia estiver ativo)
            $table->integer('quantidade_mentorias_mensais')->nullable();
            
            // Valor do plano (null = gratuito/ilimitado)
            $table->decimal('valor_mensal', 10, 2)->nullable();
            
            $table->timestamps();
            
            // Índices
            $table->index('ativo');
            $table->index('modulo_ortodontia');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('planos');
    }
};
