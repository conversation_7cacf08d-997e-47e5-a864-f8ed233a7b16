<template>
  <div class="modal fade consultorio-modal" :id="modalId" tabindex="-1" ref="modal" data-bs-backdrop="true" data-bs-keyboard="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content consultorio-modal">
        <div class="modal-header">
          <h5 class="modal-title">
            <i :class="isEditing ? 'fas fa-edit' : 'fas fa-plus'" class="me-2"></i>
            {{ isEditing ? 'Editar consultório' : 'Novo consultório' }}
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>

        <form @submit.prevent="handleSubmit">
          <div class="modal-body">
            <!-- Nome do Consultório -->
            <div class="mb-3">
              <label class="form-label fw-bold">
                <i class="fas fa-tag me-1"></i>
                Nome do consultório
              </label>
              <input
                type="text"
                v-model="formData.nome"
                class="form-control"
                :class="{ 'is-invalid': errors.nome }"
                placeholder="Ex: Consultório 1, Sala A, Cadeira Principal"
                maxlength="255"
                required
              >
              <div v-if="errors.nome" class="invalid-feedback">{{ errors.nome }}</div>
            </div>

            <!-- Descrição -->
            <div class="mb-3">
              <label class="form-label fw-bold">
                <i class="fas fa-align-left me-1"></i>
                Descrição (Opcional)
              </label>
              <input
                type="text"
                v-model="formData.descricao"
                class="form-control"
                :class="{ 'is-invalid': errors.descricao }"
                placeholder="Descrição adicional do consultório"
                maxlength="500"
              >
              <div v-if="errors.descricao" class="invalid-feedback">{{ errors.descricao }}</div>
              <small class="form-text text-muted">
                {{ formData.descricao ? formData.descricao.length : 0 }}/500 caracteres
              </small>
            </div>

            <!-- Ícone e Cor (ordem invertida) -->
            <div class="row">
              <div class="col-md-4 mb-3">
                <label class="form-label fw-bold">
                  <i class="fas fa-icons me-1"></i>
                  Ícone
                </label>
                <select
                  v-model="formData.icone"
                  class="form-select p-0 px-2"
                  :class="{ 'is-invalid': errors.icone }"
                >
                  <option v-for="icon in availableIcons" :key="icon.value" :value="icon.value">
                    {{ icon.label }}
                  </option>
                </select>
                <div v-if="errors.icone" class="invalid-feedback">{{ errors.icone }}</div>
              </div>

              <div class="col-md-8 mb-3">
                <label class="form-label fw-bold">
                  <i class="fas fa-palette me-1"></i>
                  Cor de identificação
                </label>
                <div class="color-selector">
                  <div
                    v-for="color in availableColors"
                    :key="color.value"
                    class="color-option"
                    :class="{ 'selected': formData.cor === color.value }"
                    :style="{ backgroundColor: color.value }"
                    @click="formData.cor = color.value"
                    :title="color.name"
                  >
                    <i v-if="formData.cor === color.value" class="fas fa-check"></i>
                  </div>
                </div>
                <div v-if="errors.cor" class="invalid-feedback">{{ errors.cor }}</div>
              </div>
            </div>

            <!-- Preview do Consultório -->
            <div class="mb-3">
              <label class="form-label fw-bold">
                <i class="fas fa-eye me-1"></i>
                Visualização
              </label>
              <div class="consultorio-preview">
                <div class="preview-card" :style="{ '--preview-color': formData.cor }">
                  <div class="preview-icon-container">
                    <i :class="formData.icone"></i>
                  </div>
                  <div class="preview-info">
                    <h6>{{ formData.nome || 'Nome do consultório' }}</h6>
                    <p>{{ formData.descricao || 'Descrição do consultório' }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Status (apenas para edição) -->
            <div v-if="isEditing" class="mb-3">
              <div class="custom-switch-wrapper">
                <input
                  class="custom-switch-input"
                  type="checkbox"
                  id="consultorio-ativo"
                  v-model="formData.ativo"
                >
                <label class="custom-switch-label" for="consultorio-ativo">
                  <span class="custom-switch-slider"></span>
                  <span class="custom-switch-text">
                    <i class="fas fa-power-off me-2 text-primary"></i>
                    Consultório ativo
                  </span>
                </label>
              </div>
              <small class="form-text text-muted d-block mt-2">
                Consultórios inativos não aparecerão na seleção de agendas
              </small>
            </div>
          </div>

          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-outline-secondary"
              data-bs-dismiss="modal"
              :disabled="isLoading"
            >
              <i class="fas fa-times me-1"></i>
              Cancelar
            </button>
            <button
              type="submit"
              class="btn btn-primary"
              :disabled="isLoading || !isFormValid"
            >
              <span v-if="isLoading" class="spinner-border spinner-border-sm me-2"></span>
              <i v-else :class="isEditing ? 'fas fa-save' : 'fas fa-plus'" class="me-1"></i>
              {{ isLoading ? 'Salvando...' : (isEditing ? 'Salvar Alterações' : 'Criar Consultório') }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { openModal, closeModal } from '@/utils/modalHelper.js';

export default {
  name: 'ConsultorioModal',
  props: {
    modalId: {
      type: String,
      default: 'consultorioModal'
    },
    consultorio: {
      type: Object,
      default: null
    },
    isLoading: {
      type: Boolean,
      default: false
    }
  },
  emits: ['save', 'close'],
  data() {
    return {
      formData: {
        nome: '',
        descricao: '',
        cor: '#007bff',
        icone: 'fas fa-tooth',
        ativo: true
      },
      errors: {},
      availableIcons: [
        { value: 'fas fa-clinic-medical', label: '🏥 Clínica' },
        { value: 'fas fa-door-open', label: '🚪 Sala' },
        { value: 'fas fa-tooth', label: '🦷 Dente' },
        { value: 'fas fa-user-md', label: '👨‍⚕️ Médico' },
        { value: 'fas fa-stethoscope', label: '🩺 Estetoscópio' },
        { value: 'fas fa-heartbeat', label: '💓 Batimento' },
        { value: 'fas fa-syringe', label: '💉 Seringa' },
        { value: 'fas fa-pills', label: '💊 Medicamento' },
        { value: 'fas fa-microscope', label: '🔬 Microscópio' },
        { value: 'fas fa-x-ray', label: '🩻 Raio-X' },
      ],
      availableColors: [
        { value: '#007bff', name: 'Azul Profissional' },
        { value: '#0056b3', name: 'Azul Escuro' },
        { value: '#28a745', name: 'Verde' },
        { value: '#17a2b8', name: 'Ciano' },
        { value: '#6f42c1', name: 'Roxo' },
        { value: '#d63384', name: 'Pink' },
        { value: '#fd7e14', name: 'Laranja' },
        { value: '#dc3545', name: 'Vermelho' },
        { value: '#6c757d', name: 'Cinza' },
      ]
    };
  },
  computed: {
    isEditing() {
      return !!this.consultorio;
    },
    isFormValid() {
      return this.formData.nome.trim().length > 0 && 
             this.formData.cor && 
             this.formData.icone;
    }
  },
  watch: {
    consultorio: {
      handler(newVal) {
        if (newVal) {
          this.formData = { ...newVal };
        } else {
          this.resetForm();
        }
        this.errors = {};
      },
      immediate: true
    }
  },
  methods: {
    resetForm() {
      this.formData = {
        nome: '',
        descricao: '',
        cor: '#007bff',
        icone: 'fas fa-tooth',
        ativo: true
      };
      this.errors = {};
    },

    handleSubmit() {
      this.errors = {};
      
      // Validação básica
      if (!this.formData.nome.trim()) {
        this.errors.nome = 'Nome é obrigatório';
        return;
      }

      if (this.formData.nome.length > 255) {
        this.errors.nome = 'Nome deve ter no máximo 255 caracteres';
        return;
      }

      if (this.formData.descricao && this.formData.descricao.length > 500) {
        this.errors.descricao = 'Descrição deve ter no máximo 500 caracteres';
        return;
      }

      if (!this.formData.cor || !/^#[0-9A-Fa-f]{6}$/.test(this.formData.cor)) {
        this.errors.cor = 'Cor inválida';
        return;
      }

      if (!this.formData.icone) {
        this.errors.icone = 'Ícone é obrigatório';
        return;
      }

      // Emitir evento de salvamento
      this.$emit('save', { ...this.formData });
    },

    show() {
      openModal(this.modalId);
    },

    hide() {
      closeModal(this.modalId);
    }
  },
  mounted() {
    // Escutar evento de fechamento do modal
    this.$refs.modal.addEventListener('hidden.bs.modal', () => {
      this.resetForm();
      this.$emit('close');
    });
  }
};
</script>

<style scoped>
.consultorio-modal .modal-content {
  border-radius: 16px;
  border: none;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
}

/* Estilos básicos do modal - o teleport resolve problemas de z-index */
.consultorio-modal .modal-content {
  border-radius: 0.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.consultorio-modal .modal-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 16px 16px 0 0;
  padding: 1.25rem 1.5rem;
}

.consultorio-modal .modal-title {
  font-weight: 600;
  color: #495057;
}

.consultorio-modal .modal-body {
  padding: 1.5rem;
}

.consultorio-modal .modal-footer {
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding: 1rem 1.5rem;
}

/* Form Controls */
.form-control, .form-select {
  border-radius: 10px;
  border: 2px solid #e9ecef;
  padding: 0.75rem 1rem;
  transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
}

.form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
}

/* Color Selector */
.color-selector {
  display: flex;
  flex-wrap: nowrap;
  gap: 0.4rem;
  padding: 0.5rem 0;
  align-items: center;
}

.color-option {
  width: 26px;
  height: 26px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
  position: relative;
  flex-shrink: 0;
}

.color-option:hover {
  transform: scale(1.25);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.25);
}

.color-option.selected {
  border-color: #ffffff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.4), 0 3px 8px rgba(0, 0, 0, 0.25);
  transform: scale(1.2);
}

.color-option i {
  color: #ffffff;
  font-size: 0.7rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  animation: checkFadeIn 0.3s ease;
}

@keyframes checkFadeIn {
  from {
    opacity: 0;
    transform: scale(0.5);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Preview do Consultório */
.consultorio-preview {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1rem;
}

.preview-card {
  background: white;
  border: 2px solid var(--preview-color, #007bff);
  border-radius: 12px;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
}

.preview-icon-container {
  width: 48px;
  height: 48px;
  background: 
  linear-gradient(135deg, rgba(0, 0, 0, 0.1), rgba(255,255,255, 0.2) 80%),
  var(--preview-color, #007bff);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.preview-info h6 {
  margin: 0 0 0.25rem 0;
  font-weight: 600;
  color: #495057;
}

.preview-info p {
  margin: 0;
  font-size: 0.875rem;
  color: #6c757d;
}

/* Custom Switch Styles */
.custom-switch-wrapper {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  position: relative;
}

.custom-switch-input {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.custom-switch-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  margin: 0;
  user-select: none;
}

.custom-switch-slider {
  position: relative;
  display: inline-block;
  width: 3rem;
  height: 1.5rem;
  background-color: #cbd5e0;
  border-radius: 1rem;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.custom-switch-slider::before {
  content: '';
  position: absolute;
  top: 0.125rem;
  left: 0.125rem;
  width: 1.25rem;
  height: 1.25rem;
  background-color: #ffffff;
  border-radius: 50%;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.custom-switch-input:checked + .custom-switch-label .custom-switch-slider {
  background-color: #007bff;
}

.custom-switch-input:checked + .custom-switch-label .custom-switch-slider::before {
  transform: translateX(1.5rem);
}

.custom-switch-input:focus + .custom-switch-label .custom-switch-slider {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
}

.custom-switch-text {
  font-weight: 600;
  color: #495057;
  font-size: 0.95rem;
}

/* Buttons */
.btn {
  border-radius: 8px;
  font-weight: 500;
  padding: 0.5rem 1.25rem;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border: none;
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

.btn-outline-secondary {
  border: 2px solid #6c757d;
  color: #6c757d;
}

.btn-outline-secondary:hover {
  background: #6c757d;
  color: white;
  transform: translateY(-1px);
}

/* Invalid Feedback */
.invalid-feedback {
  font-size: 0.875rem;
  font-weight: 500;
}

/* Responsive */
@media (max-width: 576px) {
  .consultorio-modal .modal-dialog {
    margin: 0.5rem;
  }
  
  .preview-card {
    flex-direction: column;
    text-align: center;
  }
}
</style>
