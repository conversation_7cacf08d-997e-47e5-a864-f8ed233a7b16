<template>
  <div class="global-layout-container">
    <!-- <PERSON><PERSON><PERSON>do principal da página -->
    <div class="global-layout-content">
      <slot />
    </div>

    <!-- Footer elegante -->
    <footer class="global-layout-footer" v-if="shouldShowFooter">
      <div class="footer-content">
        <img :src="LumiBlueLogo" class="footer-logo" alt="LUMI Vision" />
        <p class="footer-copyright">© {{ currentYear }} LUMI Vision</p>
      </div>
    </footer>
  </div>
</template>

<script>
import LumiBlueLogo from "@/assets/img/lumi/lumi-vision-logo-170.png";

export default {
  name: "GlobalLayout",
  data() {
    return {
      LumiBlueLogo,
      currentYear: new Date().getFullYear(),
    };
  },
  computed: {
    shouldShowFooter() {
      // Não mostrar footer na página de Início (ela já tem o próprio footer)
      return this.$route.name !== 'Inicio';
    }
  }
};
</script>

<style scoped>
.global-layout-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f8fbff 0%, #e8f4fd 100%);
}

/* Quando há tab navigation, ajustar altura - será controlado pelo CSS global */

.global-layout-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; /* Permite que o conteúdo se ajuste */
}

/* Footer elegante baseado no da tela de Início */
.global-layout-footer {
  margin-top: auto;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 251, 255, 0.95) 100%);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(44, 130, 201, 0.1);
  box-shadow: 0 -4px 20px rgba(33, 80, 115, 0.08);
  padding: 1rem 0;
  transition: all 0.3s ease;
}

.footer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.footer-logo {
  width: 70px;
  height: auto;
  opacity: 0.85;
  filter: grayscale(5%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.footer-logo:hover {
  opacity: 1;
  transform: scale(1.08);
  filter: grayscale(0%);
}

.footer-copyright {
  font-size: 0.75rem;
  color: #73848D;
  margin: 0;
  font-weight: 500;
  letter-spacing: 0.3px;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.footer-copyright:hover {
  opacity: 1;
}

/* Responsividade */
@media (max-width: 768px) {
  .global-layout-footer {
    padding: 0.75rem 0;
  }

  .footer-content {
    gap: 0.4rem;
  }

  .footer-logo {
    width: 60px;
  }

  .footer-copyright {
    font-size: 0.7rem;
  }
}

@media (max-width: 480px) {
  .global-layout-footer {
    padding: 0.6rem 0;
  }

  .footer-content {
    gap: 0.3rem;
  }

  .footer-logo {
    width: 50px;
  }

  .footer-copyright {
    font-size: 0.65rem;
    letter-spacing: 0.2px;
  }
}

/* Para telas com altura reduzida */
@media (max-height: 700px) {
  .global-layout-footer {
    padding: 0.5rem 0;
  }

  .footer-content {
    gap: 0;
  }

  /* Esconde o logo quando a altura é pequena */
  .footer-logo {
    display: none;
  }

  .footer-copyright {
    font-size: 0.7rem;
  }
}

@media (max-height: 600px) {
  .global-layout-footer {
    padding: 0.4rem 0;
  }

  .footer-copyright {
    font-size: 0.65rem;
  }
}
</style>
