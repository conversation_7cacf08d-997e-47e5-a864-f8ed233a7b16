<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Consultorio extends Model
{
    use HasFactory, LogsActivity;

    protected $table = 'consultorios';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'clinica_id',
        'nome',
        'descricao',
        'cor',
        'icone',
        'ativo',
        'ordem',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected function casts(): array
    {
        return [
            'ativo' => 'boolean',
            'ordem' => 'integer',
        ];
    }

    protected static function booted()
    {
        static::addGlobalScope(new \App\Scopes\ClinicaScope);
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['*']);
    }

    /**
     * Get the clinica that owns the consultorio.
     */
    public function clinica(): BelongsTo
    {
        return $this->belongsTo(Clinica::class);
    }

    /**
     * Get the agenda configurations for this consultorio.
     */
    public function agendaConfigs(): HasMany
    {
        return $this->hasMany(AgendaConfig::class);
    }

    /**
     * Get the active agenda configuration for this consultorio.
     */
    public function agendaConfig(): HasMany
    {
        return $this->hasMany(AgendaConfig::class)->where('ativo', true);
    }

    /**
     * Get consultas for this consultorio.
     */
    public function consultas(): HasMany
    {
        return $this->hasMany(Consulta::class);
    }

    /**
     * Scope to get only active consultorios.
     */
    public function scopeAtivo($query)
    {
        return $query->where('ativo', true);
    }

    /**
     * Scope to order by ordem field.
     */
    public function scopeOrdenado($query)
    {
        return $query->orderBy('ordem')->orderBy('nome');
    }

    /**
     * Create default consultorio for a clinica.
     */
    public static function createDefaultForClinica(int $clinicaId): self
    {
        return self::create([
            'clinica_id' => $clinicaId,
            'nome' => 'Consultório Principal',
            'descricao' => 'Consultório padrão da clínica',
            'cor' => '#007bff',
            'icone' => 'fas fa-tooth',
            'ativo' => true,
            'ordem' => 1,
        ]);
    }

    /**
     * Get or create default consultorio for a clinica.
     */
    public static function getDefaultForClinica(int $clinicaId): self
    {
        $consultorio = self::where('clinica_id', $clinicaId)
            ->ativo()
            ->ordenado()
            ->first();

        if (!$consultorio) {
            $consultorio = self::createDefaultForClinica($clinicaId);
        }

        return $consultorio;
    }

    /**
     * Format for frontend consumption.
     */
    public function toFrontendFormat(): array
    {
        return [
            'id' => $this->id,
            'clinica_id' => $this->clinica_id,
            'nome' => $this->nome,
            'descricao' => $this->descricao,
            'cor' => $this->cor,
            'icone' => $this->icone,
            'ativo' => $this->ativo,
            'ordem' => $this->ordem,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
