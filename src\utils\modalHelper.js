/**
 * Helper para manipulação de modais Bootstrap sem importar diretamente o Bootstrap JS
 * Isso evita conflitos com outros componentes que já estão usando Bootstrap
 */

/**
 * Abre um modal programaticamente usando a API do DOM
 * @param {string} modalId - ID do modal a ser aberto (com ou sem #)
 * @param {Object} options - Opções adicionais
 * @param {boolean} options.backdrop - Se o backdrop deve ser mostrado (default: true)
 * @param {boolean} options.keyboard - Se o modal pode ser fechado com ESC (default: true)
 * @param {boolean} options.focus - Se o foco deve ser colocado no modal (default: true)
 */
export function openModal(modalId, options = {}) {
  // Garantir que o ID comece com #
  const id = modalId.startsWith('#') ? modalId : `#${modalId}`;

  // Verificar se o modal existe
  const modalElement = document.querySelector(id);
  if (!modalElement) {
    console.error(`Modal com ID ${id} não encontrado`);
    return;
  }

  // Limpar qualquer backdrop órfão antes de abrir
  cleanupOrphanBackdrops();

  // Verificar se já existe um modal aberto e fechá-lo primeiro
  const openModals = document.querySelectorAll('.modal.show');
  openModals.forEach(modal => {
    if (modal !== modalElement) {
      // Usar Bootstrap API se disponível
      if (window.bootstrap && window.bootstrap.Modal) {
        const modalInstance = window.bootstrap.Modal.getInstance(modal);
        if (modalInstance) {
          modalInstance.hide();
        }
      } else {
        const closeButton = modal.querySelector('[data-bs-dismiss="modal"]');
        if (closeButton) {
          closeButton.click();
        }
      }
    }
  });

  // Aguardar um pouco para garantir que o modal anterior foi fechado
  setTimeout(() => {
    // Tentar usar Bootstrap API primeiro
    if (window.bootstrap && window.bootstrap.Modal) {
      try {
        // Destruir instância existente se houver
        const existingInstance = window.bootstrap.Modal.getInstance(modalElement);
        if (existingInstance) {
          existingInstance.dispose();
        }

        // Criar nova instância
        const modalInstance = new window.bootstrap.Modal(modalElement, {
          backdrop: options.backdrop !== false ? 'static' : false,
          keyboard: options.keyboard !== false,
          focus: options.focus !== false
        });

        modalInstance.show();
        return;
      } catch (error) {
        console.warn('Erro ao usar Bootstrap API, usando fallback:', error);
      }
    }

    // Fallback: usar método de botão temporário
    const openButton = document.createElement('button');
    openButton.setAttribute('data-bs-toggle', 'modal');
    openButton.setAttribute('data-bs-target', id);

    // Adicionar opções adicionais se fornecidas
    if (options.backdrop === false) {
      openButton.setAttribute('data-bs-backdrop', 'false');
    }
    if (options.keyboard === false) {
      openButton.setAttribute('data-bs-keyboard', 'false');
    }
    if (options.focus === false) {
      openButton.setAttribute('data-bs-focus', 'false');
    }

    // Adicionar o botão ao DOM, clicar nele e depois removê-lo
    document.body.appendChild(openButton);
    openButton.click();
    document.body.removeChild(openButton);
  }, 150);
}

/**
 * Fecha um modal programaticamente usando a API do DOM
 * @param {string} modalId - ID do modal a ser fechado (com ou sem #)
 */
export function closeModal(modalId) {
  // Garantir que o ID comece com #
  const id = modalId.startsWith('#') ? modalId : `#${modalId}`;

  // Verificar se o modal existe
  const modalElement = document.querySelector(id);
  if (!modalElement) {
    console.error(`Modal com ID ${id} não encontrado`);
    return;
  }

  // Tentar usar Bootstrap API primeiro
  if (window.bootstrap && window.bootstrap.Modal) {
    try {
      const modalInstance = window.bootstrap.Modal.getInstance(modalElement);
      if (modalInstance) {
        modalInstance.hide();
        return;
      }
    } catch (error) {
      console.warn('Erro ao usar Bootstrap API para fechar, usando fallback:', error);
    }
  }

  // Fallback: procurar pelo botão de fechar dentro do modal e clicar nele
  const closeButton = modalElement.querySelector('[data-bs-dismiss="modal"]');
  if (closeButton) {
    closeButton.click();
  } else {
    // Se não encontrar o botão, criar um temporário
    const closeBtn = document.createElement('button');
    closeBtn.setAttribute('data-bs-dismiss', 'modal');
    closeBtn.setAttribute('data-bs-target', id);
    document.body.appendChild(closeBtn);
    closeBtn.click();
    document.body.removeChild(closeBtn);
  }

  // Limpar backdrops órfãos após fechar
  setTimeout(() => {
    cleanupOrphanBackdrops();
  }, 300);
}

/**
 * Adiciona uma classe de animação ao modal e depois o fecha
 * @param {string} modalId - ID do modal a ser fechado (com ou sem #)
 * @param {string} animationClass - Classe CSS para animação (default: 'modal-closing')
 * @param {number} delay - Atraso em ms antes de fechar o modal (default: 200)
 */
export function closeModalWithAnimation(modalId, animationClass = 'modal-closing', delay = 200) {
  // Garantir que o ID comece com #
  const id = modalId.startsWith('#') ? modalId : `#${modalId}`;

  // Verificar se o modal existe
  const modalElement = document.querySelector(id);
  if (!modalElement) {
    console.error(`Modal com ID ${id} não encontrado`);
    return;
  }

  // Adicionar classe de animação
  modalElement.classList.add(animationClass);

  // Fechar o modal após o atraso
  setTimeout(() => {
    closeModal(id);
  }, delay);
}

/**
 * Remove backdrops órfãos que podem ter ficado na tela
 */
export function cleanupOrphanBackdrops() {
  // Verificar se há modais abertos
  const openModals = document.querySelectorAll('.modal.show');

  // Se não há modais abertos, remover todos os backdrops
  if (openModals.length === 0) {
    const backdrops = document.querySelectorAll('.modal-backdrop');
    backdrops.forEach(backdrop => {
      backdrop.remove();
    });

    // Remover classe modal-open do body se não há modais
    document.body.classList.remove('modal-open');
  }
}

export default {
  openModal,
  closeModal,
  closeModalWithAnimation,
  cleanupOrphanBackdrops
};
