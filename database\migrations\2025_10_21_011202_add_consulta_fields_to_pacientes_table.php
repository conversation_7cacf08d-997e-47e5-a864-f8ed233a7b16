<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pacientes', function (Blueprint $table) {
            // Verificar se as colunas já existem antes de adicionar
            if (!Schema::hasColumn('pacientes', 'ultima_consulta')) {
                $table->timestamp('ultima_consulta')->nullable()->after('proxima_consulta');
            }
            if (!Schema::hasColumn('pacientes', 'proxima_consulta')) {
                $table->timestamp('proxima_consulta')->nullable()->after('primeira_consulta');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pacientes', function (Blueprint $table) {
            if (Schema::hasColumn('pacientes', 'ultima_consulta')) {
                $table->dropColumn('ultima_consulta');
            }
            if (Schema::hasColumn('pacientes', 'proxima_consulta')) {
                $table->dropColumn('proxima_consulta');
            }
        });
    }
};
