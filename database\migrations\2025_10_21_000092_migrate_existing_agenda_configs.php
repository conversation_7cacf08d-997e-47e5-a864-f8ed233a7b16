<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // <PERSON>iro, adicionar as colunas necessárias se não existirem
        if (!Schema::hasColumn('agenda_configs', 'consultorio_id')) {
            Schema::table('agenda_configs', function (Blueprint $table) {
                $table->unsignedBigInteger('consultorio_id')->nullable()->after('user_id');
            });
        }

        if (!Schema::hasColumn('agenda_configs', 'ativo')) {
            Schema::table('agenda_configs', function (Blueprint $table) {
                $table->boolean('ativo')->default(true)->after('antecedencia_maxima_agendamento');
            });
        }

        // Segundo, criar consultórios padrão para todas as clínicas que têm usuários com configurações
        $clinicasComConfigs = DB::table('agenda_configs')
            ->join('users', 'agenda_configs.user_id', '=', 'users.id')
            ->select('users.clinica_id')
            ->distinct()
            ->get();

        foreach ($clinicasComConfigs as $clinica) {
            // Verificar se a clínica já tem consultório
            $consultorioExistente = DB::table('consultorios')
                ->where('clinica_id', $clinica->clinica_id)
                ->first();

            if (!$consultorioExistente) {
                // Criar consultório padrão para a clínica
                $consultorioId = DB::table('consultorios')->insertGetId([
                    'clinica_id' => $clinica->clinica_id,
                    'nome' => 'Consultório Principal',
                    'descricao' => 'Consultório padrão migrado automaticamente',
                    'cor' => '#007bff',
                    'icone' => 'fas fa-tooth',
                    'ativo' => true,
                    'ordem' => 1,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                // Migrar todas as configurações de usuários desta clínica para o consultório
                $configsParaMigrar = DB::table('agenda_configs')
                    ->join('users', 'agenda_configs.user_id', '=', 'users.id')
                    ->where('users.clinica_id', $clinica->clinica_id)
                    ->select('agenda_configs.*')
                    ->get();

                if (Schema::hasColumn('agenda_configs', 'user_id')) {
                    Schema::table('agenda_configs', function (Blueprint $table) {
                        $table->unsignedBigInteger('user_id')->nullable()->change();
                    });
                }

                foreach ($configsParaMigrar as $config) {
                    // Criar nova configuração para o consultório baseada na configuração do usuário
                    DB::table('agenda_configs')->insert([
                        'user_id' => null, // Não vinculado a usuário específico
                        'consultorio_id' => $consultorioId,
                        'horario_inicio' => $config->horario_inicio,
                        'horario_fim' => $config->horario_fim,
                        'dias_semana' => $config->dias_semana,
                        'duracao_padrao_consulta' => $config->duracao_padrao_consulta,
                        'permitir_duracao_personalizada' => $config->permitir_duracao_personalizada,
                        'intervalo_entre_consultas' => $config->intervalo_entre_consultas,
                        'horario_almoco_inicio' => $config->horario_almoco_inicio,
                        'horario_almoco_fim' => $config->horario_almoco_fim,
                        'tem_horario_almoco' => $config->tem_horario_almoco,
                        'permitir_agendamento_passado' => $config->permitir_agendamento_passado,
                        'permitir_agendamento_feriados' => $config->permitir_agendamento_feriados,
                        'antecedencia_minima_agendamento' => $config->antecedencia_minima_agendamento,
                        'antecedencia_maxima_agendamento' => $config->antecedencia_maxima_agendamento,
                        'ativo' => true,
                        'created_at' => $config->created_at,
                        'updated_at' => now(),
                    ]);

                    // Quebrar após a primeira configuração (usar apenas uma como base)
                    break;
                }
            }
        }

        // Agora, desativar todas as configurações antigas baseadas em usuário
        DB::table('agenda_configs')
            ->whereNotNull('user_id')
            ->whereNull('consultorio_id')
            ->update([
                'ativo' => false,
                'updated_at' => now()
            ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reativar configurações baseadas em usuário
        DB::table('agenda_configs')
            ->whereNotNull('user_id')
            ->whereNull('consultorio_id')
            ->update([
                'ativo' => true,
                'updated_at' => now()
            ]);

        // Remover configurações baseadas em consultório criadas na migração
        DB::table('agenda_configs')
            ->whereNotNull('consultorio_id')
            ->delete();

        // Remover consultórios criados automaticamente
        DB::table('consultorios')
            ->where('nome', 'Consultório Principal')
            ->where('descricao', 'Consultório padrão migrado automaticamente')
            ->delete();
    }
};
