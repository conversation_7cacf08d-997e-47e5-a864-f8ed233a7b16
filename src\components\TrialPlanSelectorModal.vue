<template>
  <Teleport to="body">
    <div class="modal fade lumi-fade" :id="modalId" tabindex="-1" aria-hidden="true">
      <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
        <div class="modal-header border-0 pb-0">
          <div class="modal-title-container text-center">
            <h5 class="modal-title">
              <!-- <i class="fas fa-rocket text-success me-2"></i> -->
              <b>Teste Gratuito - Escolha seu Plano</b>
            </h5>
            <p class="modal-subtitle mb-0">
              Selecione o plano que deseja testar gratuitamente
            </p>
          </div>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>

        <div class="modal-body">
          <!-- Loading -->
          <div v-if="isLoading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Carregando...</span>
            </div>
            <p class="mt-2 text-muted">Carregando planos disponíveis...</p>
          </div>

          <!-- Mensagem se não houver planos trial -->
          <div v-if="!isLoading && planosTrial.length === 0" class="text-center py-4">
            <i class="fas fa-info-circle text-muted mb-3" style="font-size: 3rem;"></i>
            <p class="text-muted">Nenhum plano de teste disponível no momento.</p>
          </div>

          <!-- Lista de Planos Trial -->
          <div v-if="!isLoading && planosTrial.length > 0" class="available-plans-section">
            <div class="plans-sections-container">

              <!-- Seção: Planos Clínica -->
              <div v-if="planosClinica.length > 0" class="plan-section">
                <h6 class="section-header">
                  <i class="fas fa-clinic-medical me-2 text-success"></i>
                  Planos Clínica
                </h6>
                <div class="plans-list">
                  <div
                    v-for="plano in planosClinica"
                    :key="plano.id"
                    class="plan-item"
                    :class="{ 'plan-item--selected': selectedPlano?.id === plano.id }"
                    @click="selectPlano(plano)"
                  >
                    <!-- Indicador de cor do plano -->
                    <div class="plan-color-bar" :style="{ backgroundColor: plano.cor }"></div>

                    <!-- Conteúdo do plano -->
                    <div class="plan-content">
                      <!-- Nome e badge de trial -->
                      <div class="plan-header-row">
                        <h6 class="plan-name mb-0">{{ plano.nome }}</h6>
                      </div>

                      <!-- Badge de trial (posicionada absolutamente) -->
                      <span class="trial-badge-compact">
                        <i class="fas fa-gift me-1"></i>
                        {{ plano.dias_gratuitos || 30 }} dias grátis
                      </span>

                      <!-- Preço e features em linha -->
                      <div class="plan-details-row">
                        <div class="plan-price-compact">
                          {{ formatCurrency(plano.valor_mensal) }}<span class="price-period-compact">/mês</span>
                        </div>

                        <div class="plan-features-compact">
                          <span v-if="plano.quantidade_usuarios" class="feature-badge">
                            <i class="fas fa-users"></i>
                            <span class="feature-value">{{ formatQuantity(plano.quantidade_usuarios) }}</span>
                            <span class="feature-label">Usuários</span>
                          </span>
                          <span v-if="plano.quantidade_agendas" class="feature-badge">
                            <i class="fas fa-calendar"></i>
                            <span class="feature-value">{{ formatQuantity(plano.quantidade_agendas) }}</span>
                            <span class="feature-label">Consultórios</span>
                          </span>
                          <span v-if="plano.quantidade_mentorias_mensais" class="feature-badge">
                            <i class="fas fa-graduation-cap"></i>
                            <span class="feature-value">{{ formatQuantity(plano.quantidade_mentorias_mensais) }}</span>
                            <span class="feature-label">Mentoria{{ plano.quantidade_mentorias_mensais && plano.quantidade_mentorias_mensais > 1 ? 's' : '' }}/mês</span>
                          </span>
                        </div>
                      </div>
                    </div>

                    <!-- Indicador de seleção -->
                    <div class="selection-check">
                      <i class="fas fa-check-circle"></i>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Seção: Planos com Ortodontia -->
              <div v-if="planosOrtodontia.length > 0" class="plan-section">
                <h6 class="section-header">
                  <i class="fas fa-tooth me-2"></i>
                  Planos com Ortodontia
                </h6>
                <div class="plans-list">
                  <div
                    v-for="plano in planosOrtodontia"
                    :key="plano.id"
                    class="plan-item"
                    :class="{ 'plan-item--selected': selectedPlano?.id === plano.id }"
                    @click="selectPlano(plano)"
                  >
                    <!-- Indicador de cor do plano -->
                    <div class="plan-color-bar" :style="{ backgroundColor: plano.cor }"></div>

                    <!-- Conteúdo do plano -->
                    <div class="plan-content">
                      <!-- Nome e badge de trial -->
                      <div class="plan-header-row">
                        <h6 class="plan-name mb-0">{{ plano.nome }}</h6>
                      </div>

                      <!-- Badge de trial (posicionada absolutamente) -->
                      <span class="trial-badge-compact">
                        <i class="fas fa-gift me-1"></i>
                        {{ plano.dias_gratuitos || 30 }} dias grátis
                      </span>

                      <!-- Preço e features em linha -->
                      <div class="plan-details-row">
                        <div class="plan-price-compact">
                          {{ formatCurrency(plano.valor_mensal) }}<span class="price-period-compact">/mês</span>
                        </div>

                        <div class="plan-features-compact">
                          <span v-if="plano.quantidade_usuarios" class="feature-badge">
                            <i class="fas fa-users"></i>
                            <span class="feature-value">{{ formatQuantity(plano.quantidade_usuarios) }}</span>
                            <span class="feature-label">Usuários</span>
                          </span>
                          <span v-if="plano.quantidade_agendas" class="feature-badge">
                            <i class="fas fa-calendar"></i>
                            <span class="feature-value">{{ formatQuantity(plano.quantidade_agendas) }}</span>
                            <span class="feature-label">Consultórios</span>
                          </span>
                          <span class="feature-badge feature-badge--highlight">
                            <i class="fas fa-tooth"></i> Ortodontia
                          </span>
                          <span v-if="plano.quantidade_mentorias_mensais" class="feature-badge">
                            <i class="fas fa-graduation-cap"></i>
                            <span class="feature-value">{{ formatQuantity(plano.quantidade_mentorias_mensais) }}</span>
                            <span class="feature-label">Mentoria{{ plano.quantidade_mentorias_mensais && plano.quantidade_mentorias_mensais > 1 ? 's' : '' }}/mês</span>
                          </span>
                        </div>
                      </div>
                    </div>

                    <!-- Indicador de seleção -->
                    <div class="selection-check">
                      <i class="fas fa-check-circle"></i>
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>

        <div class="modal-footer border-0 pt-0">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            <i class="fas fa-times me-2"></i>
            Cancelar
          </button>
          <button 
            type="button" 
            class="btn btn-success"
            :disabled="!selectedPlano"
            @click="confirmarPlanoSelecionado"
          >
            <i class="fas fa-arrow-right me-2"></i>
            Continuar
          </button>
        </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script>
import { planosService } from '@/services/planosService';
import cSwal from '@/utils/cSwal';

export default {
  name: 'TrialPlanSelectorModal',
  props: {
    modalId: {
      type: String,
      default: 'trialPlanSelectorModal'
    }
  },
  emits: ['plano-selecionado'],
  data() {
    return {
      isLoading: false,
      planos: [],
      selectedPlano: null,
    };
  },
  computed: {
    planosTrial() {
      if (!Array.isArray(this.planos)) {
        return [];
      }
      // Filtrar apenas planos ativos e com auto_registro habilitado
      return this.planos.filter(plano => plano.ativo && plano.auto_registro);
    },

    planosClinica() {
      return this.planosTrial.filter(plano => !plano.modulo_ortodontia);
    },

    planosOrtodontia() {
      return this.planosTrial.filter(plano => plano.modulo_ortodontia);
    }
  },
  mounted() {
    // Carregar planos quando o modal for montado
    const modalElement = document.getElementById(this.modalId);
    if (modalElement) {
      modalElement.addEventListener('shown.bs.modal', () => {
        if (this.planos.length === 0) {
          this.loadPlanosTrial();
        }
      });
    }
  },
  methods: {
    async loadPlanosTrial() {
      this.isLoading = true;
      try {
        const response = await planosService.getPlanos({ 
          ativo: true
        });
        this.planos = Array.isArray(response) ? response : (response?.data || []);
      } catch (error) {
        console.error('Erro ao carregar planos trial:', error);
        this.planos = [];
        cSwal.cError('Erro ao carregar planos disponíveis.');
      }
      this.isLoading = false;
    },
    
    selectPlano(plano) {
      this.selectedPlano = plano;
    },
    
    confirmarPlanoSelecionado() {
      if (this.selectedPlano) {
        this.$emit('plano-selecionado', this.selectedPlano);
      }
    },
    
    getPlanoIcon(plano) {
      if (plano.modulo_ortodontia) return 'fas fa-tooth';
      return 'fas fa-hospital-building';
    },
    
    formatCurrency(value) {
      return planosService.formatCurrency(value);
    },
    
    formatQuantity(value) {
      return planosService.formatQuantity(value);
    }
  }
};
</script>

<style scoped>
.modal-title-container {
  flex: 1;
}

.modal-subtitle {
  font-size: 0.875rem;
  color: #6c757d;
  font-weight: normal;
}

/* Container das seções (horizontal) */
.plans-sections-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

/* Seção individual */
.plan-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.section-header {
  font-size: 0.95rem;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e9ecef;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.5rem;
}

.section-header i {
  flex-shrink: 0;
  font-size: 0.9rem;
}

/* Layout de lista compacta */
.plans-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.plan-item {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.25s ease;
  background: white;
  padding: 0;
}

.plan-item:hover {
  border-color: #007bff;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
  transform: translateX(3px);
}

.plan-item--selected {
  border-color: #007bff;
  background: linear-gradient(to right, rgba(0, 123, 255, 0.03), white);
  box-shadow: 0 4px 16px rgba(0, 123, 255, 0.2);
  transform: translateX(3px);
}

/* Barra de cor lateral */
.plan-color-bar {
  width: 5px;
  height: 100%;
  flex-shrink: 0;
}

/* Conteúdo do plano */
.plan-content {
  position: relative;
  flex: 1;
  padding: 1rem 1.25rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* Header row: nome */
.plan-header-row {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding-right: 8rem;
}

.plan-name {
  font-size: 1.125rem;
  font-weight: 700;
  color: #2d3748;
  flex: 1 1 auto;
  min-width: 0;
}

.trial-badge-compact {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.7rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  white-space: nowrap;
  box-shadow: 0 2px 6px rgba(40, 167, 69, 0.3);
}

/* Details row: preço + features */
.plan-details-row {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.plan-price-compact {
  font-size: 1.25rem;
  font-weight: 700;
  color: #007bff;
}

.price-period-compact {
  font-size: 0.75rem;
  font-weight: 400;
  color: #718096;
  margin-left: 0.25rem;
}

.plan-features-compact {
  display: flex;
  align-items: center;
  gap: 0.625rem;
  flex-wrap: wrap;
}

.feature-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.3rem;
  padding: 0.25rem 0.625rem;
  background: #f8f9fa;
  border-radius: 12px;
  font-size: 0.75rem;
  color: #4a5568;
  font-weight: 500;
}

.feature-badge i {
  font-size: 0.7rem;
  color: #718096;
  flex-shrink: 0;
}

.feature-value {
  font-weight: 700;
  color: #2d3748;
}

.feature-label {
  font-size: 0.65rem;
  color: #718096;
  font-weight: 400;
  text-transform: lowercase;
}

.feature-badge--highlight {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  font-weight: 600;
}

.feature-badge--highlight i {
  color: white;
}

/* Indicador de seleção */
.selection-check {
  padding: 0 1.25rem;
  color: #007bff;
  font-size: 1.5rem;
  opacity: 0;
  transition: opacity 0.25s ease;
}

.plan-item--selected .selection-check {
  opacity: 1;
}

.btn {
  border-radius: 8px;
  font-weight: 500;
  padding: 0.625rem 1.25rem;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Responsividade */
@media (max-width: 992px) {
  .plans-sections-container {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .plan-header-row {
    padding-right: 0;
    padding-top: 2rem;
  }

  .plan-name {
    flex: 1 1 100%;
    font-size: 1rem;
  }

  .trial-badge-compact {
    top: 0.5rem;
    right: 0.5rem;
    font-size: 0.65rem;
    padding: 0.2rem 0.6rem;
  }

  .plan-details-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .plan-features-compact {
    flex-wrap: wrap;
  }

  .selection-check {
    padding: 0 0.75rem;
  }
}
</style>
