import axios from '@/services/axios';

/**
 * Service para gerenciar consultórios
 */

/**
 * Obter todos os consultórios da clínica do usuário
 */
async function getConsultorios() {
    try {
        const response = await axios.get('/consultorios');
        
        if (response.data && response.data.status === 'success') {
            return response.data.data;
        }
        
        return [];
    } catch (error) {
        console.error('Erro ao obter consultórios:', error);
        throw error;
    }
}

/**
 * Obter um consultório específico
 */
async function getConsultorio(id) {
    try {
        const response = await axios.get(`/consultorios/${id}`);
        
        if (response.data && response.data.status === 'success') {
            return response.data.data;
        }
        
        return null;
    } catch (error) {
        console.error('Erro ao obter consultório:', error);
        throw error;
    }
}

/**
 * Criar um novo consultório
 */
async function createConsultorio(consultorioData) {
    try {
        const response = await axios.post('/consultorios', consultorioData);
        
        if (response.data && response.data.status === 'success') {
            return response.data.data;
        }
        
        return null;
    } catch (error) {
        console.error('Erro ao criar consultório:', error);
        throw error;
    }
}

/**
 * Atualizar um consultório
 */
async function updateConsultorio(id, consultorioData) {
    try {
        const response = await axios.put(`/consultorios/${id}`, consultorioData);
        
        if (response.data && response.data.status === 'success') {
            return response.data.data;
        }
        
        return null;
    } catch (error) {
        console.error('Erro ao atualizar consultório:', error);
        throw error;
    }
}

/**
 * Remover um consultório
 */
async function deleteConsultorio(id) {
    try {
        const response = await axios.delete(`/consultorios/${id}`);
        
        if (response.data && response.data.status === 'success') {
            return true;
        }
        
        return false;
    } catch (error) {
        console.error('Erro ao remover consultório:', error);
        throw error;
    }
}

/**
 * Reordenar consultórios
 */
async function reorderConsultorios(consultorios) {
    try {
        const response = await axios.post('/consultorios/reorder', {
            consultorios: consultorios.map((consultorio, index) => ({
                id: consultorio.id,
                ordem: index + 1
            }))
        });
        
        if (response.data && response.data.status === 'success') {
            return true;
        }
        
        return false;
    } catch (error) {
        console.error('Erro ao reordenar consultórios:', error);
        throw error;
    }
}

/**
 * Validar dados do consultório
 */
function validateConsultorio(consultorioData) {
    const errors = {};
    
    if (!consultorioData.nome || !consultorioData.nome.trim()) {
        errors.nome = 'Nome é obrigatório';
    } else if (consultorioData.nome.length > 255) {
        errors.nome = 'Nome deve ter no máximo 255 caracteres';
    }
    
    if (consultorioData.descricao && consultorioData.descricao.length > 500) {
        errors.descricao = 'Descrição deve ter no máximo 500 caracteres';
    }
    
    if (!consultorioData.cor) {
        errors.cor = 'Cor é obrigatória';
    } else if (!/^#[0-9A-Fa-f]{6}$/.test(consultorioData.cor)) {
        errors.cor = 'Cor deve estar no formato hexadecimal (#RRGGBB)';
    }
    
    if (!consultorioData.icone) {
        errors.icone = 'Ícone é obrigatório';
    }
    
    return {
        isValid: Object.keys(errors).length === 0,
        errors
    };
}

/**
 * Gerar cor aleatória para novo consultório
 */
function generateRandomColor() {
    const colors = [
        '#007bff', // Azul
        '#28a745', // Verde
        '#dc3545', // Vermelho
        '#ffc107', // Amarelo
        '#17a2b8', // Ciano
        '#6f42c1', // Roxo
        '#e83e8c', // Rosa
        '#fd7e14', // Laranja
        '#20c997', // Verde água
        '#6c757d', // Cinza
        '#343a40', // Preto
        '#495057'  // Cinza escuro
    ];
    
    return colors[Math.floor(Math.random() * colors.length)];
}

/**
 * Obter ícones disponíveis
 */
function getAvailableIcons() {
    return [
        { value: 'fas fa-tooth', label: '🦷 Dente' },
        { value: 'fas fa-clinic-medical', label: '🏥 Clínica' },
        { value: 'fas fa-user-md', label: '👨‍⚕️ Médico' },
        { value: 'fas fa-stethoscope', label: '🩺 Estetoscópio' },
        { value: 'fas fa-heartbeat', label: '💓 Batimento' },
        { value: 'fas fa-syringe', label: '💉 Seringa' },
        { value: 'fas fa-pills', label: '💊 Medicamento' },
        { value: 'fas fa-microscope', label: '🔬 Microscópio' },
        { value: 'fas fa-x-ray', label: '🩻 Raio-X' },
        { value: 'fas fa-chair', label: '🪑 Cadeira' },
        { value: 'fas fa-bed', label: '🛏️ Maca' },
        { value: 'fas fa-door-open', label: '🚪 Sala' }
    ];
}

/**
 * Converter cor hex para RGB
 */
function hexToRgb(hex) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
    } : null;
}

/**
 * Verificar se uma cor é clara ou escura
 */
function isLightColor(hex) {
    const rgb = hexToRgb(hex);
    if (!rgb) return false;
    
    // Calcular luminância
    const luminance = (0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b) / 255;
    return luminance > 0.5;
}

export default {
    getConsultorios,
    getConsultorio,
    createConsultorio,
    updateConsultorio,
    deleteConsultorio,
    reorderConsultorios,
    validateConsultorio,
    generateRandomColor,
    getAvailableIcons,
    hexToRgb,
    isLightColor
};
