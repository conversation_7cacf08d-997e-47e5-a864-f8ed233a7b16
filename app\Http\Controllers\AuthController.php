<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Auth;
use App\Models\Clinica;
use App\Models\Dentista;
use App\Models\User;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class AuthController extends Controller
{
    /**
     * Create a new AuthController instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth:api', ['except' => ['login', 'refresh', 'logout']]);
    }

    /**
     * Get a JWT via given credentials.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function login()
    {
        // Verificar se o usuário está tentando fazer login com email ou username
        $loginField = request('username');
        $credentials = ['password' => request('password')];

        // Determinar se o campo de login é um email ou username
        if (filter_var($loginField, FILTER_VALIDATE_EMAIL)) {
            // Se for um email, tentamos encontrar o usuário pelo email
            $user = User::where('email', $loginField)->first();
            if ($user) {
                $credentials['username'] = $user->username;
            }
        } else {
            // Se não for um email, assumimos que é um username
            $credentials['username'] = $loginField;
        }

        if (! $token = auth()->attempt($credentials)) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $user = auth()->getProvider()->retrieveByCredentials($credentials);

        // Atualizar last_login
        $user->update(['last_login' => now()]);

        // Generate token with claims
        $token = $this->generateTokenWithClaims($user);

        return $this->respondWithToken($token);
    }

    /**
     * Get the authenticated User.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function me()
    {
        return response()->json(auth()->user());
    }

    /**
     * Log the user out (Invalidate the token).
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function logout(Request $request)
    {
        auth()->invalidate(true);
        auth()->logout();

        return response()->json(['status' => 'success']);
    }

    /**
     * Refresh a token.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function refresh()
    {
        try {
            // Get the current user ID from the token BEFORE refreshing
            // Use payload() to get the token data without triggering blacklist check
            $payload = auth()->payload();
            $userId = $payload->get('sub');

            // Refresh the token (this will blacklist the old one)
            $newToken = auth()->refresh(true, true);

            // Get the user with fresh data
            $user = User::find($userId);

            // Generate a new token with updated claims
            $token = $this->generateTokenWithClaims($user);

            return $this->respondWithToken($token);
        } catch (\Tymon\JWTAuth\Exceptions\TokenBlacklistedException $e) {
            return response()->json(['error' => 'Token inválido ou já utilizado'], 401);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Erro ao atualizar token'], 500);
        }
    }

    /**
     * Get the token array structure.
     *
     * @param  string $token
     *
     * @return \Illuminate\Http\JsonResponse
     */
    protected function respondWithToken($token)
    {
        return response()->json([
            'access_token' => $token,
            'token_type' => 'bearer',
            'expires_in' => auth()->factory()->getTTL() * 60
        ]);
    }

    /**
     * Generate a JWT token with custom claims for a user
     *
     * @param User $user
     * @return string
     */
    private function generateTokenWithClaims($user)
    {
        $clinica = Clinica::find($user->clinica_id);

        $claims = [
            'nome' => $user->name,
            'username' => $user->username,
            'email' => $user->email,
            'clinica' => $clinica,
            'system_admin' => $user->system_admin,
            'clinica_admin' => $user->clinica_admin,
            'language' => $user->language,
            'storageRoot' => 'clinicas/' . $clinica->id
        ];

        $dentista = Dentista::where('user_id', $user->id)->first();

        if ($dentista) {
            $claims['dentista'] = $dentista;
        }

        // Adicionar configurações da agenda
        $agendaConfig = \App\Models\AgendaConfig::getForUser($user->id);
        $claims['agenda_config'] = $agendaConfig->toFrontendFormat();

        return auth()->claims($claims)->tokenById($user->getAuthIdentifier());
    }
}
