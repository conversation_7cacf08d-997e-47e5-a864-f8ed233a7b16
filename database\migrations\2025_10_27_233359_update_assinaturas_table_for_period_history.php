<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Verificar se o índice de status existe
        $statusIndexExists = collect(DB::select("SHOW INDEX FROM assinaturas WHERE Key_name = 'assinaturas_status_index'"))->isNotEmpty();

        // Verificar se a constraint única existe
        $uniqueConstraintExists = collect(DB::select("SHOW INDEX FROM assinaturas WHERE Key_name = 'assinaturas_clinica_id_data_inicio_unique'"))->isNotEmpty();

        Schema::table('assinaturas', function (Blueprint $table) use ($statusIndexExists, $uniqueConstraintExists) {
            // Adicionar índice de status se não existir
            if (!$statusIndexExists) {
                $table->index('status');
            }

            // Adicionar constraint única se não existir
            if (!$uniqueConstraintExists) {
                $table->unique(['clinica_id', 'data_inicio']);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('assinaturas', function (Blueprint $table) {
            // Remover apenas os índices que foram adicionados
            try {
                $table->dropIndex(['status']);
            } catch (\Exception $e) {
                // Índice não existe, ignorar
            }

            try {
                $table->dropUnique(['clinica_id', 'data_inicio']);
            } catch (\Exception $e) {
                // Constraint não existe, ignorar
            }
        });
    }
};
