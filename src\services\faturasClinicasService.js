import axios from '@/services/axios';

export const faturasClinicasService = {
  /**
   * Obter todas as faturas de uma clínica
   */
  async getFaturas(clinicaId, filters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.status) params.append('status', filters.status);
      if (filters.mes) params.append('mes', filters.mes);
      if (filters.ano) params.append('ano', filters.ano);
      if (filters.data_inicio) params.append('data_inicio', filters.data_inicio);
      if (filters.data_fim) params.append('data_fim', filters.data_fim);
      if (filters.per_page) params.append('per_page', filters.per_page);

      const response = await axios.get(`/clinicas/${clinicaId}/faturas?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar faturas:', error);
      throw error;
    }
  },

  /**
   * Obter fatura específica
   */
  async getFatura(clinicaId, faturaId) {
    try {
      const response = await axios.get(`/clinicas/${clinicaId}/faturas/${faturaId}`);
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar fatura:', error);
      throw error;
    }
  },

  /**
   * Criar nova fatura
   */
  async createFatura(clinicaId, data) {
    try {
      const response = await axios.post(`/clinicas/${clinicaId}/faturas`, data);
      return response.data;
    } catch (error) {
      console.error('Erro ao criar fatura:', error);
      throw error;
    }
  },

  /**
   * Atualizar fatura
   */
  async updateFatura(clinicaId, faturaId, data) {
    try {
      const response = await axios.put(`/clinicas/${clinicaId}/faturas/${faturaId}`, data);
      return response.data;
    } catch (error) {
      console.error('Erro ao atualizar fatura:', error);
      throw error;
    }
  },

  /**
   * Excluir fatura
   */
  async deleteFatura(clinicaId, faturaId) {
    try {
      const response = await axios.delete(`/clinicas/${clinicaId}/faturas/${faturaId}`);
      return response.data;
    } catch (error) {
      console.error('Erro ao excluir fatura:', error);
      throw error;
    }
  },

  /**
   * Marcar fatura como paga
   */
  async pagarFatura(clinicaId, faturaId, formaPagamento, dataPagamento = null, observacoes = null) {
    try {
      const data = {
        forma_pagamento: formaPagamento
      };
      
      if (dataPagamento) data.data_pagamento = dataPagamento;
      if (observacoes) data.observacoes = observacoes;

      const response = await axios.post(`/clinicas/${clinicaId}/faturas/${faturaId}/pagar`, data);
      return response.data;
    } catch (error) {
      console.error('Erro ao pagar fatura:', error);
      throw error;
    }
  },

  /**
   * Cancelar fatura
   */
  async cancelarFatura(clinicaId, faturaId, motivo) {
    try {
      const response = await axios.post(`/clinicas/${clinicaId}/faturas/${faturaId}/cancelar`, {
        motivo: motivo
      });
      return response.data;
    } catch (error) {
      console.error('Erro ao cancelar fatura:', error);
      throw error;
    }
  },

  /**
   * Obter estatísticas das faturas
   */
  async getEstatisticas(clinicaId) {
    try {
      const response = await axios.get(`/clinicas/${clinicaId}/faturas-estatisticas`);
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar estatísticas:', error);
      throw error;
    }
  },

  /**
   * Validar dados de fatura
   */
  validateFaturaData(data) {
    const errors = {};

    if (!data.descricao || data.descricao.trim() === '') {
      errors.descricao = 'Descrição é obrigatória';
    }

    if (data.valor_nominal === null || data.valor_nominal === undefined || data.valor_nominal < 0) {
      errors.valor_nominal = 'Valor nominal deve ser maior ou igual a zero';
    }

    if (data.valor_desconto && data.valor_desconto < 0) {
      errors.valor_desconto = 'Valor do desconto não pode ser negativo';
    }

    if (data.valor_acrescimo && data.valor_acrescimo < 0) {
      errors.valor_acrescimo = 'Valor do acréscimo não pode ser negativo';
    }

    if (!data.data_vencimento) {
      errors.data_vencimento = 'Data de vencimento é obrigatória';
    }

    // Validar se valor final não fica negativo
    const valorNominal = data.valor_nominal || 0;
    const valorDesconto = data.valor_desconto || 0;
    const valorAcrescimo = data.valor_acrescimo || 0;
    const valorFinal = valorNominal - valorDesconto + valorAcrescimo;

    if (valorFinal < 0) {
      errors.valor_desconto = 'O desconto não pode ser maior que o valor nominal';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  },

  /**
   * Calcular valor final da fatura
   */
  calcularValorFinal(valorNominal, valorDesconto = 0, valorAcrescimo = 0) {
    return Math.max(0, valorNominal - valorDesconto + valorAcrescimo);
  },

  /**
   * Formatar status da fatura
   */
  formatStatus(status) {
    const statusMap = {
      'pendente': { text: 'Pendente', color: 'warning', icon: 'fas fa-clock' },
      'pago': { text: 'Pago', color: 'success', icon: 'fas fa-check-circle' },
      'vencido': { text: 'Vencido', color: 'danger', icon: 'fas fa-exclamation-triangle' },
      'cancelado': { text: 'Cancelado', color: 'secondary', icon: 'fas fa-times-circle' }
    };

    return statusMap[status] || { text: status, color: 'secondary', icon: 'fas fa-question-circle' };
  },

  /**
   * Formatar forma de pagamento
   */
  formatFormaPagamento(forma) {
    const formaMap = {
      'dinheiro': 'Dinheiro',
      'cartao_credito': 'Cartão de Crédito',
      'cartao_debito': 'Cartão de Débito',
      'pix': 'PIX',
      'transferencia': 'Transferência',
      'boleto': 'Boleto'
    };

    return formaMap[forma] || forma;
  },

  /**
   * Formatar valor monetário
   */
  formatCurrency(value) {
    if (value === null || value === undefined) {
      return 'R$ 0,00';
    }
    
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  },

  /**
   * Formatar data
   */
  formatDate(date) {
    if (!date) return '';
    
    return new Date(date).toLocaleDateString('pt-BR');
  },

  /**
   * Calcular dias até vencimento
   */
  calcularDiasAteVencimento(dataVencimento) {
    if (!dataVencimento) return 0;
    
    const hoje = new Date();
    const vencimento = new Date(dataVencimento);
    const diffTime = vencimento - hoje;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays;
  },

  /**
   * Calcular dias em atraso
   */
  calcularDiasEmAtraso(dataVencimento) {
    if (!dataVencimento) return 0;
    
    const hoje = new Date();
    const vencimento = new Date(dataVencimento);
    const diffTime = hoje - vencimento;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return Math.max(0, diffDays);
  },

  /**
   * Verificar se fatura está vencida
   */
  isVencida(status, dataVencimento) {
    if (status === 'vencido') return true;
    if (status !== 'pendente') return false;
    
    const hoje = new Date();
    const vencimento = new Date(dataVencimento);
    
    return hoje > vencimento;
  },

  /**
   * Obter cor do status baseado na situação da fatura
   */
  getStatusColor(status, dataVencimento) {
    if (status === 'pago') return 'success';
    if (status === 'cancelado') return 'secondary';
    if (this.isVencida(status, dataVencimento)) return 'danger';
    
    const diasAteVencimento = this.calcularDiasAteVencimento(dataVencimento);
    if (diasAteVencimento <= 3) return 'warning';
    
    return 'info';
  },

  /**
   * Gerar dados padrão para nova fatura
   */
  getDefaultFaturaData() {
    const hoje = new Date();
    const vencimento = new Date();
    vencimento.setDate(hoje.getDate() + 30); // 30 dias a partir de hoje

    return {
      descricao: '',
      valor_nominal: 0,
      valor_desconto: 0,
      valor_acrescimo: 0,
      data_vencimento: vencimento.toISOString().split('T')[0],
      observacoes: ''
    };
  },

  /**
   * Filtrar faturas por período
   */
  filtrarPorPeriodo(faturas, dataInicio, dataFim) {
    if (!dataInicio || !dataFim) return faturas;
    
    const inicio = new Date(dataInicio);
    const fim = new Date(dataFim);
    
    return faturas.filter(fatura => {
      const dataVencimento = new Date(fatura.data_vencimento);
      return dataVencimento >= inicio && dataVencimento <= fim;
    });
  },

  /**
   * Agrupar faturas por status
   */
  agruparPorStatus(faturas) {
    return faturas.reduce((grupos, fatura) => {
      const status = fatura.status;
      if (!grupos[status]) {
        grupos[status] = [];
      }
      grupos[status].push(fatura);
      return grupos;
    }, {});
  }
};

export default faturasClinicasService;
