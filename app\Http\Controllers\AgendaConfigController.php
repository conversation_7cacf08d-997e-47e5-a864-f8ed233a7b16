<?php

namespace App\Http\Controllers;

use App\Models\AgendaConfig;
use App\Models\Consultorio;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class AgendaConfigController extends Controller
{
    /**
     * Get the agenda configuration for a specific consultorio
     */
    public function show(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $consultorioId = $request->query('consultorio_id');

            if (!$consultorioId) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'ID do consultório é obrigatório'
                ], 422);
            }

            // Verificar se o consultório pertence à clínica do usuário
            $consultorio = Consultorio::where('id', $consultorioId)
                ->where('clinica_id', $user->clinica_id)
                ->first();

            if (!$consultorio) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Consultório não encontrado'
                ], 404);
            }

            $config = AgendaConfig::getForConsultorio($consultorioId);

            return response()->json([
                'status' => 'success',
                'data' => $config->toFrontendFormat()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Erro ao carregar configurações da agenda',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the agenda configuration for a specific consultorio
     */
    public function update(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $consultorioId = $request->input('consultorio_id');

            if (!$consultorioId) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'ID do consultório é obrigatório'
                ], 422);
            }

            // Verificar se o consultório pertence à clínica do usuário
            $consultorio = Consultorio::where('id', $consultorioId)
                ->where('clinica_id', $user->clinica_id)
                ->first();

            if (!$consultorio) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Consultório não encontrado'
                ], 404);
            }
            
            // Validação dos dados
            $validatedData = $request->validate([
                'horario_inicio' => 'nullable|date_format:H:i',
                'horario_fim' => 'nullable|date_format:H:i',
                'dias_semana' => 'required|array|min:1',
                'dias_semana.*' => Rule::in(['segunda', 'terca', 'quarta', 'quinta', 'sexta', 'sabado', 'domingo']),
                'configuracoes_por_dia' => 'nullable|array',
                'configuracoes_por_dia.*.horario_inicio' => 'required_with:configuracoes_por_dia|date_format:H:i',
                'configuracoes_por_dia.*.horario_fim' => 'required_with:configuracoes_por_dia|date_format:H:i',
                'configuracoes_por_dia.*.tem_horario_almoco' => 'required_with:configuracoes_por_dia|boolean',
                'configuracoes_por_dia.*.horario_almoco_inicio' => 'nullable|date_format:H:i',
                'configuracoes_por_dia.*.horario_almoco_fim' => 'nullable|date_format:H:i',
                'duracao_padrao_consulta' => 'required|integer|min:15|max:240',
                'permitir_duracao_personalizada' => 'required|boolean',
                'intervalo_entre_consultas' => 'required|integer|min:0|max:60',
                'tem_horario_almoco' => 'nullable|boolean',
                'horario_almoco_inicio' => 'nullable|date_format:H:i',
                'horario_almoco_fim' => 'nullable|date_format:H:i',
                'permitir_agendamento_passado' => 'required|boolean',
                'permitir_agendamento_feriados' => 'required|boolean',
                'antecedencia_minima_agendamento' => 'required|integer|min:0|max:168',
                'antecedencia_maxima_agendamento' => 'required|integer|min:24|max:8760',
            ]);

            // Validações adicionais para configurações por dia
            if (isset($validatedData['configuracoes_por_dia'])) {
                foreach ($validatedData['configuracoes_por_dia'] as $dia => $config) {
                    if ($config['tem_horario_almoco'] &&
                        (!isset($config['horario_almoco_inicio']) || !isset($config['horario_almoco_fim']))) {
                        return response()->json([
                            'status' => 'error',
                            'message' => "Horário de almoço incompleto para {$dia}"
                        ], 422);
                    }

                    if ($config['tem_horario_almoco']) {
                        $almocoInicio = \Carbon\Carbon::createFromFormat('H:i', $config['horario_almoco_inicio']);
                        $almocoFim = \Carbon\Carbon::createFromFormat('H:i', $config['horario_almoco_fim']);
                        $horarioInicio = \Carbon\Carbon::createFromFormat('H:i', $config['horario_inicio']);
                        $horarioFim = \Carbon\Carbon::createFromFormat('H:i', $config['horario_fim']);

                        if ($almocoInicio->lessThan($horarioInicio) || $almocoFim->greaterThan($horarioFim)) {
                            return response()->json([
                                'status' => 'error',
                                'message' => "O horário de almoço de {$dia} deve estar dentro do horário de funcionamento"
                            ], 422);
                        }
                    }
                }
            }

            // Converter horários para formato de banco
            $dataToUpdate = $validatedData;

            // Manter compatibilidade com campos antigos se existirem
            if (isset($validatedData['horario_inicio'])) {
                $dataToUpdate['horario_inicio'] = $validatedData['horario_inicio'] . ':00';
            }
            if (isset($validatedData['horario_fim'])) {
                $dataToUpdate['horario_fim'] = $validatedData['horario_fim'] . ':00';
            }

            if (isset($validatedData['tem_horario_almoco']) && $validatedData['tem_horario_almoco']) {
                $dataToUpdate['horario_almoco_inicio'] = $validatedData['horario_almoco_inicio'] . ':00';
                $dataToUpdate['horario_almoco_fim'] = $validatedData['horario_almoco_fim'] . ':00';
            } else {
                $dataToUpdate['horario_almoco_inicio'] = null;
                $dataToUpdate['horario_almoco_fim'] = null;
            }

            // Buscar ou criar configuração para o consultório
            $config = AgendaConfig::where('consultorio_id', $consultorioId)
                ->where('ativo', true)
                ->first();

            if ($config) {
                $config->update($dataToUpdate);
            } else {
                $dataToUpdate['consultorio_id'] = $consultorioId;
                $dataToUpdate['ativo'] = true;
                $config = AgendaConfig::create($dataToUpdate);
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Configurações da agenda atualizadas com sucesso',
                'data' => $config->toFrontendFormat()
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Dados inválidos',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Erro ao atualizar configurações da agenda',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reset agenda configuration to default values
     */
    public function reset(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $consultorioId = $request->input('consultorio_id');

            if (!$consultorioId) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'ID do consultório é obrigatório'
                ], 422);
            }

            // Verificar se o consultório pertence à clínica do usuário
            $consultorio = Consultorio::where('id', $consultorioId)
                ->where('clinica_id', $user->clinica_id)
                ->first();

            if (!$consultorio) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Consultório não encontrado'
                ], 404);
            }

            $config = AgendaConfig::where('consultorio_id', $consultorioId)
                ->where('ativo', true)
                ->first();

            if ($config) {
                $defaultConfig = AgendaConfig::getDefaultConfig();
                $defaultConfig['ativo'] = true;
                $config->update($defaultConfig);
            } else {
                $config = AgendaConfig::createDefaultForConsultorio($consultorioId);
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Configurações da agenda restauradas para os valores padrão',
                'data' => $config->toFrontendFormat()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Erro ao restaurar configurações da agenda',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get time slots based on current configuration for a specific consultorio
     */
    public function getTimeSlots(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $consultorioId = $request->query('consultorio_id');

            if (!$consultorioId) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'ID do consultório é obrigatório'
                ], 422);
            }

            // Verificar se o consultório pertence à clínica do usuário
            $consultorio = Consultorio::where('id', $consultorioId)
                ->where('clinica_id', $user->clinica_id)
                ->first();

            if (!$consultorio) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Consultório não encontrado'
                ], 404);
            }

            $config = AgendaConfig::getForConsultorio($consultorioId);

            return response()->json([
                'status' => 'success',
                'data' => [
                    'time_slots' => $config->generateTimeSlots(),
                    'config' => $config->toFrontendFormat()
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Erro ao gerar horários disponíveis',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
