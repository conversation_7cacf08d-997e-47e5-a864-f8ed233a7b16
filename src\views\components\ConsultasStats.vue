<template>
  <div class="consultas-stats mb-2 container-fluid py-0" v-if="!isLoading">
    <div class="row">
      <!-- 1 - <PERSON><PERSON><PERSON> do tratamento -->
      <div class="col-6 col-md mb-1 mb-md-0">
        <div class="stats-card compact-card">
          <div class="card-icon">
            <font-awesome-icon :icon="['fas', 'play']" />
          </div>
          <div class="card-main-content">
            <div class="card-title">Início do tratamento</div>
            <div class="card-value editable" @click="editarDataInicio">
              {{ this.$filters.dateDmy(paciente.data_inicio_tratamento) }}
            </div>
            <div class="card-subtitle">{{ $filters.howMuchTime(paciente.data_inicio_tratamento, { type: 'date' }) }}</div>
          </div>
        </div>
      </div>

      <!-- 2 - Término previsto -->
      <div class="col-6 col-md mb-1 mb-md-0">
        <div class="stats-card compact-card">
          <div class="card-icon">
            <font-awesome-icon :icon="['fas', 'stopwatch']" />
          </div>
          <div class="card-main-content">
            <div class="card-title">Término previsto</div>
            <div class="card-value editable" @click="editarDataFinal">
              {{ this.$filters.dateDmy(paciente.data_final_prevista) }}
            </div>
            <div class="card-subtitle">{{ $filters.howMuchTime(paciente.data_final_prevista, { type: 'date' }) }}</div>
          </div>
        </div>
      </div>

      <!-- 3 - Próxima consulta -->
      <div class="col-6 col-md mb-1 mb-md-0">
        <div class="stats-card compact-card proxima-consulta-card">
          <div class="card-icon">
            <font-awesome-icon :icon="['fas', 'calendar-check']" />
          </div>
          <div class="card-main-content">
            <div class="card-title">Próxima consulta</div>
            <div class="card-value" v-if="proximaConsulta">
              {{ $filters.dateDmy(proximaConsulta) }}
            </div>
            <div class="card-value-action" v-if="!proximaConsulta">
              <button class="mini-btn" @click="abrirModalNovaConsulta">
                AGENDAR
              </button>
            </div>
            <div class="card-subtitle" v-if="proximaConsulta">
              {{ $filters.howMuchTime(proximaConsulta, { type: 'date' }) }}
            </div>
            <div class="card-subtitle" v-if="!proximaConsulta">
              não agendada
            </div>
          </div>
        </div>
      </div>

      <!-- 4 - Metas concluídas -->
      <div class="col-6 col-md mb-1 mb-md-0" v-if="paciente.status_tratamento == 'ATIVO'">
        <div class="stats-card compact-card">
          <div class="card-icon">
            <font-awesome-icon :icon="['fas', 'check-double']" />
          </div>
          <div class="card-main-content">
            <div class="card-title">Metas concluídas</div>
            <div class="card-value">
              {{ getMetasConcluidas }}/{{ getTotalMetas }}
            </div>
            <div class="progress-indicator-full">
              <span class="progress-fill" :style="{ width: getMetasProgresso + '%' }"></span>
            </div>
            <div class="card-subtitle">{{ getMetasRestantes }} restantes</div>
          </div>
        </div>
      </div>

      <!-- 5 - Progresso geral -->
      <div class="col-6 col-md mb-1 mb-md-0" v-if="paciente.status_tratamento == 'ATIVO'">
        <div class="stats-card compact-card">
          <div class="card-icon">
            <font-awesome-icon :icon="['fas', 'chart-line']" />
          </div>
          <div class="card-main-content">
            <div class="card-title">Progresso geral</div>
            <div class="card-value">
              {{ getProgresso }}%
            </div>
            <div class="progress-indicator-full">
              <span class="progress-fill" :style="{ width: getProgresso + '%' }"></span>
            </div>
            <div class="card-subtitle">{{ getDuracaoMeses }} meses</div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div v-else class="w-100 text-center py-3">
    <div class="spinner-border text-primary" role="status"></div>
  </div>
</template>

<script>
import cSwal from "@/utils/cSwal.js";
import { updatePacienteField } from "@/services/pacientesService";

export default {
  name: "ConsultasStats",
  props: {
    paciente: {
      type: Object,
      required: true
    }
  },
  emits: ['pacienteChange', 'abrirModalConsulta'],
  data() {
    return {
      isLoading: false
    };
  },
  computed: {
    // Cálculo do progresso geral do tratamento
    getProgresso() {
      if (!this.paciente || !this.paciente.data_inicio_tratamento || !this.paciente.data_final_prevista) {
        return 0;
      }

      const dataInicio = new Date(this.paciente.data_inicio_tratamento);
      const dataFinal = new Date(this.paciente.data_final_prevista);
      const hoje = new Date();

      // Se o tratamento ainda não começou
      if (hoje < dataInicio) return 0;

      // Se o tratamento já terminou
      if (hoje > dataFinal) return 100;

      // Calcular o progresso
      const totalDias = (dataFinal - dataInicio) / (1000 * 60 * 60 * 24);
      const diasPassados = (hoje - dataInicio) / (1000 * 60 * 60 * 24);

      const progresso = (diasPassados / totalDias) * 100;
      return parseFloat(progresso.toFixed(1));
    },

    // Total de metas terapêuticas
    getTotalMetas() {
      if (!this.paciente || !this.paciente.metas_terapeuticas) return 0;
      return this.paciente.metas_terapeuticas.length;
    },

    // Metas terapêuticas concluídas
    getMetasConcluidas() {
      if (!this.paciente || !this.paciente.metas_terapeuticas) return 0;
      return this.paciente.metas_terapeuticas.filter(meta => meta.status === 'CONCLUIDA').length;
    },

    // Metas terapêuticas restantes
    getMetasRestantes() {
      if (!this.paciente || !this.paciente.metas_terapeuticas) return 0;
      return this.getTotalMetas - this.getMetasConcluidas;
    },

    // Progresso das metas terapêuticas
    getMetasProgresso() {
      if (!this.paciente || !this.paciente.metas_terapeuticas || this.getTotalMetas === 0) return 0;
      const progresso = (this.getMetasConcluidas / this.getTotalMetas) * 100;
      return parseFloat(progresso.toFixed(1));
    },

    // Duração do tratamento em meses
    getDuracaoMeses() {
      if (!this.paciente || !this.paciente.data_inicio_tratamento || !this.paciente.data_final_prevista) {
        return "0 de 0";
      }

      const dataInicio = new Date(this.paciente.data_inicio_tratamento);
      const dataFinal = new Date(this.paciente.data_final_prevista);
      const hoje = new Date();

      // Calcular o total de meses entre início e fim
      const totalMeses = (dataFinal.getFullYear() - dataInicio.getFullYear()) * 12 +
                         (dataFinal.getMonth() - dataInicio.getMonth());

      // Calcular quantos meses já se passaram
      const mesesPassados = (hoje.getFullYear() - dataInicio.getFullYear()) * 12 +
                           (hoje.getMonth() - dataInicio.getMonth());

      const mesesDecorridos = Math.max(0, Math.min(mesesPassados, totalMeses));

      return `${mesesDecorridos} de ${totalMeses}`;
    },

    // Data da próxima consulta (consultas futuras)
    proximaConsulta() {
      if (!this.paciente.consultas || !Array.isArray(this.paciente.consultas) || this.paciente.consultas.length === 0) {
        return null;
      }

      const agora = new Date();

      const consultasFuturas = this.paciente.consultas
        .filter(consulta => {
          const dataConsulta = new Date(consulta.horario);
          // Considera consultas a partir do momento atual (incluindo hoje)
          return dataConsulta >= agora && consulta.status !== 'cancelada';
        })
        .sort((a, b) => new Date(a.horario) - new Date(b.horario));

      return consultasFuturas.length > 0 ? consultasFuturas[0].horario : null;
    }
  },
  methods: {
    statusClass(status) {
      const classMap = {
        "NÃO INICIADO": "bg-gradient-warning",
        CONCLUÍDO: "bg-gradient-success",
        ATIVO: "bg-gradient-secondary",
      };

      return classMap[status] || "";
    },
    statusText(status) {
      const textMap = {
        "NÃO INICIADO": "NÃO INICIADO",
        CONCLUÍDO: "CONCLUÍDO",
        ATIVO: "EM ANDAMENTO",
      };

      return textMap[status] || "";
    },
    editarDataInicio() {
      const dataAtual = this.paciente.data_inicio_tratamento ? this.paciente.data_inicio_tratamento.split('T')[0] : '';
      const dataFormatada = this.$filters.dateDmy(this.paciente.data_inicio_tratamento);

      cSwal.fire({
        title: 'Editar data de início',
        html: `<p>Alterar a data de início do tratamento:</p>
               <p>Data atual: <b>${dataFormatada}</b></p>`,
        input: 'date',
        inputValue: dataAtual,
        showCancelButton: true,
        confirmButtonText: 'Salvar',
        cancelButtonText: 'Cancelar',
        inputValidator: (value) => {
          if (!value) {
            return 'É necessário selecionar uma data';
          }
        }
      }).then((result) => {
        if (result.isConfirmed && result.value) {
          this.atualizarCampoPaciente('data_inicio_tratamento', result.value);
        }
      });
    },
    editarDataFinal() {
      const dataAtual = this.paciente.data_final_prevista ? this.paciente.data_final_prevista.split('T')[0] : '';
      const dataFormatada = this.$filters.dateDmy(this.paciente.data_final_prevista);

      cSwal.fire({
        title: 'Editar data de término',
        html: `<p>Alterar a data de término previsto do tratamento:</p>
               <p>Data atual: <b>${dataFormatada}</b></p>`,
        input: 'date',
        inputValue: dataAtual,
        showCancelButton: true,
        confirmButtonText: 'Salvar',
        cancelButtonText: 'Cancelar',
        inputValidator: (value) => {
          if (!value) {
            return 'É necessário selecionar uma data';
          }
        }
      }).then((result) => {
        if (result.isConfirmed && result.value) {
          this.atualizarCampoPaciente('data_final_prevista', result.value);
        }
      });
    },
    async atualizarCampoPaciente(campo, valor) {
      try {
        this.isLoading = true;

        // Confirmar a atualização
        cSwal.cConfirm(
          `Deseja realmente alterar a data para <b>${this.$filters.dateDmy(valor)}</b>?`,
          async () => {
            cSwal.loading('Atualizando dados do paciente...');

            const resultado = await updatePacienteField(this.paciente.id, campo, valor);

            cSwal.loaded();

            if (resultado) {
              cSwal.cSuccess('Data atualizada com sucesso!');

              // Emitir evento para atualizar os dados do paciente no componente pai
              this.$emit('pacienteChange');
            } else {
              cSwal.cError('Erro ao atualizar a data. Por favor, tente novamente.');
            }

            this.isLoading = false;
          },
          {
            title: 'Confirmar alteração',
            confirmButtonText: 'Sim, alterar',
            cancelButtonText: 'Cancelar'
          }
        );
      } catch (error) {
        console.error('Erro ao atualizar campo do paciente:', error);
        cSwal.cError('Erro ao processar a solicitação. Por favor, tente novamente.');
        this.isLoading = false;
      }
    },
    abrirModalNovaConsulta() {
      this.$emit('abrirModalConsulta');
    }
  }
};
</script>

<style scoped>
/* Estilos para os cards de estatísticas - Design Ultra Compacto */
.consultas-stats {
  margin-top: 0.25rem;
}

.stats-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 0.75rem;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.04);
  height: 100%;
  transition: all 0.2s ease;
  border: 1px solid rgba(0, 0, 0, 0.04);
  min-height: 70px;
}

.stats-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

/* Layout centralizado com ícone lateral */
.compact-card {
  display: flex;
  align-items: center;
  gap: 0.6rem;
  position: relative;
}

.card-icon {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 4px;
  color: #6c757d;
  font-size: 0.65rem;
  position: absolute;
  left: 0.6rem;
  top: 0.6rem;
}

.card-main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding-left: 0;
}

.card-title {
  font-size: 0.65rem;
  font-weight: 600;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  line-height: 1.1;
  margin-bottom: 0.2rem;
}

.card-value {
  font-size: 0.85rem;
  font-weight: 600;
  color: #344767;
  line-height: 1.2;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.4rem;
}

.card-value-action {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 0.15rem;
}

.card-subtitle {
  font-size: 0.65rem;
  color: #8392ab;
  font-weight: 500;
  margin-top: 0.25rem;
}

/* Estilos para valores editáveis */
.editable {
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
  padding: 0.15rem 0.3rem;
  border-radius: 4px;
  background: rgba(248, 249, 250, 0.5);
  border: 1px solid transparent;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.editable:hover {
  background: rgba(248, 249, 250, 0.8);
  border-color: rgba(13, 110, 253, 0.2);
}

/* Ícone de edição usando pseudo-elemento */
.editable::after {
  content: '\f044'; /* FontAwesome edit icon */
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  font-size: 0.6rem;
  color: #adb5bd;
  opacity: 0;
  transition: opacity 0.2s ease;
  position: absolute;
  right: -16px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}

.editable:hover::after {
  opacity: 1;
  color: #0d6efd;
}

/* Progress indicator full width */
.progress-indicator-full {
  width: 100%;
  height: 4px;
  background: #e9ecef;
  border-radius: 2px;
  overflow: hidden;
  position: relative;
  margin: 0.2rem 0 0.15rem 0;
}

.progress-fill {
  display: block;
  height: 100%;
  background: linear-gradient(90deg, #17ad37, #7fce18);
  border-radius: 2px;
  transition: width 0.6s ease;
}

/* Botão mini */
.mini-btn {
  background: transparent;
  border: 1px solid #0d6efd;
  border-radius: 3px;
  color: #0d6efd;
  font-size: 0.55rem;
  font-weight: 600;
  padding: 0.2rem 0.4rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.mini-btn:hover {
  background: #0d6efd;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 1px 4px rgba(13, 110, 253, 0.3);
}

/* Destaque sutil para o card de próxima consulta */
.proxima-consulta-card {
  border: 1px solid rgba(13, 110, 253, 0.08) !important;
  background: linear-gradient(135deg, #ffffff 0%, rgba(13, 110, 253, 0.02) 100%);
}

.proxima-consulta-card:hover {
  border: 1px solid rgba(13, 110, 253, 0.15) !important;
  box-shadow: 0 2px 16px rgba(13, 110, 253, 0.12) !important;
}

.proxima-consulta-card .card-icon {
  background: linear-gradient(135deg, rgba(13, 110, 253, 0.12) 0%, rgba(13, 110, 253, 0.06) 100%);
  color: #0d6efd;
  border: 1px solid rgba(13, 110, 253, 0.1);
  width: 20px;
  height: 20px;
}

/* Responsividade */
@media (max-width: 768px) {
  .stats-card {
    padding: 0.6rem;
    min-height: 65px;
  }

  .card-title {
    font-size: 0.6rem;
  }

  .card-value {
    font-size: 0.8rem;
  }

  .card-subtitle {
    font-size: 0.55rem;
  }
}

@media (max-width: 480px) {
  .stats-card {
    padding: 0.5rem;
    min-height: 60px;
  }

  .card-header {
    gap: 0.4rem;
  }

  .card-icon {
    width: 18px;
    height: 18px;
    font-size: 0.65rem;
  }
}
</style>
