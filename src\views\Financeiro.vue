<template>
  <lumi-sidenav
    icon="mdi-currency-usd"
    class="fixed-end lumi-sidenav"
    v-if="showSidenav"
    :config="sidenavConfig"
    @action="handleSidenavAction"
  ></lumi-sidenav>

  <div class="main-page-content">
    <div class="container-fluid p-0">
      <!-- Conteúdo das abas -->
      <div class="tab-content" id="tabs-tabContent">
        <!-- <PERSON>ba Gestão Detalhada (Analítico) -->
        <div class="tab-pane fade"
             :class="{ 'show active': activeTab === 'analitico' }"
             id="analitico"
             role="tabpanel">
          <financeiro-analitico
            :faturas="faturas"
            :loading="loading.faturas"
            :estatisticas="estatisticas"
            @refresh="loadFaturas"
            @create="openCreateCompleteModal"
            @edit="openEditModal"
            @delete="deleteFatura"
            @mark-paid="markAsPaid"
            @generate-receipt="generateReceipt"
            @duplicate="duplicateFatura"
            @send-reminder="sendReminder"
          />
        </div>

        <!-- Aba Dashboard Executivo (Sintético) -->
        <div class="tab-pane fade"
             :class="{ 'show active': activeTab === 'sintetico' }"
             id="sintetico"
             role="tabpanel">
          <financeiro-sintetico
            :estatisticas="estatisticas"
            :faturas="faturas"
            :loading="loading.estatisticas"
            @refresh="loadEstatisticas"
          />
        </div>
      </div>
    </div>
  </div>

  <!-- Modal para criar/editar fatura -->
  <financeiro-modal
    ref="financeiroModal"
    @saved="onFaturaSaved"
  />

  <!-- Modal de Criação Completo -->
  <financeiro-create-modal
    ref="financeiroCreateModal"
    @saved="onFaturaSaved"
  />
</template>
<script>
import { mapState, mapMutations } from "vuex";
import LumiSidenav from "@/views/components/LumiSidenav/index.vue";
import FinanceiroAnalitico from "@/components/Financeiro/FinanceiroAnalitico.vue";
import FinanceiroSintetico from "@/components/Financeiro/FinanceiroSintetico.vue";
import FinanceiroModal from "@/components/Financeiro/FinanceiroModal.vue";
import FinanceiroCreateModal from "@/components/Financeiro/FinanceiroCreateModal.vue";
import { financeiroService } from "@/services/financeiroService";
import cSwal from "@/utils/cSwal.js";

export default {
  name: "Financeiro",
  components: {
    LumiSidenav,
    FinanceiroAnalitico,
    FinanceiroSintetico,
    FinanceiroModal,
    FinanceiroCreateModal,
  },
  data() {
    return {
      activeTab: 'analitico',
      faturas: [],
      estatisticas: {},
      loading: {
        faturas: false,
        estatisticas: false,
      },
      sidenavConfig: {
        groups: []
      }
    };
  },
  computed: {
    ...mapState(["showSidenav"]),
  },
  methods: {
    ...mapMutations(["navbarMinimize"]),

    updateSidenavConfig() {
      this.sidenavConfig = {
        groups: [
          {
            title: "VISUALIZAÇÃO",
            buttons: [
              {
                text: "Gestão Detalhada",
                icon: ["fas", "list"],
                iconType: "fontawesome",
                action: "changeTab",
                actionData: "analitico",
                active: this.activeTab === 'analitico'
              },
              {
                text: "Dashboard Executivo",
                icon: ["fas", "chart-pie"],
                iconType: "fontawesome",
                action: "changeTab",
                actionData: "sintetico",
                active: this.activeTab === 'sintetico'
              }
            ]
          },
          {
            title: "FINANCEIRO",
            buttons: [
              {
                text: "Nova Fatura",
                icon: "add",
                iconType: "material",
                action: "newFatura",
                attributes: {
                  "data-bs-toggle": "modal",
                  "data-bs-target": "#modalFinanceiro"
                }
              },
              {
                text: "Relatório Mensal",
                icon: "assessment",
                iconType: "material",
                action: "monthlyReport"
              },
              {
                text: "Exportar Dados",
                icon: "download",
                iconType: "material",
                action: "exportData"
              }
            ]
          }
        ]
      };
    },

    handleSidenavAction(action, button) {
      console.log(`Action: ${action}`, button);

      switch (action) {
        case 'changeTab':
          this.changeTab(button.actionData);
          break;
        case 'newFatura':
          this.openCreateModal();
          break;
        case 'monthlyReport':
          this.generateMonthlyReport();
          break;
        case 'exportData':
          this.exportData();
          break;
      }
    },

    changeTab(tab) {
      this.activeTab = tab;
      this.updateSidenavConfig(); // Atualizar a configuração para refletir o estado ativo
    },

    async loadFaturas(filters = {}) {
      this.loading.faturas = true;
      try {
        const response = await financeiroService.getFaturas(filters);
        this.faturas = response.data.data.data || [];
      } catch (error) {
        console.error('Erro ao carregar faturas:', error);
        cSwal.cError('Erro ao carregar faturas');
      } finally {
        this.loading.faturas = false;
      }
    },

    async loadEstatisticas() {
      this.loading.estatisticas = true;
      try {
        const response = await financeiroService.getEstatisticas();
        this.estatisticas = response.data.data;
      } catch (error) {
        console.error('Erro ao carregar estatísticas:', error);
        cSwal.cError('Erro ao carregar estatísticas');
      } finally {
        this.loading.estatisticas = false;
      }
    },

    openCreateModal() {
      this.$refs.financeiroModal.openCreate();
    },

    openCreateCompleteModal() {
      this.$refs.financeiroCreateModal.open('fatura');
    },

    openEditModal(fatura) {
      this.$refs.financeiroModal.openEdit(fatura);
    },

    async deleteFatura(faturaId) {
      if (confirm('Tem certeza que deseja cancelar esta fatura?')) {
        try {
          await financeiroService.deleteFatura(faturaId);
          cSwal.cSuccess('Fatura cancelada com sucesso');
          this.loadFaturas();
        } catch (error) {
          console.error('Erro ao cancelar fatura:', error);
          cSwal.cError('Erro ao cancelar fatura');
        }
      }
    },

    async markAsPaid(faturaId, paymentData) {
      try {
        await financeiroService.markAsPaid(faturaId, paymentData);
        cSwal.cSuccess('Fatura marcada como paga');
        this.loadFaturas();
        this.loadEstatisticas();
      } catch (error) {
        console.error('Erro ao marcar fatura como paga:', error);
        cSwal.cError('Erro ao marcar fatura como paga');
      }
    },

    onFaturaSaved() {
      this.loadFaturas();
      this.loadEstatisticas();
    },

    generateMonthlyReport() {
      // TODO: Implementar geração de relatório mensal
      cSwal.cInfo('Funcionalidade em desenvolvimento');
    },

    exportData() {
      // TODO: Implementar exportação de dados
      cSwal.cInfo('Funcionalidade em desenvolvimento');
    },

    generateReceipt(fatura) {
      // TODO: Implementar geração de recibo
      cSwal.cInfo(`Gerando recibo para a fatura: ${fatura.descricao}`);
    },

    duplicateFatura(fatura) {
      // TODO: Implementar duplicação de fatura
      cSwal.cInfo(`Duplicando fatura: ${fatura.descricao}`);
    },

    sendReminder(fatura) {
      // TODO: Implementar envio de lembrete
      cSwal.cSuccess(`Lembrete enviado para ${fatura.paciente?.nome || 'paciente'}`);
    },
  },

  async mounted() {
    this.updateSidenavConfig(); // Inicializar a configuração da sidenav
    await Promise.all([
      this.loadFaturas(),
      this.loadEstatisticas()
    ]);
  },
};
</script>

<style scoped>
/* Animação suave para mudança de abas */
.tab-content .tab-pane {
  transition: opacity 0.3s ease-in-out;
}

.tab-content .tab-pane:not(.show) {
  opacity: 0;
}

.tab-content .tab-pane.show {
  opacity: 1;
}
</style>
