<template>
  <div class="user-management">
    <!-- Per<PERSON>são negada -->
    <div v-if="!isClinicaAdmin" class="alert alert-warning">
      <i class="fas fa-exclamation-triangle me-2"></i>
      Você não tem permissão para gerenciar usuários desta clínica.
    </div>

    <!-- Card com estilo TabelaPrecos -->
    <div v-else class="card shadow-sm">
      <!-- Header -->
      <div class="card-header bg-gradient-light">
        <div class="row align-items-center">
          <div class="col">
            <h4 class="mb-0 text-dark">
              <i class="fas fa-users me-2"></i>
              Gerenciamento de Usuários
            </h4>
            <small class="text-muted">Clínica: <strong>{{ clinicaNome }}</strong></small>
          </div>
          <div class="col-auto">
            <button
              class="btn btn-primary btn-sm"
              @click="openNewUserForm"
              title="Adicionar Usuário"
            >
              <i class="fas fa-plus me-1"></i>
              Novo Usuário
            </button>
          </div>
        </div>
      </div>

      <!-- Body -->
      <div class="card-body p-0">
        <!-- Filtros -->
        <div class="filtros-container p-3 border-bottom bg-white">
          <div class="input-group input-group-sm">
            <span class="input-group-text filtro-icon">
              <i class="fas fa-search"></i>
            </span>
            <input
              v-model="searchQuery"
              type="text"
              class="form-control form-control-sm filtro-input"
              placeholder="Buscar usuário por nome ou email..."
              @input="handleSearch"
            >
          </div>
        </div>

        <!-- Loading -->
        <div v-if="isLoading" class="text-center p-4">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Carregando...</span>
          </div>
        </div>

        <!-- Tabela de usuários -->
        <div v-else-if="filteredUsuarios.length > 0" class="table-responsive">
          <table class="table table-hover mb-0">
            <thead class="table-light">
              <tr>
                <th class="nome-cell">Nome</th>
                <th>Tipo</th>
                <th>Administrador</th>
                <th>Status</th>
                <th>Último Login</th>
                <th>Email</th>
                <th class="acoes-column" style="width: 100px;">Ações</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="usuario in filteredUsuarios" :key="usuario.id">
                <td class="nome-cell">
                  <strong>{{ usuario.name }}</strong>
                </td>
                <td>
                  <span class="badge tipo-badge" :class="getTipoBadgeClass(usuario)">
                    <i :class="getTipoIcon(usuario)" class="me-1"></i>
                    {{ getTipoUsuario(usuario) }}
                  </span>
                </td>
                <td>
                  <span v-if="usuario.clinica_admin" class="badge badge-sm bg-success">
                    SIM
                  </span>
                  <span v-else class="badge badge-sm bg-light text-muted badge-bordered">
                    NÃO
                  </span>
                </td>
                <td>
                  <span v-if="!usuario.deleted_at" class="badge bg-success">
                    Ativo
                  </span>
                  <span v-else class="badge bg-danger">
                    Inativo
                  </span>
                </td>
                <td>
                  <span v-if="usuario.last_login" class="text-muted">
                    {{ $filters.dateDmy(usuario.last_login) }}
                  </span>
                  <span v-else class="text-muted">Nunca</span>
                </td>
                <td>{{ usuario.email || '-' }}</td>
                <td class="acoes-column">
                  <div class="btn-group btn-group-sm">
                    <button
                      v-if="usuario.id !== currentUserId"
                      class="btn btn-outline-primary btn-sm"
                      @click="editUsuario(usuario)"
                      title="Editar"
                    >
                      <i class="fas fa-edit"></i>
                    </button>
                    <button
                      v-if="usuario.id !== currentUserId"
                      class="btn btn-outline-danger btn-sm"
                      @click="deleteUsuario(usuario)"
                      title="Desativar"
                    >
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Estado vazio -->
        <div v-else class="text-center p-5">
          <i class="fas fa-users fa-3x text-muted mb-3"></i>
          <h5 class="text-muted">Nenhum usuário encontrado</h5>
          <p class="text-muted">
            {{ searchQuery ?
               'Tente ajustar os filtros de busca.' :
               'Comece adicionando seu primeiro usuário.' }}
          </p>
          <button
            v-if="!searchQuery"
            class="btn btn-primary"
            @click="openNewUserForm"
          >
            <i class="fas fa-plus me-1"></i>
            Adicionar Primeiro Usuário
          </button>
        </div>
      </div>
    </div>

    <!-- Modal de formulário -->
    <UserForm
      :clinica-id="clinicaId"
      :usuario="selectedUsuario"
      @usuario-saved="handleUsuarioSaved"
    />
  </div>
</template>

<script>
import UserForm from './UserForm.vue';
import usuariosService from '@/services/usuariosService';
import { getClinica } from '@/services/clinicasService';
import cSwal from '@/utils/cSwal';
import { Modal } from 'bootstrap';

export default {
  name: 'UserManagement',
  components: {
    UserForm
  },
  props: {
    clinicaId: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      clinicaNome: '',
      isClinicaAdmin: false,
      selectedUsuario: null,
      usuarios: [],
      searchQuery: '',
      isLoading: false,
      currentUserId: null,
      searchTimeout: null
    };
  },
  computed: {
    filteredUsuarios() {
      if (!this.searchQuery) return this.usuarios;

      const query = this.searchQuery.toLowerCase();
      return this.usuarios.filter(u =>
        u.name.toLowerCase().includes(query) ||
        u.username.toLowerCase().includes(query) ||
        (u.email && u.email.toLowerCase().includes(query))
      );
    }
  },
  async mounted() {
    this.isClinicaAdmin = usuariosService.isClinicaAdmin();
    const decoded = usuariosService.decodedToken();
    this.currentUserId = decoded?.sub;

    // Carregar dados da clínica
    try {
      const clinica = await getClinica(this.clinicaId);
      this.clinicaNome = clinica.nome || 'Clínica';
    } catch (error) {
      console.error('Erro ao carregar clínica:', error);
      this.clinicaNome = 'Clínica';
    }

    // Carregar usuários
    if (this.isClinicaAdmin) {
      await this.loadUsuarios();
    }
  },
  methods: {
    async loadUsuarios() {
      this.isLoading = true;
      try {
        const response = await usuariosService.getClinicaUsuarios(this.clinicaId);
        this.usuarios = response.data || response;
      } catch (error) {
        console.error('Erro ao carregar usuários:', error);
        cSwal.cError('Erro ao carregar usuários');
      } finally {
        this.isLoading = false;
      }
    },
    handleSearch() {
      // Debounce search
      clearTimeout(this.searchTimeout);
      this.searchTimeout = setTimeout(() => {
        // Filtro é feito no computed
      }, 300);
    },
    limparFiltros() {
      this.searchQuery = '';
    },
    openNewUserForm() {
      this.selectedUsuario = null;
      this.$nextTick(() => {
        const modalElement = document.getElementById('userFormModal');
        const modal = new Modal(modalElement);
        modal.show();
      });
    },
    editUsuario(usuario) {
      this.selectedUsuario = usuario;
      this.$nextTick(() => {
        const modalElement = document.getElementById('userFormModal');
        const modal = new Modal(modalElement);
        modal.show();
      });
    },
    async deleteUsuario(usuario) {
      const result = await cSwal.cConfirm(
        `Desativar usuário "${usuario.name}"?`,
        'Esta ação não pode ser desfeita.'
      );

      if (!result.isConfirmed) return;

      try {
        await usuariosService.deleteClinicaUsuario(this.clinicaId, usuario.id);
        cSwal.cSuccess('Usuário desativado com sucesso');
        await this.loadUsuarios();
      } catch (error) {
        console.error('Erro ao desativar usuário:', error);
        cSwal.cError('Erro ao desativar usuário');
      }
    },
    handleUsuarioSaved() {
      this.selectedUsuario = null;
      this.loadUsuarios();
    },
    getTipoUsuario(usuario) {
      // Verificar se o usuário tem um campo user_type
      if (usuario.user_type) {
        return usuario.user_type === 'profissional' ? 'Profissional da Saúde' : 'Colaborador';
      }

      // Fallback: verificar se tem dentista associado
      if (usuario.dentista || usuario.dentista_id) {
        return 'Profissional da Saúde';
      }

      return 'Colaborador';
    },
    getTipoIcon(usuario) {
      const tipo = this.getTipoUsuario(usuario);
      return tipo === 'Profissional da Saúde' ? 'fas fa-stethoscope' : 'fas fa-user';
    },
    getTipoBadgeClass(usuario) {
      const tipo = this.getTipoUsuario(usuario);
      return tipo === 'Profissional da Saúde' ? 'bg-teal' : 'bg-info';
    }
  }
};
</script>

<style scoped>
.user-management {
  min-height: 600px;
}

.card {
  border-radius: 0 !important;
}

.card-header {
  border-radius: 0 !important;
}

.table th {
  font-weight: 600;
  font-size: 0.8rem;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
  vertical-align: middle;
  padding: 0.5rem 0.4rem;
}

.table td {
  vertical-align: middle;
  font-size: 0.8rem;
  padding: 0.5rem 0.4rem;
}

.badge {
  font-size: 0.65rem;
  padding: 0.35rem 0.5rem;
}

.badge-sm {
  font-size: 0.6rem;
  padding: 0.25rem 0.4rem;
}

.badge-bordered {
  border: 1px solid #d1d3e2;
}

.tipo-badge {
  min-width: 180px;
  display: inline-block;
  text-align: center;
}

.bg-teal {
  background-color: #20c997 !important;
  color: white;
}

.btn-group-sm > .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.bg-gradient-light {
  background: linear-gradient(87deg, #e9ecef 0, #f8f9fc 100%) !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

/* Estilos dos filtros */
.filtros-container {
  background: linear-gradient(135deg, #f8f9fc 0%, #ffffff 100%) !important;
  border-bottom: 1px solid #e3e6f0 !important;
}

.filtro-input {
  border: 1px solid #d1d3e2;
  border-radius: 0.375rem;
  transition: all 0.15s ease-in-out;
  font-size: 0.875rem;
}

.filtro-input:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.filtro-icon {
  background-color: #f8f9fc;
  border-color: #d1d3e2;
  color: #5a5c69;
  border-radius: 0.375rem 0 0 0.375rem;
}

.filtro-btn {
  border: 1px solid #d1d3e2;
  border-radius: 0.375rem;
  transition: all 0.15s ease-in-out;
}

.filtro-btn:hover {
  background-color: #f8f9fc;
  border-color: #adb5bd;
}

/* Padding para coluna Ações */
.acoes-column {
  padding-right: 1rem !important;
}

/* Padding para coluna Nome */
.nome-cell {
  padding-left: 1.5rem !important;
}

/* Aumentar fonte da coluna Último Login */
.table td:nth-child(6) {
  font-size: 0.9rem;
}
</style>

