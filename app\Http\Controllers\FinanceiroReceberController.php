<?php

namespace App\Http\Controllers;

use App\Models\FinanceiroReceber;
use App\Models\Orcamento; // IMPORT ADICIONADO
use App\Models\Paciente;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class FinanceiroReceberController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {

        $query = FinanceiroReceber::with(['paciente', 'dentista', 'orcamento']); // RELACIONAMENTO ADICIONADO

        // Filtros
        if ($request->has('paciente_id')) {
            $query->doPaciente($request->paciente_id);
        }

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('descricao')) {
            $query->where('descricao', 'LIKE', '%' . $request->descricao . '%');
        }

        if ($request->has('data_inicio') && $request->has('data_fim')) {
            $query->whereBetween('data_vencimento', [
                $request->data_inicio,
                $request->data_fim
            ]);
        }

        if ($request->has('vencidas') && $request->vencidas) {
            $query->vencidas();
        }

        $faturas = $query->orderBy('data_vencimento', 'desc')
                        ->orderBy('created_at', 'desc')
                        ->paginate(15);

        return responseSuccess(['data' => $faturas]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $user = auth()->payload();

        $validator = Validator::make($request->all(), [
            'paciente_id' => 'required|exists:pacientes,id',
            'orcamento_id' => 'nullable|exists:orcamentos,id', // VALIDAÇÃO ADICIONADA
            'descricao' => 'required|string|max:255',
            'valor_nominal' => 'required|numeric|min:0.01',
            'data_vencimento' => 'required|date',
            'parcelas_total' => 'integer|min:1|max:60',
            'percentual_desconto' => 'nullable|numeric|min:0|max:100',
            'valor_desconto' => 'nullable|numeric|min:0',
            'percentual_acrescimo' => 'nullable|numeric|min:0|max:100',
            'valor_acrescimo' => 'nullable|numeric|min:0',
            'observacoes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return responseError([
                'message' => 'Dados inválidos',
                'data' => $validator->errors(),
                'statusCode' => 422
            ]);
        }

        try {
            $dados = [
                'clinica_id' => $user['clinica']['id'],
                'paciente_id' => $request->paciente_id,
                'dentista_id' => $request->dentista_id ?: null,
                'orcamento_id' => $request->orcamento_id ?: null, // CAMPO ADICIONADO
                'descricao' => $request->descricao,
                'valor_nominal' => (float) $request->valor_nominal,
                'data_vencimento' => $request->data_vencimento,
                'parcelas_total' => (int) ($request->get('parcelas_total') ?: 1),
                'percentual_desconto' => (float) ($request->get('percentual_desconto') ?: 0),
                'valor_desconto' => (float) ($request->get('valor_desconto') ?: 0),
                'percentual_acrescimo' => (float) ($request->get('percentual_acrescimo') ?: 0),
                'valor_acrescimo' => (float) ($request->get('valor_acrescimo') ?: 0),
                'observacoes' => $request->observacoes ?: null,
                'data_emissao' => now(),
                'lancado_por' => $user['id'],
                'status' => FinanceiroReceber::STATUS_PENDENTE,
            ];

            $numeroParcelas = $request->get('parcelas_total', 1);

            DB::beginTransaction();
            try {
                if ($numeroParcelas > 1) {
                    $faturas = FinanceiroReceber::criarComParcelamento($dados, $numeroParcelas);

                    // LÓGICA ADICIONADA: Atualizar status do orçamento se foi gerado a partir de um
                    if ($request->orcamento_id) {
                        $orcamento = Orcamento::find($request->orcamento_id);
                        if ($orcamento) {
                            // Atualizar status dinamicamente baseado nos itens e faturas
                            $orcamento->atualizarStatusDinamico();
                            Log::info("Orçamento #{$orcamento->id} status atualizado após geração de {$numeroParcelas} faturas");
                        }
                    }

                    DB::commit();
                    return responseSuccess([
                        'message' => "Fatura criada com sucesso em {$numeroParcelas} parcelas",
                        'data' => $faturas
                    ]);
                } else {
                    $fatura = FinanceiroReceber::create($dados);
                    $fatura->valor_final = $fatura->calcularValorFinal();
                    $fatura->save();

                    // LÓGICA ADICIONADA: Atualizar status do orçamento se foi gerado a partir de um
                    if ($request->orcamento_id) {
                        $orcamento = Orcamento::find($request->orcamento_id);
                        if ($orcamento) {
                            // Atualizar status dinamicamente baseado nos itens e faturas
                            $orcamento->atualizarStatusDinamico();
                            Log::info("Orçamento #{$orcamento->id} status atualizado após geração da fatura #{$fatura->id}");
                        }
                    }

                    DB::commit();
                    return responseSuccess([
                        'message' => 'Fatura criada com sucesso',
                        'data' => $fatura->load(['paciente', 'dentista', 'orcamento'])
                    ]);
                }
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
        } catch (\Exception $e) {
            return responseError([
                'message' => 'Erro ao criar fatura: ' . $e->getMessage(),
                'statusCode' => 500
            ]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $fatura = FinanceiroReceber::with(['paciente', 'dentista', 'parcelas', 'orcamento']) // RELACIONAMENTO ADICIONADO
            ->findOrFail($id);

        return responseSuccess(['data' => $fatura]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $fatura = FinanceiroReceber::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'descricao' => 'sometimes|string|max:255',
            'valor_nominal' => 'sometimes|numeric|min:0.01',
            'data_vencimento' => 'sometimes|date',
            'percentual_desconto' => 'nullable|numeric|min:0|max:100',
            'valor_desconto' => 'nullable|numeric|min:0',
            'percentual_acrescimo' => 'nullable|numeric|min:0|max:100',
            'valor_acrescimo' => 'nullable|numeric|min:0',
            'observacoes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return responseError([
                'message' => 'Dados inválidos',
                'data' => $validator->errors(),
                'statusCode' => 422
            ]);
        }

        try {
            $fatura->update($request->all());
            $fatura->valor_final = $fatura->calcularValorFinal();
            $fatura->save();

            return responseSuccess([
                'message' => 'Fatura atualizada com sucesso',
                'data' => $fatura->load(['paciente', 'dentista', 'orcamento']) // RELACIONAMENTO ADICIONADO
            ]);
        } catch (\Exception $e) {
            return responseError([
                'message' => 'Erro ao atualizar fatura: ' . $e->getMessage(),
                'statusCode' => 500
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $fatura = FinanceiroReceber::findOrFail($id);

        try {
            // Se for fatura principal, cancelar todas as parcelas
            if ($fatura->fatura_principal_id === null && $fatura->parcelas_total > 1) {
                $fatura->parcelas()->update(['status' => FinanceiroReceber::STATUS_CANCELADO]);
            }
            
            $fatura->cancelar();

            return responseSuccess(['message' => 'Fatura cancelada com sucesso']);
        } catch (\Exception $e) {
            return responseError([
                'message' => 'Erro ao cancelar fatura: ' . $e->getMessage(),
                'statusCode' => 500
            ]);
        }
    }

    /**
     * Marcar fatura como paga
     */
    public function marcarComoPago(Request $request, string $id)
    {
        $validator = Validator::make($request->all(), [
            'data_pagamento' => 'nullable|date',
            'meio_pagamento' => 'nullable|string|max:100',
        ]);

        if ($validator->fails()) {
            return responseError([
                'message' => 'Dados inválidos',
                'data' => $validator->errors(),
                'statusCode' => 422
            ]);
        }

        $fatura = FinanceiroReceber::findOrFail($id);

        try {
            $fatura->marcarComoPago(
                $request->get('data_pagamento'),
                $request->get('meio_pagamento')
            );

            return responseSuccess([
                'message' => 'Fatura marcada como paga',
                'data' => $fatura->fresh(['paciente', 'dentista'])
            ]);
        } catch (\Exception $e) {
            return responseError([
                'message' => 'Erro ao marcar fatura como paga: ' . $e->getMessage(),
                'statusCode' => 500
            ]);
        }
    }

    /**
     * Obter faturas de um paciente específico
     */
    public function faturasPaciente(string $pacienteId)
    {
        $faturas = FinanceiroReceber::with(['dentista'])
            ->doPaciente($pacienteId)
            ->orderBy('data_vencimento', 'asc')
            ->orderBy('parcela_numero', 'asc')
            ->get();

        return responseSuccess(['data' => $faturas]);
    }

    /**
     * Obter estatísticas financeiras
     */
    public function estatisticas(Request $request)
    {
        try {
            $query = FinanceiroReceber::query();

            if ($request->has('paciente_id')) {
                $query->doPaciente($request->paciente_id);
            }

            $totalPendente = $query->clone()->pendentes()->sum('valor_final') ?: 0;
            $totalVencido = $query->clone()->vencidas()->sum('valor_final') ?: 0;
            $totalPago = $query->clone()->pagas()->sum('valor_final') ?: 0;
            $totalGeral = $query->clone()->sum('valor_final') ?: 0;

            $estatisticas = [
                'total_pendente' => $totalPendente,
                'total_vencido' => $totalVencido,
                'total_pago' => $totalPago,
                'total_geral' => $totalGeral,
                'quantidade_pendente' => $query->clone()->pendentes()->count(),
                'quantidade_vencida' => $query->clone()->vencidas()->count(),
                'quantidade_paga' => $query->clone()->pagas()->count(),
            ];

            return responseSuccess(['data' => $estatisticas]);
        } catch (\Exception $e) {
            return responseError([
                'message' => 'Erro ao obter estatísticas: ' . $e->getMessage(),
                'statusCode' => 500
            ]);
        }
    }
}
