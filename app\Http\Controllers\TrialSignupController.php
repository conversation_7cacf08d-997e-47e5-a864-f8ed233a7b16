<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use App\Models\Clinica;
use App\Models\User;
use App\Models\Dentista;
use App\Models\Plano;
use App\Models\Assinatura;
use App\Models\MatriculaCounter;
use Tymon\JWTAuth\Facades\JWTAuth;

class TrialSignupController extends Controller
{
    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Validação dos dados
        $validator = Validator::make($request->all(), [
            'plano_id' => 'required|exists:planos,id',
            'clinica' => 'required|array',
            'clinica.nome' => 'required|string|max:255',
            'dentista' => 'required|array',
            'dentista.nome' => 'required|string|max:255',
            'dentista.email' => 'required|email|unique:users,email',
            'dentista.senha' => 'required|string|min:8',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Dados inválidos',
                'errors' => $validator->errors()
            ], 422);
        }

        // Verificar se o plano permite auto-registro
        $plano = Plano::where('id', $request->plano_id)
                     ->where('auto_registro', true)
                     ->where('ativo', true)
                     ->first();

        if (!$plano) {
            return response()->json([
                'success' => false,
                'message' => 'Plano não disponível para auto-registro'
            ], 400);
        }

        DB::beginTransaction();

        try {
            // Gerar slug único baseado no nome
            $baseSlug = Str::slug(mb_strtolower(preg_replace('/[\s]/', '-', $request->clinica['nome'])), '-');
            $slug = $this->generateUniqueSlug($baseSlug);

            // 1. Criar a clínica
            $clinica = Clinica::create([
                'nome' => $request->clinica['nome'],
                'slug' => $slug,
                'plano_id' => $plano->id,
                'ativo' => true,
            ]);

            // 2. Criar o usuário
            $user = User::create([
                'name' => $request->dentista['nome'],
                'username' => $request->dentista['email'], // Usar email como username
                'email' => $request->dentista['email'],
                'password' => Hash::make($request->dentista['senha']),
                'clinica_id' => $clinica->id,
                'clinica_admin' => true, // Usuário trial é admin da clínica
                'user_type' => 'profissional',
                'is_active' => true,
            ]);

            // 3. Criar o dentista
            $dentista = Dentista::create([
                'nome' => $request->dentista['nome'],
                'email' => $request->dentista['email'],
                'clinica_id' => $clinica->id,
                'user_id' => $user->id,
                'id_matricula' => MatriculaCounter::getNextIdMatricula($clinica->id),
                'ativo' => true,
            ]);

            // 4. Criar assinatura trial
            $dataInicio = now();
            // Usar dias_gratuitos do plano, ou 15 dias como padrão
            $diasGratuitos = $plano->dias_gratuitos ?? 15;
            $dataFim = $dataInicio->copy()->addDays($diasGratuitos);

            $assinatura = Assinatura::create([
                'clinica_id' => $clinica->id,
                'plano_id' => $plano->id,
                'status' => 'ativo',
                'data_inicio' => $dataInicio,
                'data_fim' => $dataFim,
                'valor_mensal' => 0, // Gratuito
                'trial' => true,
            ]);

            // 5. Gerar JWT token com claims customizados
            $token = $this->generateTokenWithClaims($user);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Conta de teste criada com sucesso',
                'data' => [
                    'token' => $token,
                    'user' => $user,
                    'clinica' => $clinica,
                    'dentista' => $dentista,
                    'assinatura' => $assinatura,
                    'trial_ends_at' => $dataFim->toISOString(),
                ]
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Erro ao criar conta de teste: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    /**
     * Generate a JWT token with custom claims for a user
     *
     * @param User $user
     * @return string
     */
    private function generateTokenWithClaims($user)
    {
        $clinica = Clinica::find($user->clinica_id);

        $claims = [
            'nome' => $user->name,
            'username' => $user->username,
            'email' => $user->email,
            'clinica' => $clinica,
            'system_admin' => $user->system_admin,
            'clinica_admin' => $user->clinica_admin,
            'language' => $user->language,
            'storageRoot' => 'clinicas/' . $clinica->id
        ];

        $dentista = Dentista::where('user_id', $user->id)->first();

        if ($dentista) {
            $claims['dentista'] = $dentista;
        }

        // Adicionar configurações da agenda
        try {
            $agendaConfig = \App\Models\AgendaConfig::getForUser($user->id);
            $claims['agenda_config'] = $agendaConfig->toFrontendFormat();
        } catch (\Exception $e) {
            // Se houver erro na criação da agenda config, usar configuração padrão
            $claims['agenda_config'] = \App\Models\AgendaConfig::getDefaultConfig();
        }

        return JWTAuth::claims($claims)->fromUser($user);
    }

    /**
     * Gera um slug único adicionando números se necessário
     */
    private function generateUniqueSlug($baseSlug)
    {
        $slug = $baseSlug;
        $counter = 2;

        // Verifica se o slug já existe
        while (Clinica::where('slug', $slug)->exists()) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }
}
