<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orcamento_itens', function (Blueprint $table) {
            $table->enum('status_aprovacao', ['pendente', 'aprovado', 'rejeitado'])->default('pendente')->after('dentes');
            $table->timestamp('aprovado_em')->nullable()->after('status_aprovacao');
            $table->string('aprovado_por')->nullable()->after('aprovado_em');
            $table->text('motivo_rejeicao')->nullable()->after('aprovado_por');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orcamento_itens', function (Blueprint $table) {
            $table->dropColumn(['status_aprovacao', 'aprovado_em', 'aprovado_por', 'motivo_rejeicao']);
        });
    }
};
