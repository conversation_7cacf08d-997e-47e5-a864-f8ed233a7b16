import axios from '@/services/axios';

/**
 * Service para gerenciar configurações da agenda
 */

/**
 * Obter configurações da agenda para um consultório específico
 */
async function getAgendaConfig(consultorioId) {
    try {
        if (!consultorioId) {
            throw new Error('ID do consultório é obrigatório');
        }

        console.log('🌐 Fazendo requisição para API /agenda-config com consultorio_id:', consultorioId);

        const response = await axios.get('/agenda-config', {
            params: { consultorio_id: consultorioId }
        });

        console.log('📡 Resposta da API /agenda-config:', response.data);

        if (response.data && response.data.status === 'success') {
            console.log('✅ Configuração obtida da API:', response.data.data);
            return response.data.data;
        }

        console.log('⚠️ API retornou status diferente de success:', response.data);
        return null;
    } catch (error) {
        console.error('❌ Erro ao obter configurações da agenda:', error);
        throw error;
    }
}

/**
 * Obter configurações da agenda para todos os consultórios
 * Carrega as configurações individualmente para cada consultório
 */
async function getAllAgendaConfigs(consultorioIds = []) {
    try {
        console.log('🌐 Carregando configurações para consultórios:', consultorioIds);

        if (!consultorioIds || consultorioIds.length === 0) {
            console.warn('⚠️ Nenhum consultório fornecido para carregar configurações');
            return {};
        }

        const configs = {};
        const promises = consultorioIds.map(async (consultorioId) => {
            try {
                const config = await getAgendaConfig(consultorioId);
                if (config) {
                    configs[consultorioId] = config;
                }
            } catch (error) {
                console.warn(`⚠️ Erro ao carregar config para consultório ${consultorioId}:`, error);
            }
        });

        await Promise.all(promises);

        console.log(`✅ Configurações carregadas para ${Object.keys(configs).length}/${consultorioIds.length} consultórios`);
        return configs;
    } catch (error) {
        console.error('❌ Erro ao obter configurações da agenda para todos os consultórios:', error);
        throw error;
    }
}

/**
 * Atualizar configurações da agenda para um consultório específico
 */
async function updateAgendaConfig(consultorioId, configData) {
    try {
        if (!consultorioId) {
            throw new Error('ID do consultório é obrigatório');
        }

        const dataToSend = {
            ...configData,
            consultorio_id: consultorioId
        };

        const response = await axios.patch('/agenda-config', dataToSend);

        if (response.data && response.data.status === 'success') {
            return response.data.data;
        }

        return null;
    } catch (error) {
        console.error('Erro ao atualizar configurações da agenda:', error);
        throw error;
    }
}

/**
 * Restaurar configurações padrão para um consultório específico
 */
async function resetAgendaConfig(consultorioId) {
    try {
        if (!consultorioId) {
            throw new Error('ID do consultório é obrigatório');
        }

        const response = await axios.post('/agenda-config/reset', {
            consultorio_id: consultorioId
        });

        if (response.data && response.data.status === 'success') {
            return response.data.data;
        }

        return null;
    } catch (error) {
        console.error('Erro ao restaurar configurações da agenda:', error);
        throw error;
    }
}

/**
 * Obter horários disponíveis baseados na configuração de um consultório específico
 */
async function getTimeSlots(consultorioId) {
    try {
        if (!consultorioId) {
            throw new Error('ID do consultório é obrigatório');
        }

        const response = await axios.get('/agenda-config/time-slots', {
            params: { consultorio_id: consultorioId }
        });

        if (response.data && response.data.status === 'success') {
            return response.data.data;
        }

        return null;
    } catch (error) {
        console.error('Erro ao obter horários disponíveis:', error);
        throw error;
    }
}

/**
 * Verificar se um horário está disponível
 */
function isTimeSlotAvailable(timeSlot, config) {
    if (!config || !config.time_slots) {
        return false;
    }
    
    return config.time_slots.includes(timeSlot);
}

/**
 * Verificar se um dia da semana está ativo
 */
function isDayActive(dayOfWeek, config) {
    if (!config || !config.dias_semana) {
        return false;
    }
    
    return config.dias_semana.includes(dayOfWeek);
}

/**
 * Converter dia da semana de número para string
 */
function dayNumberToString(dayNumber) {
    const days = {
        0: 'domingo',
        1: 'segunda',
        2: 'terca',
        3: 'quarta',
        4: 'quinta',
        5: 'sexta',
        6: 'sabado'
    };
    
    return days[dayNumber] || null;
}

/**
 * Verificar se está dentro do horário de funcionamento
 */
function isWithinWorkingHours(time, config) {
    if (!config) {
        return false;
    }
    
    const [hour, minute] = time.split(':').map(Number);
    const timeMinutes = hour * 60 + minute;
    
    const [startHour, startMinute] = config.horario_inicio.split(':').map(Number);
    const startMinutes = startHour * 60 + startMinute;
    
    const [endHour, endMinute] = config.horario_fim.split(':').map(Number);
    const endMinutes = endHour * 60 + endMinute;
    
    if (timeMinutes < startMinutes || timeMinutes >= endMinutes) {
        return false;
    }
    
    // Verificar horário de almoço
    if (config.tem_horario_almoco && config.horario_almoco_inicio && config.horario_almoco_fim) {
        const [lunchStartHour, lunchStartMinute] = config.horario_almoco_inicio.split(':').map(Number);
        const lunchStartMinutes = lunchStartHour * 60 + lunchStartMinute;
        
        const [lunchEndHour, lunchEndMinute] = config.horario_almoco_fim.split(':').map(Number);
        const lunchEndMinutes = lunchEndHour * 60 + lunchEndMinute;
        
        if (timeMinutes >= lunchStartMinutes && timeMinutes < lunchEndMinutes) {
            return false;
        }
    }
    
    return true;
}

/**
 * Gerar horários disponíveis baseados na configuração
 */
function generateTimeSlots(config) {
    if (!config) {
        return [];
    }
    
    const slots = [];
    const [startHour, startMinute] = config.horario_inicio.split(':').map(Number);
    const [endHour, endMinute] = config.horario_fim.split(':').map(Number);
    
    const start = new Date();
    start.setHours(startHour, startMinute, 0, 0);
    
    const end = new Date();
    end.setHours(endHour, endMinute, 0, 0);
    
    const current = new Date(start);
    
    while (current < end) {
        const timeString = current.toTimeString().substring(0, 5);
        
        // Verificar se não está no horário de almoço
        if (isWithinWorkingHours(timeString, config)) {
            slots.push(timeString);
        }
        
        // Adicionar duração da consulta + intervalo
        current.setTime(current.getTime() + (config.duracao_padrao_consulta + config.intervalo_entre_consultas) * 60000);
    }
    
    return slots;
}

/**
 * Validar configurações antes de salvar
 */
function validateConfig(config) {
    const errors = {};

    // Validar dias da semana
    if (!config.dias_semana || config.dias_semana.length === 0) {
        errors.dias_semana = 'Pelo menos um dia da semana deve estar ativo';
    }

    // Validar duração padrão da consulta
    if (!config.duracao_padrao_consulta || config.duracao_padrao_consulta < 15) {
        errors.duracao_padrao_consulta = 'Duração mínima da consulta é 15 minutos';
    }

    // Validar configurações por dia
    if (config.configuracoes_por_dia && typeof config.configuracoes_por_dia === 'object') {
        Object.keys(config.configuracoes_por_dia).forEach(dia => {
            const dayConfig = config.configuracoes_por_dia[dia];

            if (!dayConfig.horario_inicio) {
                errors[`${dia}_horario_inicio`] = `Horário de início é obrigatório para ${dia}`;
            }

            if (!dayConfig.horario_fim) {
                errors[`${dia}_horario_fim`] = `Horário de fim é obrigatório para ${dia}`;
            }

            if (dayConfig.horario_inicio && dayConfig.horario_fim) {
                const [startHour, startMinute] = dayConfig.horario_inicio.split(':').map(Number);
                const [endHour, endMinute] = dayConfig.horario_fim.split(':').map(Number);

                const startMinutes = startHour * 60 + startMinute;
                const endMinutes = endHour * 60 + endMinute;

                if (startMinutes >= endMinutes) {
                    errors[`${dia}_horario_fim`] = `Horário de fim deve ser posterior ao horário de início para ${dia}`;
                }
            }

            // Validar horário de almoço se habilitado
            if (dayConfig.tem_horario_almoco) {
                if (!dayConfig.horario_almoco_inicio) {
                    errors[`${dia}_horario_almoco_inicio`] = `Horário de início do almoço é obrigatório para ${dia}`;
                }

                if (!dayConfig.horario_almoco_fim) {
                    errors[`${dia}_horario_almoco_fim`] = `Horário de fim do almoço é obrigatório para ${dia}`;
                }

                if (dayConfig.horario_almoco_inicio && dayConfig.horario_almoco_fim) {
                    const [lunchStartHour, lunchStartMinute] = dayConfig.horario_almoco_inicio.split(':').map(Number);
                    const [lunchEndHour, lunchEndMinute] = dayConfig.horario_almoco_fim.split(':').map(Number);

                    const lunchStartMinutes = lunchStartHour * 60 + lunchStartMinute;
                    const lunchEndMinutes = lunchEndHour * 60 + lunchEndMinute;

                    if (lunchStartMinutes >= lunchEndMinutes) {
                        errors[`${dia}_horario_almoco_fim`] = `Horário de fim do almoço deve ser posterior ao horário de início para ${dia}`;
                    }

                    // Verificar se o horário do almoço está dentro do horário de funcionamento
                    if (dayConfig.horario_inicio && dayConfig.horario_fim) {
                        const [startHour, startMinute] = dayConfig.horario_inicio.split(':').map(Number);
                        const [endHour, endMinute] = dayConfig.horario_fim.split(':').map(Number);

                        const startMinutes = startHour * 60 + startMinute;
                        const endMinutes = endHour * 60 + endMinute;

                        if (lunchStartMinutes < startMinutes || lunchEndMinutes > endMinutes) {
                            errors[`${dia}_horario_almoco_inicio`] = `Horário do almoço deve estar dentro do horário de funcionamento para ${dia}`;
                        }
                    }
                }
            }
        });
    }

    return {
        isValid: Object.keys(errors).length === 0,
        errors
    };
}

export default {
    getAgendaConfig,
    getAllAgendaConfigs,
    updateAgendaConfig,
    resetAgendaConfig,
    getTimeSlots,
    isTimeSlotAvailable,
    isDayActive,
    dayNumberToString,
    isWithinWorkingHours,
    generateTimeSlots,
    validateConfig
};
