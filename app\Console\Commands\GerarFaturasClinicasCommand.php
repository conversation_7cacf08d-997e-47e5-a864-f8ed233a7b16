<?php

namespace App\Console\Commands;

use App\Jobs\GerarFaturasClinicasJob;
use Illuminate\Console\Command;

class GerarFaturasClinicasCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'faturas:gerar-clinicas {--force : Forçar geração mesmo que não seja o dia de cobrança}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Gerar faturas mensais para clínicas baseadas nas assinaturas ativas';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Iniciando geração de faturas de clínicas...');

        try {
            GerarFaturasClinicasJob::dispatch();
            $this->info('Job de geração de faturas despachado com sucesso!');
        } catch (\Exception $e) {
            $this->error('Erro ao despachar job: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
