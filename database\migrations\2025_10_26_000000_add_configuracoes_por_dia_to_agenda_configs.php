<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('agenda_configs', function (Blueprint $table) {
            // Adicionar campo JSON para configurações específicas por dia da semana
            $table->json('configuracoes_por_dia')->nullable()->after('dias_semana');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('agenda_configs', function (Blueprint $table) {
            $table->dropColumn('configuracoes_por_dia');
        });
    }
};
